using System.Windows;
using System.Windows.Threading;
using Microsoft.Web.WebView2.Core;
using YouTubePlayerApp.Core;

namespace YouTubePlayerApp;

/// <summary>
/// النافذة الرئيسية المحسنة لتطبيق YouTube Desktop Player
/// تم تحسينها لتقليل التكرار واستخدام الفئات المساعدة الجديدة
/// </summary>
public partial class MainWindow : Window
{
    #region Private Fields

    private bool _isFullScreen = false;
    private WindowState _previousWindowState;
    private WindowStyle _previousWindowStyle;
    
    // الفئات المحسنة الموحدة
    private UnifiedAdBlocker? _unifiedAdBlocker;
    private VideoQualityController? _qualityController;
    private YouTubeEnhancer? _youtubeEnhancer;
    
    private readonly DispatcherTimer _statsUpdateTimer;
    private readonly DateTime _applicationStartTime;

    #endregion

    #region Constructor & Initialization

    public MainWindow()
    {
        _applicationStartTime = DateTime.Now;
        InitializeComponent();
        
        // تهيئة المؤقتات والمراقبة
        InitializeTimersAndMonitoring();
        
        // تهيئة WebView2 بشكل غير متزامن
        _ = InitializeWebViewAsync();
        
        AppLogger.LogInfo("تم بدء تشغيل التطبيق");
    }

    /// <summary>
    /// تهيئة المؤقتات ومراقبة الأداء
    /// </summary>
    private void InitializeTimersAndMonitoring()
    {
        // إعداد مؤقت تحديث الإحصائيات
        _statsUpdateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(3)
        };
        _statsUpdateTimer.Tick += UpdateStatsDisplay;
        _statsUpdateTimer.Start();
        
        // تهيئة مراقبة الأداء
        PerformanceMonitor.Instance.LogCurrentState("Application Start");
    }

    /// <summary>
    /// تهيئة WebView2 مع استخدام المساعدات الجديدة
    /// </summary>
    private async Task InitializeWebViewAsync()
    {
        try
        {
            PerformanceMonitor.Instance.StartOperation("WebView.Initialize");
            
            // انتظار تهيئة WebView2
            await YouTubeWebView.EnsureCoreWebView2Async();
            
            // تكوين الإعدادات الأساسية باستخدام المساعد
            WebViewHelper.ConfigureBasicSettings(YouTubeWebView.CoreWebView2);
            
            // تكوين الأذونات
            WebViewHelper.ConfigurePermissions(YouTubeWebView.CoreWebView2);
            
            // تطبيق تحسينات YouTube
            await WebViewHelper.ApplyYouTubeOptimizationsAsync(YouTubeWebView.CoreWebView2);
            
            // حقن CSS لحجب الإعلانات
            await WebViewHelper.InjectAdBlockingCssAsync(YouTubeWebView.CoreWebView2);
            
            // حقن سكريبت تفاعل الفيديو
            await WebViewHelper.InjectVideoInteractionScriptAsync(YouTubeWebView.CoreWebView2);
            
            // تهيئة الفئات المحسنة
            await InitializeEnhancedComponentsAsync();
            
            // تسجيل أحداث التنقل
            YouTubeWebView.CoreWebView2.NavigationCompleted += OnNavigationCompleted;
            
            AppLogger.LogInfo("تم تهيئة WebView2 بنجاح");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تهيئة WebView2: {ex.Message}");
            WindowHelper.ShowErrorDialog("فشل في تهيئة متصفح الويب", "خطأ في التهيئة", this, ex);
        }
        finally
        {
            PerformanceMonitor.Instance.EndOperation("WebView.Initialize");
        }
    }

    /// <summary>
    /// تهيئة المكونات المحسنة
    /// </summary>
    private async Task InitializeEnhancedComponentsAsync()
    {
        try
        {
            // تهيئة حاجب الإعلانات الموحد
            _unifiedAdBlocker = new UnifiedAdBlocker();
            await _unifiedAdBlocker.InitializeAsync(YouTubeWebView.CoreWebView2);
            
            // تهيئة مراقب جودة الفيديو
            _qualityController = new VideoQualityController();
            _qualityController.Initialize(YouTubeWebView.CoreWebView2);
            
            // تهيئة محسن YouTube
            _youtubeEnhancer = new YouTubeEnhancer();
            _youtubeEnhancer.Initialize(YouTubeWebView.CoreWebView2);
            
            AppLogger.LogInfo("تم تهيئة جميع المكونات المحسنة");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تهيئة المكونات المحسنة: {ex.Message}");
        }
    }

    #endregion

    #region Event Handlers

    /// <summary>
    /// معالج اكتمال التنقل
    /// </summary>
    private void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (e.IsSuccess)
        {
            // تحديث عنوان النافذة باستخدام المساعد
            WebViewHelper.UpdateWindowTitle(YouTubeWebView.CoreWebView2, this);
            
            // تحديث حالة حجب الإعلانات
            UpdateAdBlockStatus();
        }
    }

    /// <summary>
    /// تحديث عرض الإحصائيات
    /// </summary>
    private void UpdateStatsDisplay(object? sender, EventArgs e)
    {
        try
        {
            if (AppSettings.ShowAdBlockStats && AppSettings.BlockAds && _unifiedAdBlocker != null)
            {
                var totalBlocked = _unifiedAdBlocker.GetTotalBlocked();
                var stats = _unifiedAdBlocker.GetBlockedStats();
                
                SessionTotalText.Text = $"الجلسة: {totalBlocked}";
                BlockedRequestsText.Text = $"الطلبات: {stats.GetValueOrDefault("Total", 0)}";
                AdBlockStatusIndicator.Visibility = Visibility.Visible;
            }
            else
            {
                AdBlockStatusIndicator.Visibility = Visibility.Collapsed;
            }
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تحديث الإحصائيات: {ex.Message}");
        }
    }

    /// <summary>
    /// تحديث حالة حجب الإعلانات
    /// </summary>
    private void UpdateAdBlockStatus()
    {
        // يتم التعامل مع هذا في UpdateStatsDisplay الآن
        // الاحتفاظ بالطريقة للتوافق مع الإصدارات السابقة
    }

    #endregion

    #region Navigation Menu Handlers

    private void Back_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2?.CanGoBack == true)
            YouTubeWebView.CoreWebView2.GoBack();
    }

    private void Forward_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2?.CanGoForward == true)
            YouTubeWebView.CoreWebView2.GoForward();
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2?.Reload();
    }

    private void Home_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2?.Navigate("https://www.youtube.com");
    }

    #endregion

    #region View Menu Handlers

    private void FullScreen_Click(object sender, RoutedEventArgs e)
    {
        ToggleFullScreen();
    }

    private void ZoomIn_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor += 0.1;
    }

    private void ZoomOut_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = Math.Max(0.1, YouTubeWebView.ZoomFactor - 0.1);
    }

    private void ResetZoom_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = 1.0;
    }

    #endregion

    #region Tools Menu Handlers

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        var settingsWindow = new SettingsWindow();
        settingsWindow.Owner = this;
        settingsWindow.ShowDialog();
    }

    private void Performance_Click(object sender, RoutedEventArgs e)
    {
        var performanceWindow = new PerformanceBenchmarkWindow();
        performanceWindow.Owner = this;
        performanceWindow.Show();
    }

    private void MemoryProfiler_Click(object sender, RoutedEventArgs e)
    {
        var memoryProfilerWindow = new MemoryProfilerWindow();
        memoryProfilerWindow.Owner = this;
        memoryProfilerWindow.Show();
    }

    private void LogViewer_Click(object sender, RoutedEventArgs e)
    {
        var logViewerWindow = new LogViewerWindow();
        logViewerWindow.Owner = this;
        logViewerWindow.Show();
    }

    #endregion

    #region Diagnostics Menu Handlers (Enhanced)

    /// <summary>
    /// تشخيص حجب الإعلانات باستخدام المساعدات الجديدة
    /// </summary>
    private async void DiagnoseAdBlocking_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var diagnostics = await AdBlockDiagnostics.RunComprehensiveDiagnosticsAsync(YouTubeWebView.CoreWebView2);
            var window = WindowHelper.CreateDiagnosticsWindow("تشخيص حجب الإعلانات", diagnostics, this);
            window.ShowDialog();
        }, "تشخيص حجب الإعلانات", false);
    }

    /// <summary>
    /// إجبار إزالة الإعلانات
    /// </summary>
    private async void ForceAdRemoval_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var result = await AdBlockDiagnostics.ForceAdRemovalAsync(YouTubeWebView.CoreWebView2);
            WindowHelper.ShowInfoDialog(result, "إجبار إزالة الإعلانات", this);
        }, "إجبار إزالة الإعلانات");
    }

    /// <summary>
    /// اختبار فعالية حجب الإعلانات
    /// </summary>
    private async void TestAdBlocking_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var isEffective = await AdBlockDiagnostics.TestAdBlockingEffectivenessAsync(YouTubeWebView.CoreWebView2);
            var message = isEffective 
                ? "✅ حجب الإعلانات يعمل بفعالية! لم يتم اكتشاف أي إعلانات."
                : "⚠️ قد لا يكون حجب الإعلانات فعالاً بالكامل. تم اكتشاف بعض الإعلانات.";
            
            if (isEffective)
                WindowHelper.ShowInfoDialog(message, "اختبار حجب الإعلانات", this);
            else
                WindowHelper.ShowWarningDialog(message, "اختبار حجب الإعلانات", this);
        }, "اختبار حجب الإعلانات", false);
    }

    /// <summary>
    /// تشخيص تشغيل الفيديو
    /// </summary>
    private async void DiagnoseVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var diagnostics = await VideoPlaybackDiagnostics.RunVideoPlaybackTestAsync(YouTubeWebView.CoreWebView2);
            var window = WindowHelper.CreateDiagnosticsWindow("تشخيص تشغيل الفيديو", diagnostics, this, 1000, 800);
            window.ShowDialog();
        }, "تشخيص تشغيل الفيديو", false);
    }

    /// <summary>
    /// إصلاح مشاكل تشغيل الفيديو
    /// </summary>
    private async void FixVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var result = await VideoPlaybackDiagnostics.FixVideoPlaybackIssuesAsync(YouTubeWebView.CoreWebView2);
            WindowHelper.ShowInfoDialog(result, "إصلاح تشغيل الفيديو", this);
        }, "إصلاح تشغيل الفيديو");
    }

    /// <summary>
    /// اختبار وظائف تشغيل الفيديو
    /// </summary>
    private async void TestVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var isWorking = await VideoPlaybackDiagnostics.TestVideoPlaybackFunctionalityAsync(YouTubeWebView.CoreWebView2);
            var message = isWorking 
                ? "✅ تشغيل الفيديو يعمل بشكل مثالي! جميع عناصر التحكم تعمل بشكل صحيح."
                : "⚠️ تم اكتشاف مشاكل في تشغيل الفيديو. قد تكون بعض الوظائف معطلة.";
            
            if (isWorking)
                WindowHelper.ShowInfoDialog(message, "اختبار تشغيل الفيديو", this);
            else
                WindowHelper.ShowWarningDialog(message, "اختبار تشغيل الفيديو", this);
        }, "اختبار تشغيل الفيديو", false);
    }

    /// <summary>
    /// تشغيل مجموعة الاختبارات الشاملة
    /// </summary>
    private async void RunFullTestSuite_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var testResults = await ComprehensiveTestSuite.RunFullTestSuiteAsync(YouTubeWebView.CoreWebView2);
            var window = WindowHelper.CreateDiagnosticsWindow("نتائج مجموعة الاختبارات الشاملة", testResults, this, 1100, 900);
            window.ShowDialog();
        }, "مجموعة الاختبارات الشاملة", false);
    }

    /// <summary>
    /// فحص سريع للحالة الصحية
    /// </summary>
    private async void QuickHealthCheck_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var healthCheck = await ComprehensiveTestSuite.RunQuickHealthCheckAsync(YouTubeWebView.CoreWebView2);
            WindowHelper.ShowInfoDialog(healthCheck, "فحص سريع للحالة", this);
        }, "فحص سريع للحالة");
    }

    /// <summary>
    /// تشخيص تفاعل الفيديو
    /// </summary>
    private async void DiagnoseVideo_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var diagnostics = await VideoInteractionDiagnostics.DiagnoseVideoInteractionAsync(YouTubeWebView.CoreWebView2);
            var window = WindowHelper.CreateDiagnosticsWindow("تشخيص تفاعل الفيديو", diagnostics, this, 800, 600);
            window.ShowDialog();
        }, "تشخيص تفاعل الفيديو", false);
    }

    /// <summary>
    /// إصلاح تفاعل الفيديو
    /// </summary>
    private async void FixVideoInteraction_Click(object sender, RoutedEventArgs e)
    {
        await DiagnosticsHelper.ExecuteDiagnosticOperationAsync(async () =>
        {
            var result = await VideoInteractionDiagnostics.AttemptVideoInteractionFixAsync(YouTubeWebView.CoreWebView2);
            
            var message = result 
                ? "تم تطبيق إصلاحات تفاعل الفيديو بنجاح!\n\nجرب النقر على الفيديوهات الآن."
                : "لم يتم تطبيق أي إصلاحات. قد يكون تفاعل الفيديو يعمل بشكل صحيح بالفعل.";
            
            WindowHelper.ShowInfoDialog(message, "إصلاح تفاعل الفيديو", this);
        }, "إصلاح تفاعل الفيديو", false);
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// تبديل وضع ملء الشاشة
    /// </summary>
    private void ToggleFullScreen()
    {
        if (_isFullScreen)
        {
            // الخروج من ملء الشاشة
            WindowState = _previousWindowState;
            WindowStyle = _previousWindowStyle;
            _isFullScreen = false;
        }
        else
        {
            // الدخول في ملء الشاشة
            _previousWindowState = WindowState;
            _previousWindowStyle = WindowStyle;
            WindowStyle = WindowStyle.None;
            WindowState = WindowState.Maximized;
            _isFullScreen = true;
        }
    }

    #endregion

    #region Cleanup & Disposal

    protected override void OnClosed(EventArgs e)
    {
        try
        {
            // حفظ حالة النافذة
            WindowHelper.SaveWindowState(this, "MainWindow");
            
            // التخلص من الموارد لمنع تسريب الذاكرة
            _unifiedAdBlocker?.Dispose();
            _qualityController?.Dispose();
            _youtubeEnhancer?.Dispose();
            PerformanceMonitor.Instance.Dispose();
            SettingsCache.Instance.Dispose();
            
            PerformanceMonitor.Instance.LogCurrentState("Application Exit");
            AppLogger.LogInfo("تم إغلاق التطبيق بنجاح");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ أثناء التنظيف: {ex.Message}");
        }
        
        // إيقاف المؤقتات وتنظيف الموارد
        _statsUpdateTimer?.Stop();
        
        base.OnClosed(e);
    }

    #endregion
}