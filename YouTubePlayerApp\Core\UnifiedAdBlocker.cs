using Microsoft.Web.WebView2.Core;
using System.Collections.Concurrent;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp.Core;

/// <summary>
/// حاجب إعلانات موحد ومحسن يجمع أفضل ميزات جميع حاجبات الإعلانات السابقة
/// مع ضمان عدم تأثيره على وظائف تشغيل الفيديو
/// </summary>
public sealed class UnifiedAdBlocker : IDisposable
{
    #region Private Fields

    private readonly HashSet<string> _blockedDomains;
    private readonly CompiledRegex[] _urlPatterns;
    private readonly ConcurrentDictionary<string, int> _blockedStats;
    private readonly SettingsCache _settingsCache;
    private readonly Timer _protectionTimer;
    private readonly Timer _statsTimer;
    
    private CoreWebView2? _webView;
    private bool _disposed;
    private bool _isInitialized;

    #endregion

    #region Nested Types

    /// <summary>
    /// نمط Regex مُجمع لتحسين الأداء
    /// </summary>
    private readonly struct CompiledRegex
    {
        public readonly Regex Pattern;
        public readonly string Description;

        public CompiledRegex(string pattern, string description)
        {
            Pattern = new Regex(pattern, RegexOptions.Compiled | RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
            Description = description;
        }
    }

    #endregion

    #region Constructor & Initialization

    public UnifiedAdBlocker()
    {
        _settingsCache = SettingsCache.Instance;
        _blockedDomains = LoadBlockedDomains();
        _urlPatterns = LoadUrlPatterns();
        _blockedStats = new ConcurrentDictionary<string, int>();
        
        // مؤقت حماية مستمر للفيديو
        _protectionTimer = new Timer(ProtectVideoPlayback, null, 
            TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(5));
        
        // مؤقت تحديث الإحصائيات
        _statsTimer = new Timer(UpdateStats, null, 
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(3));

        AppLogger.LogInfo("تم إنشاء حاجب الإعلانات الموحد");
    }

    /// <summary>
    /// تهيئة حاجب الإعلانات مع WebView2
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    public async Task InitializeAsync(CoreWebView2 webView)
    {
        if (_disposed || webView == null) return;

        try
        {
            _webView = webView;
            
            if (AppSettings.BlockAds)
            {
                await EnableAdBlockingAsync();
            }

            _isInitialized = true;
            AppLogger.LogInfo("تم تهيئة حاجب الإعلانات الموحد بنجاح");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تهيئة حاجب الإعلانات: {ex.Message}");
        }
    }

    #endregion

    #region Ad Blocking Core

    /// <summary>
    /// تمكين حجب الإعلانات
    /// </summary>
    private async Task EnableAdBlockingAsync()
    {
        if (_webView == null) return;

        try
        {
            // تسجيل أحداث الشبكة
            _webView.DOMContentLoaded += OnDOMContentLoaded;
            _webView.NavigationCompleted += OnNavigationCompleted;
            
            // حجب الطلبات على مستوى الشبكة
            await _webView.AddWebResourceRequestedFilterAsync("*", CoreWebView2WebResourceContext.All);
            _webView.WebResourceRequested += OnWebResourceRequested;

            // حقن CSS وJavaScript لحجب الإعلانات
            await InjectAdBlockingScriptsAsync();

            AppLogger.LogInfo("تم تمكين حجب الإعلانات");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تمكين حجب الإعلانات: {ex.Message}");
        }
    }

    /// <summary>
    /// حقن سكريبتات حجب الإعلانات
    /// </summary>
    private async Task InjectAdBlockingScriptsAsync()
    {
        if (_webView == null) return;

        try
        {
            // حقن CSS لحجب الإعلانات
            await _webView.AddScriptToExecuteOnDocumentCreatedAsync(@"
                (function() {
                    'use strict';
                    
                    // CSS شامل لحجب الإعلانات
                    const adBlockCSS = `
                        /* إعلانات الفيديو - أولوية عالية */
                        .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
                        .ytp-ad-image-overlay, .ytp-ad-text-overlay,
                        .ytp-ad-player-overlay, .ytp-ad-skip-button-container,
                        .ytp-ad-skip-button, .ytp-ad-text, .ytp-ad-preview-container,
                        
                        /* إعلانات البانر والعرض */
                        .ytd-promoted-video-renderer, .ytd-promoted-sparkles-web-renderer,
                        .ytd-banner-promo-renderer, .ytd-in-feed-ad-layout-renderer,
                        .ytd-display-ad-renderer, .ytd-companion-slot-renderer,
                        .ytd-ad-slot-renderer, .ytd-promoted-video-renderer,
                        
                        /* إعلانات الشريط الجانبي والمصاحبة */
                        #player-ads, #masthead-ad, #watch-sidebar-ads,
                        .ytd-action-companion-ad-renderer, .companion-ad-renderer,
                        
                        /* عروض Premium والترقية */
                        .ytd-popup-container[dialog][role='dialog']:has([class*='premium']),
                        .ytd-mealbar-promo-renderer, .ytd-premium-upsell-renderer,
                        .ytd-upsell-dialog-renderer, .ytd-background-promo-renderer,
                        .ytd-consent-bump-v2-lightbox, .ytd-consent-bump-v2-renderer,
                        
                        /* إعلانات التسوق والمنتجات */
                        .ytd-product-details-renderer, .ytd-merch-shelf-renderer,
                        .ytd-shopping-shelf-renderer, .ytd-product-shelf-renderer,
                        .ytd-shopping-carousel-renderer,
                        
                        /* أنماط الإعلانات العامة */
                        [class*='promoted'], [class*='sponsor'], [class*='advertisement'],
                        [id*='promoted'], [id*='sponsor'], [id*='advertisement'],
                        [data-is-ad='true'], [data-promoted='true'], [data-ad-status],
                        [aria-label*='Ad'], [aria-label*='Sponsored'],
                        
                        /* إعلانات YouTube Shorts */
                        .ytd-reel-video-renderer[is-ad], .ytd-shorts-video-renderer[is-ad],
                        
                        /* إعلانات البحث */
                        .ytd-search-pyv-renderer, .ytd-promoted-sparkles-text-search-renderer
                        
                        { 
                            display: none !important; 
                            visibility: hidden !important; 
                            opacity: 0 !important;
                            height: 0 !important;
                            width: 0 !important;
                            position: absolute !important;
                            left: -9999px !important;
                        }
                        
                        /* إخفاء حاويات الإعلانات الفارغة */
                        .ytd-rich-item-renderer:has(.ytd-display-ad-renderer),
                        .ytd-item-section-renderer:has(.ytd-promoted-video-renderer) {
                            display: none !important;
                        }
                    `;
                    
                    // إنشاء وحقن الـ CSS
                    const style = document.createElement('style');
                    style.id = 'unified-adblock-css';
                    style.textContent = adBlockCSS;
                    
                    // حقن فوري
                    (document.head || document.documentElement).appendChild(style);
                    
                    // مراقبة مستمرة لضمان بقاء الـ CSS
                    const observer = new MutationObserver(() => {
                        if (!document.getElementById('unified-adblock-css')) {
                            (document.head || document.documentElement).appendChild(style);
                        }
                    });
                    
                    observer.observe(document.documentElement, {
                        childList: true,
                        subtree: true
                    });
                    
                })();
            ");

            // حقن سكريبت حماية الفيديو
            await _webView.AddScriptToExecuteOnDocumentCreatedAsync(@"
                (function() {
                    'use strict';
                    
                    // حماية وظائف الفيديو من التداخل مع حجب الإعلانات
                    const protectVideoFunctions = () => {
                        // حماية مشغل YouTube
                        if (window.ytplayer && window.ytplayer.config) {
                            const originalConfig = window.ytplayer.config;
                            Object.defineProperty(window.ytplayer, 'config', {
                                get: () => originalConfig,
                                set: (value) => {
                                    // السماح بالتحديثات الضرورية فقط
                                    if (value && typeof value === 'object') {
                                        Object.assign(originalConfig, value);
                                    }
                                },
                                configurable: false
                            });
                        }
                        
                        // حماية أحداث الفيديو
                        const videoElements = document.querySelectorAll('video');
                        videoElements.forEach(video => {
                            if (!video.dataset.protected) {
                                video.dataset.protected = 'true';
                                
                                // حماية أحداث التشغيل
                                ['play', 'pause', 'seeking', 'seeked', 'timeupdate'].forEach(event => {
                                    video.addEventListener(event, (e) => {
                                        e.stopPropagation();
                                    }, { capture: true, passive: true });
                                });
                            }
                        });
                    };
                    
                    // تطبيق الحماية
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', protectVideoFunctions);
                    } else {
                        protectVideoFunctions();
                    }
                    
                    // مراقبة مستمرة
                    setInterval(protectVideoFunctions, 3000);
                    
                })();
            ");

            AppLogger.LogInfo("تم حقن سكريبتات حجب الإعلانات بنجاح");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في حقن سكريبتات حجب الإعلانات: {ex.Message}");
        }
    }

    #endregion

    #region Event Handlers

    /// <summary>
    /// معالج طلبات الموارد على الويب
    /// </summary>
    private void OnWebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        if (!AppSettings.BlockAds) return;

        try
        {
            var uri = e.Request.Uri;
            
            if (ShouldBlockRequest(uri))
            {
                // حجب الطلب
                e.Response = _webView?.Environment.CreateWebResourceResponse(
                    null, 200, "OK", "");
                
                // تحديث الإحصائيات
                UpdateBlockedStats(uri);
                
                AppLogger.LogDebug($"تم حجب الطلب: {uri}");
            }
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في معالجة طلب الموارد: {ex.Message}");
        }
    }

    /// <summary>
    /// معالج تحميل محتوى DOM
    /// </summary>
    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (!AppSettings.BlockAds || _webView == null) return;

        try
        {
            // تطبيق حجب إضافي بعد تحميل DOM
            await _webView.ExecuteScriptAsync(@"
                // إزالة الإعلانات الموجودة
                document.querySelectorAll('[class*=""ad""], [id*=""ad""], [class*=""promoted""]').forEach(el => {
                    if (el.closest('video') === null) {
                        el.style.display = 'none';
                    }
                });
            ");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في معالجة DOM: {ex.Message}");
        }
    }

    /// <summary>
    /// معالج اكتمال التنقل
    /// </summary>
    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (!e.IsSuccess || !AppSettings.BlockAds) return;

        try
        {
            // تطبيق حجب إضافي بعد التنقل
            await Task.Delay(1000); // انتظار تحميل المحتوى
            await ApplyPostNavigationBlockingAsync();
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في معالجة اكتمال التنقل: {ex.Message}");
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// تحديد ما إذا كان يجب حجب الطلب
    /// </summary>
    /// <param name="uri">عنوان URL للطلب</param>
    /// <returns>true إذا كان يجب حجب الطلب</returns>
    private bool ShouldBlockRequest(string uri)
    {
        if (string.IsNullOrEmpty(uri)) return false;

        try
        {
            var url = new Uri(uri);
            
            // فحص النطاقات المحجوبة
            if (_blockedDomains.Contains(url.Host))
                return true;

            // فحص الأنماط
            foreach (var pattern in _urlPatterns)
            {
                if (pattern.Pattern.IsMatch(uri))
                    return true;
            }

            return false;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// تحديث إحصائيات الحجب
    /// </summary>
    /// <param name="uri">عنوان URL المحجوب</param>
    private void UpdateBlockedStats(string uri)
    {
        try
        {
            var domain = new Uri(uri).Host;
            _blockedStats.AddOrUpdate(domain, 1, (key, value) => value + 1);
            _blockedStats.AddOrUpdate("Total", 1, (key, value) => value + 1);
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تحديث إحصائيات الحجب: {ex.Message}");
        }
    }

    /// <summary>
    /// تطبيق حجب إضافي بعد التنقل
    /// </summary>
    private async Task ApplyPostNavigationBlockingAsync()
    {
        if (_webView == null) return;

        try
        {
            await _webView.ExecuteScriptAsync(@"
                // إزالة الإعلانات المتأخرة
                setTimeout(() => {
                    document.querySelectorAll('.ytd-promoted-video-renderer, .ytd-display-ad-renderer').forEach(el => {
                        el.remove();
                    });
                }, 2000);
            ");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تطبيق الحجب بعد التنقل: {ex.Message}");
        }
    }

    /// <summary>
    /// حماية تشغيل الفيديو (يتم استدعاؤها بواسطة المؤقت)
    /// </summary>
    private async void ProtectVideoPlayback(object? state)
    {
        if (_webView == null || !_isInitialized) return;

        try
        {
            await _webView.ExecuteScriptAsync(@"
                // التأكد من عمل مشغل الفيديو
                const video = document.querySelector('video');
                if (video && video.paused && video.currentTime > 0) {
                    // إعادة تشغيل إذا توقف بشكل غير متوقع
                    video.play().catch(() => {});
                }
            ");
        }
        catch
        {
            // تجاهل الأخطاء في الحماية المستمرة
        }
    }

    /// <summary>
    /// تحديث الإحصائيات (يتم استدعاؤها بواسطة المؤقت)
    /// </summary>
    private void UpdateStats(object? state)
    {
        try
        {
            // تحديث إحصائيات الأداء
            PerformanceMonitor.Instance.LogCurrentState("AdBlocker.Stats");
        }
        catch
        {
            // تجاهل أخطاء تحديث الإحصائيات
        }
    }

    /// <summary>
    /// تحميل النطاقات المحجوبة
    /// </summary>
    private HashSet<string> LoadBlockedDomains()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // نطاقات الإعلانات الرئيسية
            "googleads.g.doubleclick.net",
            "googlesyndication.com",
            "googleadservices.com",
            "google-analytics.com",
            "googletagmanager.com",
            "googletagservices.com",
            "adsystem.google.com",
            "tpc.googlesyndication.com",
            "pagead2.googlesyndication.com",
            
            // نطاقات إعلانات YouTube
            "ads.youtube.com",
            "youtube.com/ads",
            "youtube.com/api/stats/ads",
            "s.youtube.com/api/stats/ads",
            
            // نطاقات التتبع
            "facebook.com/tr",
            "connect.facebook.net",
            "analytics.google.com",
            "stats.g.doubleclick.net"
        };
    }

    /// <summary>
    /// تحميل أنماط URL المحجوبة
    /// </summary>
    private CompiledRegex[] LoadUrlPatterns()
    {
        return new[]
        {
            new CompiledRegex(@"/ads/|/ad\?|/ad/|/ads\?", "إعلانات عامة"),
            new CompiledRegex(@"doubleclick\.net", "DoubleClick"),
            new CompiledRegex(@"googlesyndication\.com", "Google Syndication"),
            new CompiledRegex(@"googleadservices\.com", "Google Ad Services"),
            new CompiledRegex(@"/pagead/", "Page Ads"),
            new CompiledRegex(@"/adnxs\.|/adsystem/", "Ad Networks"),
            new CompiledRegex(@"youtube\.com/api/stats/ads", "YouTube Ad Stats"),
            new CompiledRegex(@"youtube\.com/ptracking", "YouTube Tracking"),
            new CompiledRegex(@"/generate_204\?", "Tracking Pixels")
        };
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// الحصول على إجمالي العناصر المحجوبة
    /// </summary>
    /// <returns>عدد العناصر المحجوبة</returns>
    public int GetTotalBlocked()
    {
        return _blockedStats.GetValueOrDefault("Total", 0);
    }

    /// <summary>
    /// الحصول على إحصائيات الحجب المفصلة
    /// </summary>
    /// <returns>قاموس الإحصائيات</returns>
    public Dictionary<string, int> GetBlockedStats()
    {
        return new Dictionary<string, int>(_blockedStats);
    }

    /// <summary>
    /// إعادة تعيين الإحصائيات
    /// </summary>
    public void ResetStats()
    {
        _blockedStats.Clear();
        AppLogger.LogInfo("تم إعادة تعيين إحصائيات حجب الإعلانات");
    }

    /// <summary>
    /// تمكين أو تعطيل حجب الإعلانات
    /// </summary>
    /// <param name="enabled">true لتمكين الحجب</param>
    public async Task SetAdBlockingEnabledAsync(bool enabled)
    {
        if (enabled && !AppSettings.BlockAds)
        {
            AppSettings.BlockAds = true;
            await EnableAdBlockingAsync();
        }
        else if (!enabled && AppSettings.BlockAds)
        {
            AppSettings.BlockAds = false;
            // إعادة تحميل الصفحة لإزالة تأثيرات الحجب
            _webView?.Reload();
        }
    }

    #endregion

    #region IDisposable Implementation

    public void Dispose()
    {
        if (_disposed) return;

        try
        {
            _protectionTimer?.Dispose();
            _statsTimer?.Dispose();

            if (_webView != null)
            {
                _webView.DOMContentLoaded -= OnDOMContentLoaded;
                _webView.NavigationCompleted -= OnNavigationCompleted;
                _webView.WebResourceRequested -= OnWebResourceRequested;
            }

            _blockedStats.Clear();
            _disposed = true;

            AppLogger.LogInfo("تم التخلص من حاجب الإعلانات الموحد");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في التخلص من حاجب الإعلانات: {ex.Message}");
        }
    }

    #endregion
}