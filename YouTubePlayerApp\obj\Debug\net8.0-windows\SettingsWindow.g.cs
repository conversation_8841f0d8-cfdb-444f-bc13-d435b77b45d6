﻿#pragma checksum "..\..\..\SettingsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1ABA4F9C9C0823E4FC2FFDFFF4155292ACAEBA34"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using YouTubePlayerApp;


namespace YouTubePlayerApp {
    
    
    /// <summary>
    /// SettingsWindow
    /// </summary>
    public partial class SettingsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 28 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartWithWindowsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox StartFullScreenCheckBox;
        
        #line default
        #line hidden
        
        
        #line 32 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox RememberLastPositionCheckBox;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton YouTubeHomeRadio;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton YouTubeTrendingRadio;
        
        #line default
        #line hidden
        
        
        #line 43 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton CustomUrlRadio;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CustomUrlTextBox;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox VideoQualityComboBox;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoplayCheckBox;
        
        #line default
        #line hidden
        
        
        #line 76 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MuteOnStartupCheckBox;
        
        #line default
        #line hidden
        
        
        #line 80 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VolumeSlider;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ForceVideoQualityCheckBox;
        
        #line default
        #line hidden
        
        
        #line 94 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel QualityControlPanel;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ForcedVideoQualityComboBox;
        
        #line default
        #line hidden
        
        
        #line 111 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox LockQualityControlsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox HideQualityControlsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 115 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox OverrideUserQualityChangesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestQualityButton;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetQualityButton;
        
        #line default
        #line hidden
        
        
        #line 140 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ClearCookiesOnExitCheckBox;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockThirdPartyCookiesCheckBox;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearDataButton;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockAdsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 156 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AdBlockingOptionsPanel;
        
        #line default
        #line hidden
        
        
        #line 158 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockVideoAdsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 160 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockBannerAdsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 162 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox BlockTrackingScriptsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 164 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoSkipAdsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 166 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ShowAdBlockStatsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ViewBlockedStatsButton;
        
        #line default
        #line hidden
        
        
        #line 174 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ResetAdBlockerButton;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DoNotTrackCheckBox;
        
        #line default
        #line hidden
        
        
        #line 190 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox DisableAutoPreviewCheckBox;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OkButton;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\SettingsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/YouTubePlayerApp;component/settingswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\SettingsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StartWithWindowsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 2:
            this.StartFullScreenCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 3:
            this.RememberLastPositionCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 4:
            this.YouTubeHomeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 5:
            this.YouTubeTrendingRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 6:
            this.CustomUrlRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 7:
            this.CustomUrlTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 8:
            this.VideoQualityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 9:
            this.AutoplayCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 10:
            this.MuteOnStartupCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 11:
            this.VolumeSlider = ((System.Windows.Controls.Slider)(target));
            return;
            case 12:
            this.ForceVideoQualityCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 92 "..\..\..\SettingsWindow.xaml"
            this.ForceVideoQualityCheckBox.Checked += new System.Windows.RoutedEventHandler(this.ForceVideoQualityCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 92 "..\..\..\SettingsWindow.xaml"
            this.ForceVideoQualityCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.ForceVideoQualityCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 13:
            this.QualityControlPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 14:
            this.ForcedVideoQualityComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 15:
            this.LockQualityControlsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 16:
            this.HideQualityControlsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 17:
            this.OverrideUserQualityChangesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 18:
            this.TestQualityButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\SettingsWindow.xaml"
            this.TestQualityButton.Click += new System.Windows.RoutedEventHandler(this.TestQualityButton_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.ResetQualityButton = ((System.Windows.Controls.Button)(target));
            
            #line 129 "..\..\..\SettingsWindow.xaml"
            this.ResetQualityButton.Click += new System.Windows.RoutedEventHandler(this.ResetQualityButton_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.ClearCookiesOnExitCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 21:
            this.BlockThirdPartyCookiesCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 22:
            this.ClearDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 146 "..\..\..\SettingsWindow.xaml"
            this.ClearDataButton.Click += new System.Windows.RoutedEventHandler(this.ClearDataButton_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.BlockAdsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 154 "..\..\..\SettingsWindow.xaml"
            this.BlockAdsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.BlockAdsCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 154 "..\..\..\SettingsWindow.xaml"
            this.BlockAdsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.BlockAdsCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 24:
            this.AdBlockingOptionsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 25:
            this.BlockVideoAdsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 26:
            this.BlockBannerAdsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 27:
            this.BlockTrackingScriptsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 28:
            this.AutoSkipAdsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 29:
            this.ShowAdBlockStatsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 30:
            this.ViewBlockedStatsButton = ((System.Windows.Controls.Button)(target));
            
            #line 173 "..\..\..\SettingsWindow.xaml"
            this.ViewBlockedStatsButton.Click += new System.Windows.RoutedEventHandler(this.ViewBlockedStatsButton_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.ResetAdBlockerButton = ((System.Windows.Controls.Button)(target));
            
            #line 176 "..\..\..\SettingsWindow.xaml"
            this.ResetAdBlockerButton.Click += new System.Windows.RoutedEventHandler(this.ResetAdBlockerButton_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.DoNotTrackCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 33:
            this.DisableAutoPreviewCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 192 "..\..\..\SettingsWindow.xaml"
            this.DisableAutoPreviewCheckBox.Checked += new System.Windows.RoutedEventHandler(this.DisableAutoPreviewCheckBox_Changed);
            
            #line default
            #line hidden
            
            #line 192 "..\..\..\SettingsWindow.xaml"
            this.DisableAutoPreviewCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.DisableAutoPreviewCheckBox_Changed);
            
            #line default
            #line hidden
            return;
            case 34:
            this.OkButton = ((System.Windows.Controls.Button)(target));
            
            #line 206 "..\..\..\SettingsWindow.xaml"
            this.OkButton.Click += new System.Windows.RoutedEventHandler(this.OkButton_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            this.CancelButton = ((System.Windows.Controls.Button)(target));
            
            #line 208 "..\..\..\SettingsWindow.xaml"
            this.CancelButton.Click += new System.Windows.RoutedEventHandler(this.CancelButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

