using Microsoft.Web.WebView2.Core;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp;

/// <summary>
/// Optimized comprehensive ad blocker for YouTube with multiple blocking methods
/// </summary>
public sealed class AdBlocker : IDisposable
{
    private readonly HashSet<string> _blockedDomains;
    private readonly CompiledRegex[] _urlPatterns;
    private readonly ConcurrentDictionary<string, int> _blockedStats;
    private readonly SettingsCache _settingsCache;
    private CoreWebView2? _webView;
    private bool _disposed;

    // Compiled regex patterns for better performance
    private readonly struct CompiledRegex
    {
        public readonly Regex Pattern;
        public readonly string Description;

        public CompiledRegex(string pattern, string description)
        {
            Pattern = new Regex(pattern, RegexOptions.Compiled | RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
            Description = description;
        }
    }

    public AdBlocker()
    {
        _settingsCache = SettingsCache.Instance;
        _blockedDomains = LoadBlockedDomains();
        _urlPatterns = LoadUrlPatterns();
        _blockedStats = new ConcurrentDictionary<string, int>();
    }

    public void Initialize(CoreWebView2 webView)
    {
        _webView = webView;
        
        if (AppSettings.BlockAds)
        {
            EnableAdBlocking();
        }
    }

    public void EnableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("AdBlocker.EnableAdBlocking", () =>
        {
            // 1. Request filtering - block ad domains
            _webView.WebResourceRequested += OnWebResourceRequested;
            _webView.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

            // 2. DOM content loaded - inject CSS and JavaScript
            _webView.DOMContentLoaded += OnDOMContentLoaded;

            // 3. Navigation completed - apply blocking
            _webView.NavigationCompleted += OnNavigationCompleted;
        });
    }

    public void DisableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("AdBlocker.DisableAdBlocking", () =>
        {
            _webView.WebResourceRequested -= OnWebResourceRequested;
            _webView.DOMContentLoaded -= OnDOMContentLoaded;
            _webView.NavigationCompleted -= OnNavigationCompleted;
            _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
        });
    }

    private void OnWebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        if (_disposed) return;
        
        var uri = e.Request.Uri;
        
        // Block known ad domains and patterns - use cached settings for performance
        if (ShouldBlockRequest(uri))
        {
            e.Response = _webView?.Environment.CreateWebResourceResponse(
                null, 200, "OK", "");
            
            IncrementBlockedStat("Requests");
            
            // Log blocked request
            AppLogger.Instance.LogNetworkRequest(uri, true, "Ad domain/pattern blocked");
            
            // Use cached setting to avoid property access overhead
            if (_settingsCache.ShowAdBlockStats)
            {
                AppLogger.Instance.LogAdBlockingActivity("Request blocked", 1, uri);
            }
        }
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("AdBlocker.OnDOMContentLoaded");
            try
            {
                // Inject CSS first for immediate visual effect
                await InjectAdBlockingCSS();
                
                // Small delay to let CSS take effect before JS
                await Task.Delay(50);
                
                // Inject JavaScript for dynamic ad removal
                await InjectAdBlockingJavaScript();
                
                AppLogger.Instance.LogInfo("AdBlocker", "DOM Content Loaded - Ad blocking scripts injected");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("AdBlocker.OnDOMContentLoaded");
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("AdBlocker", ex, "Error injecting ad blocking code");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("AdBlocker.OnNavigationCompleted");
            try
            {
                AppLogger.Instance.LogInfo("AdBlocker", $"Navigation completed to: {_webView.Source}");
                
                // Wait a bit for the page to stabilize before cleanup
                await Task.Delay(1000);
                
                // Additional cleanup after page load
                await RemoveAdsAfterLoad();
                
                System.Diagnostics.Debug.WriteLine("[AdBlocker] Post-navigation ad cleanup completed");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("AdBlocker.OnNavigationCompleted");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[AdBlocker] Error in post-load ad removal: {ex.Message}");
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private bool ShouldBlockRequest(string uri)
    {
        try
        {
            // Fast string-based checks first (avoid URI parsing overhead)
            if (IsYouTubeAdRequest(uri))
                return true;

            // Check against URL patterns using compiled regex
            foreach (var pattern in _urlPatterns)
            {
                if (pattern.Pattern.IsMatch(uri))
                    return true;
            }

            // Domain check last (requires URI parsing)
            var uriObj = new Uri(uri);
            var host = uriObj.Host;

            // Use HashSet.Contains for O(1) lookup instead of LINQ
            return _blockedDomains.Contains(host) || 
                   _blockedDomains.Any(domain => host.Contains(domain, StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    private bool IsYouTubeAdRequest(string uri)
    {
        var youtubeAdPatterns = new[]
        {
            "/pagead/",
            "/ptracking",
            "googleads",
            "googlesyndication",
            "googletagservices",
            "/ads?",
            "/ad?",
            "doubleclick.net",
            "/gen_204?",
            "/youtubei/v1/log_event",
            "/api/stats/",
            "video_ads",
            "/adview",
            "/ad_companion",
            "/get_midroll_info",
            "/videoplayback/.*[&?]gir=yes",
            "/api/timedtext.*[&?]caps=asr"
        };

        return youtubeAdPatterns.Any(pattern => 
            Regex.IsMatch(uri, pattern, RegexOptions.IgnoreCase));
    }

    private async Task InjectAdBlockingCSS()
    {
        if (_webView == null) return;

        var css = GetAdBlockingCSS();
        var script = $@"
            (function() {{
                var style = document.createElement('style');
                style.textContent = `{css}`;
                document.head.appendChild(style);
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task InjectAdBlockingJavaScript()
    {
        if (_webView == null) return;

        var script = GetAdBlockingJavaScript();
        await _webView.ExecuteScriptAsync(script);
    }

    private async Task RemoveAdsAfterLoad()
    {
        if (_webView == null) return;

        var script = @"
            (function() {
                // Remove video ads
                const adContainers = document.querySelectorAll([
                    '.video-ads',
                    '.ytp-ad-module',
                    '.ytp-ad-overlay-container',
                    '.ytp-ad-image-overlay',
                    '.ytp-ad-text-overlay',
                    '[class*=""ad-container""]',
                    '[id*=""ad-container""]'
                ].join(','));
                
                adContainers.forEach(el => el.remove());
                
                // Skip ad button auto-click
                const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                if (skipButton && skipButton.offsetParent !== null) {
                    skipButton.click();
                }
                
                // Remove banner ads
                const bannerAds = document.querySelectorAll([
                    '[data-ad-status]',
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-promoted-video-renderer',
                    '.ytd-banner-promo-renderer',
                    '.ytd-in-feed-ad-layout-renderer'
                ].join(','));
                
                bannerAds.forEach(el => el.remove());
                
                return adContainers.length + bannerAds.length;
            })();
        ";

        try
        {
            var result = await _webView.ExecuteScriptAsync(script);
            if (int.TryParse(result, out int blockedCount) && blockedCount > 0)
            {
                IncrementBlockedStat("Elements", blockedCount);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error executing ad removal script: {ex.Message}");
        }
    }

    private string GetAdBlockingCSS()
    {
        return @"
            /* Video Ad Containers */
            .video-ads,
            .ytp-ad-module,
            .ytp-ad-overlay-container,
            .ytp-ad-image-overlay,
            .ytp-ad-text-overlay,
            .ytp-ad-player-overlay,
            .ytp-ad-skip-button-container,
            .ytp-ad-preview-container,
            [class*='ad-container'],
            [id*='ad-container'] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Banner and Display Ads */
            .ytd-promoted-sparkles-web-renderer,
            .ytd-promoted-video-renderer,
            .ytd-banner-promo-renderer,
            .ytd-in-feed-ad-layout-renderer,
            .ytd-ad-slot-renderer,
            [data-ad-status],
            [data-is-ad='true'],
            [aria-label*='Ad'],
            [aria-label*='Sponsored'] {
                display: none !important;
            }
            
            /* Sidebar Ads */
            #player-ads,
            #masthead-ad,
            .ytd-companion-slot-renderer,
            .ytd-action-companion-ad-renderer {
                display: none !important;
            }
            
            /* Hide ad-related overlays */
            .ytp-ad-overlay-container *,
            .ytp-ad-image-overlay *,
            .ytp-ad-text-overlay * {
                display: none !important;
            }
            
            /* Premium upgrade prompts */
            .ytd-popup-container[dialog][role='dialog'],
            ytd-mealbar-promo-renderer {
                display: none !important;
            }
        ";
    }

    private string GetAdBlockingJavaScript()
    {
        return @"
            (function() {
                'use strict';
                
                // Optimized ad selectors for better performance
                const AD_SELECTORS = [
                    '.video-ads',
                    '.ytp-ad-module',
                    '.ytp-ad-overlay-container',
                    '.ytp-ad-image-overlay',
                    '.ytp-ad-text-overlay',
                    '[class*=""ad-container""]',
                    '[id*=""ad-container""]',
                    '[data-ad-status]',
                    '.ytd-promoted-video-renderer',
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-banner-promo-renderer',
                    '.ytd-in-feed-ad-layout-renderer'
                ];
                
                const AD_SELECTOR_STRING = AD_SELECTORS.join(',');
                
                // Throttled DOM cleaner for better performance
                let cleanupTimeout = null;
                let mutationCount = 0;
                const MAX_MUTATIONS_PER_BATCH = 50;
                
                function throttledCleanup() {
                    if (cleanupTimeout) return;
                    
                    cleanupTimeout = setTimeout(() => {
                        try {
                            // Batch cleanup for better performance
                            const adElements = document.querySelectorAll(AD_SELECTOR_STRING);
                            let removedCount = 0;
                            
                            adElements.forEach(el => {
                                if (el.offsetParent !== null) { // Only remove visible elements
                                    el.remove();
                                    removedCount++;
                                }
                            });
                            
                            if (removedCount > 0) {
                                console.debug(`[AdBlocker] Removed ${removedCount} ad elements`);
                            }
                        } catch (error) {
                            console.warn('[AdBlocker] Error during cleanup:', error);
                        } finally {
                            cleanupTimeout = null;
                            mutationCount = 0;
                        }
                    }, 100); // 100ms throttle
                }
                
                // Optimized mutation observer with throttling
                const observer = new MutationObserver(function(mutations) {
                    mutationCount += mutations.length;
                    
                    // Quick inline check for new ad elements
                    let hasAds = false;
                    for (const mutation of mutations) {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === 1 && 
                                (node.matches?.(AD_SELECTOR_STRING) || 
                                 node.querySelector?.(AD_SELECTOR_STRING))) {
                                hasAds = true;
                                break;
                            }
                        }
                        if (hasAds) break;
                    }
                    
                    // Only trigger cleanup if ads detected or mutation threshold reached
                    if (hasAds || mutationCount >= MAX_MUTATIONS_PER_BATCH) {
                        throttledCleanup();
                    }
                });
                
                // Start observing with optimized config
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: false, // Don't watch attribute changes for performance
                    characterData: false // Don't watch text changes
                });
                
                // Enhanced YouTube API overrides
                function overrideYouTubeAPI() {
                    try {
                        // Override ad configuration
                        if (window.yt?.config_) {
                            const config = window.yt.config_;
                            config.EXPERIMENT_FLAGS = config.EXPERIMENT_FLAGS || {};
                            Object.assign(config.EXPERIMENT_FLAGS, {
                                web_player_ads_control_config: { enabled: false },
                                html5_disable_av01_hdr: true,
                                web_player_move_autonav_toggle: true,
                                kevlar_dropdown_fix: true
                            });
                        }
                        
                        // Override player ads
                        if (window.ytplayer?.config?.args) {
                            window.ytplayer.config.args.fflags = 
                                (window.ytplayer.config.args.fflags || '') + '&html5_disable_av01_hdr=true';
                        }
                    } catch (error) {
                        console.warn('[AdBlocker] API override error:', error);
                    }
                }
                
                // Intelligent ad skip with backoff
                let skipAttempts = 0;
                const MAX_SKIP_ATTEMPTS = 3;
                let skipBackoffDelay = 500;
                
                function smartAdSkip() {
                    const skipButton = document.querySelector(
                        '.ytp-ad-skip-button, .ytp-skip-ad-button, .ytp-ad-skip-button-modern'
                    );
                    
                    if (skipButton?.offsetParent !== null && skipButton.click) {
                        skipButton.click();
                        skipAttempts++;
                        
                        if (skipAttempts >= MAX_SKIP_ATTEMPTS) {
                            skipBackoffDelay = Math.min(skipBackoffDelay * 2, 5000); // Max 5s
                        }
                        
                        console.debug('[AdBlocker] Ad skip attempted');
                        return true;
                    }
                    return false;
                }
                
                // Adaptive skip interval based on success rate
                let skipInterval = setInterval(() => {
                    if (smartAdSkip()) {
                        // Reset attempts on successful skip
                        skipAttempts = 0;
                        skipBackoffDelay = 500;
                    }
                }, skipBackoffDelay);
                
                // Enhanced fetch blocking
                const originalFetch = window.fetch;
                const originalXHR = window.XMLHttpRequest.prototype.open;
                
                const adUrlPatterns = [
                    /\/pagead\//,
                    /googleads/,
                    /doubleclick/,
                    /googlesyndication/,
                    /googletagservices/,
                    /youtube\.com\/ptracking/,
                    /youtube\.com\/api\/stats/,
                    /get_midroll_info/,
                    /generate_204/
                ];
                
                function shouldBlockURL(url) {
                    return adUrlPatterns.some(pattern => pattern.test(url));
                }
                
                // Override fetch
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && shouldBlockURL(url)) {
                        return Promise.reject(new Error('[AdBlocker] Request blocked'));
                    }
                    return originalFetch.apply(this, args);
                };
                
                // Override XMLHttpRequest
                XMLHttpRequest.prototype.open = function(method, url, ...rest) {
                    if (typeof url === 'string' && shouldBlockURL(url)) {
                        console.debug('[AdBlocker] XHR blocked:', url);
                        // Return a dummy response instead of throwing
                        this.status = 204;
                        this.statusText = 'Blocked';
                        return;
                    }
                    return originalXHR.apply(this, arguments);
                };
                
                // Initialize
                overrideYouTubeAPI();
                
                // Re-apply overrides after navigation
                let lastUrl = location.href;
                setInterval(() => {
                    if (location.href !== lastUrl) {
                        lastUrl = location.href;
                        setTimeout(overrideYouTubeAPI, 1000);
                    }
                }, 2000);
                
                // Initial cleanup
                setTimeout(throttledCleanup, 500);
                
                console.log('[AdBlocker] Enhanced YouTube ad blocker activated');
            })();
        ";
    }

    private static readonly HashSet<string> StaticBlockedDomains = new(StringComparer.OrdinalIgnoreCase)
    {
        // Google Ads
        "googleads.g.doubleclick.net",
        "googlesyndication.com", 
        "googletagservices.com",
        "doubleclick.net",
        "googleadservices.com",
        
        // YouTube specific
        "youtube.com/pagead",
        "youtube.com/ptracking", 
        "youtube.com/api/stats",
        
        // Analytics and tracking
        "google-analytics.com",
        "googletagmanager.com",
        "adsystem.amazon.com",
        "facebook.com/tr",
        
        // Other ad networks
        "amazon-adsystem.com",
        "adsafeprotected.com",
        "moatads.com",
        "scorecardresearch.com",
        "outbrain.com",
        "taboola.com"
    };

    private static readonly CompiledRegex[] StaticUrlPatterns = new[]
    {
        new CompiledRegex(@"/pagead/", "Page ads"),
        new CompiledRegex(@"/ads\?", "Ad queries"),
        new CompiledRegex(@"/ad\?", "Ad queries short"),
        new CompiledRegex(@"[&?]ad_type=", "Ad type parameter"),
        new CompiledRegex(@"[&?]adurl=", "Ad URL parameter"),
        new CompiledRegex(@"googleads\.g\.doubleclick\.net", "Google DoubleClick"),
        new CompiledRegex(@"googlesyndication\.com", "Google Syndication"),
        new CompiledRegex(@"youtube\.com/ptracking", "YouTube tracking"),
        new CompiledRegex(@"youtube\.com/api/stats/", "YouTube stats"),
        new CompiledRegex(@"youtube\.com/youtubei/v1/log_event", "YouTube logging"),
        new CompiledRegex(@"[&?]gir=yes", "GIR parameter"),
        new CompiledRegex(@"/gen_204\?", "Gen 204 beacon")
    };

    private HashSet<string> LoadBlockedDomains()
    {
        return StaticBlockedDomains;
    }

    private CompiledRegex[] LoadUrlPatterns()
    {
        return StaticUrlPatterns;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private void IncrementBlockedStat(string category, int count = 1)
    {
        _blockedStats.AddOrUpdate(category, count, (key, oldValue) => oldValue + count);
    }

    public Dictionary<string, int> GetBlockedStats()
    {
        return new Dictionary<string, int>(_blockedStats);
    }

    public void ClearStats()
    {
        _blockedStats.Clear();
    }

    public int GetTotalBlocked()
    {
        return _blockedStats.Values.Sum();
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        
        if (_webView != null)
        {
            try
            {
                _webView.WebResourceRequested -= OnWebResourceRequested;
                _webView.DOMContentLoaded -= OnDOMContentLoaded;
                _webView.NavigationCompleted -= OnNavigationCompleted;
                _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing AdBlocker: {ex.Message}");
            }
        }
        
        _blockedStats.Clear();
        _webView = null;
    }
}
