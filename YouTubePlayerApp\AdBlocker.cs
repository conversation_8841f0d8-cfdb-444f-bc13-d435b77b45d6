using Microsoft.Web.WebView2.Core;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp;

/// <summary>
/// Optimized comprehensive ad blocker for YouTube with multiple blocking methods
/// </summary>
public sealed class AdBlocker : IDisposable
{
    private readonly HashSet<string> _blockedDomains;
    private readonly CompiledRegex[] _urlPatterns;
    private readonly ConcurrentDictionary<string, int> _blockedStats;
    private readonly SettingsCache _settingsCache;
    private CoreWebView2? _webView;
    private bool _disposed;

    // Compiled regex patterns for better performance
    private readonly struct CompiledRegex
    {
        public readonly Regex Pattern;
        public readonly string Description;

        public CompiledRegex(string pattern, string description)
        {
            Pattern = new Regex(pattern, RegexOptions.Compiled | RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
            Description = description;
        }
    }

    public AdBlocker()
    {
        _settingsCache = SettingsCache.Instance;
        _blockedDomains = LoadBlockedDomains();
        _urlPatterns = LoadUrlPatterns();
        _blockedStats = new ConcurrentDictionary<string, int>();
    }

    public void Initialize(CoreWebView2 webView)
    {
        _webView = webView;
        
        if (AppSettings.BlockAds)
        {
            EnableAdBlocking();
        }
    }

    public void EnableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("AdBlocker.EnableAdBlocking", () =>
        {
            // 1. Request filtering - block ad domains
            _webView.WebResourceRequested += OnWebResourceRequested;
            _webView.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

            // 2. DOM content loaded - inject CSS and JavaScript
            _webView.DOMContentLoaded += OnDOMContentLoaded;

            // 3. Navigation completed - apply blocking
            _webView.NavigationCompleted += OnNavigationCompleted;
        });
    }

    public void DisableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("AdBlocker.DisableAdBlocking", () =>
        {
            _webView.WebResourceRequested -= OnWebResourceRequested;
            _webView.DOMContentLoaded -= OnDOMContentLoaded;
            _webView.NavigationCompleted -= OnNavigationCompleted;
            _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
        });
    }

    private void OnWebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        if (_disposed) return;
        
        var uri = e.Request.Uri;
        
        // Block known ad domains and patterns - use cached settings for performance
        if (ShouldBlockRequest(uri))
        {
            e.Response = _webView?.Environment.CreateWebResourceResponse(
                null, 200, "OK", "");
            
            IncrementBlockedStat("Requests");
            
            // Log blocked request
            AppLogger.Instance.LogNetworkRequest(uri, true, "Ad domain/pattern blocked");
            
            // Use cached setting to avoid property access overhead
            if (_settingsCache.ShowAdBlockStats)
            {
                AppLogger.Instance.LogAdBlockingActivity("Request blocked", 1, uri);
            }
        }
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("AdBlocker.OnDOMContentLoaded");
            try
            {
                // Inject CSS first for immediate visual effect
                await InjectAdBlockingCSS();
                
                // Small delay to let CSS take effect before JS
                await Task.Delay(50);
                
                // Inject JavaScript for dynamic ad removal
                await InjectAdBlockingJavaScript();
                
                AppLogger.Instance.LogInfo("AdBlocker", "DOM Content Loaded - Ad blocking scripts injected");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("AdBlocker.OnDOMContentLoaded");
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("AdBlocker", ex, "Error injecting ad blocking code");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("AdBlocker.OnNavigationCompleted");
            try
            {
                AppLogger.Instance.LogInfo("AdBlocker", $"Navigation completed to: {_webView.Source}");
                
                // Wait a bit for the page to stabilize before cleanup
                await Task.Delay(1000);
                
                // Additional cleanup after page load
                await RemoveAdsAfterLoad();
                
                System.Diagnostics.Debug.WriteLine("[AdBlocker] Post-navigation ad cleanup completed");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("AdBlocker.OnNavigationCompleted");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[AdBlocker] Error in post-load ad removal: {ex.Message}");
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private bool ShouldBlockRequest(string uri)
    {
        try
        {
            // Fast string-based checks first (avoid URI parsing overhead)
            if (IsYouTubeAdRequest(uri))
                return true;

            // Check against URL patterns using compiled regex
            foreach (var pattern in _urlPatterns)
            {
                if (pattern.Pattern.IsMatch(uri))
                    return true;
            }

            // Domain check last (requires URI parsing)
            var uriObj = new Uri(uri);
            var host = uriObj.Host;

            // Use HashSet.Contains for O(1) lookup instead of LINQ
            return _blockedDomains.Contains(host) || 
                   _blockedDomains.Any(domain => host.Contains(domain, StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    private bool IsYouTubeAdRequest(string uri)
    {
        var youtubeAdPatterns = new[]
        {
            // Core ad serving
            "/pagead/",
            "/ptracking",
            "googleads",
            "googlesyndication",
            "googletagservices",
            "/ads?",
            "/ad?",
            "doubleclick.net",
            "/gen_204?",
            "/youtubei/v1/log_event",
            "/api/stats/",
            "video_ads",
            "/adview",
            "/ad_companion",
            "/get_midroll_info",
            "/videoplayback/.*[&?]gir=yes",
            "/api/timedtext.*[&?]caps=asr",
            
            // Enhanced ad patterns for 2024
            "/youtubei/v1/player/ad_break",
            "/youtubei/v1/next.*[&?]ad=",
            "/youtubei/v1/browse.*[&?]ad=",
            "/api/stats/ads",
            "/api/stats/atr",
            "/api/stats/qoe",
            "/generate_204.*[&?]ad",
            "/pcs/activeview",
            "/pagead/interaction",
            "/pagead/adview",
            "/pagead/conversion",
            "/ads/measurement",
            "/doubleclick/instream/ad_status",
            "/video_ads_serving",
            "/ad_break",
            "/midroll_ad",
            "/preroll_ad",
            "/postroll_ad",
            "/companion_ad",
            "/overlay_ad",
            "/bumper_ad",
            "/masthead_ad",
            "/promoted_content",
            "/sponsored_content",
            "/brand_video",
            "/trueview",
            "/adnxs.com",
            "/adsystem.amazon.com",
            "/facebook.com/tr",
            "/analytics.tiktok.com",
            "/bat.bing.com",
            "/outbrain.com",
            "/taboola.com",
            "/criteo.com",
            "/adsafeprotected.com",
            "/moatads.com",
            "/scorecardresearch.com",
            "/quantserve.com",
            "/amazon-adsystem.com",
            "/googletag",
            "/gtag/js",
            "/gtm.js",
            "/analytics.js",
            "/ga.js",
            "/dc.js",
            "/ads.js",
            "/adsense",
            "/adsbygoogle",
            "/show_ads",
            "/ad_status",
            "/ad_break_heartbeat",
            "/ad_video_id",
            "/ad_cpn",
            "/ad_docid",
            "/ad_mt",
            "/ad_tag",
            "/ad_system",
            "/ad_pod",
            "/ad_creative",
            "/ad_campaign",
            "/ad_advertiser",
            "/ad_break_type",
            "/ad_format",
            "/ad_skippable",
            "/ad_duration",
            "/ad_position",
            "/ad_sequence",
            "/ad_break_start",
            "/ad_break_end",
            "/ad_impression",
            "/ad_click",
            "/ad_view",
            "/ad_complete",
            "/ad_skip",
            "/ad_mute",
            "/ad_unmute",
            "/ad_pause",
            "/ad_resume",
            "/ad_fullscreen",
            "/ad_exitfullscreen",
            "/ad_volume_change",
            "/ad_progress",
            "/ad_quartile",
            "/ad_midpoint",
            "/ad_third_quartile",
            "/ad_error",
            "/ad_loaded",
            "/ad_started",
            "/ad_first_quartile"
        };

        return youtubeAdPatterns.Any(pattern => 
            Regex.IsMatch(uri, pattern, RegexOptions.IgnoreCase));
    }

    private async Task InjectAdBlockingCSS()
    {
        if (_webView == null) return;

        var css = GetAdBlockingCSS();
        var script = $@"
            (function() {{
                var style = document.createElement('style');
                style.textContent = `{css}`;
                document.head.appendChild(style);
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task InjectAdBlockingJavaScript()
    {
        if (_webView == null) return;

        var script = GetAdBlockingJavaScript();
        await _webView.ExecuteScriptAsync(script);
    }

    private async Task RemoveAdsAfterLoad()
    {
        if (_webView == null) return;

        var script = @"
            (function() {
                console.log('[AdBlocker] Starting safe ad removal...');
                let removedCount = 0;
                
                // Safe ad removal - only target specific ad elements without affecting video player
                const safeAdSelectors = [
                    // Video ads (but preserve player controls)
                    '.video-ads:not(.html5-video-player):not(.ytp-chrome-bottom)',
                    '.ytp-ad-module:not(.ytp-chrome-controls)',
                    '.ytp-ad-overlay-container:not(.ytp-chrome-bottom)',
                    '.ytp-ad-image-overlay:not(.ytp-play-button)',
                    '.ytp-ad-text-overlay:not(.ytp-chrome-controls)',
                    
                    // Banner and promoted content
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-promoted-video-renderer',
                    '.ytd-banner-promo-renderer', 
                    '.ytd-in-feed-ad-layout-renderer',
                    '.ytd-ad-slot-renderer',
                    '.ytd-display-ad-renderer',
                    
                    // Sidebar ads
                    '#player-ads',
                    '#masthead-ad',
                    '#watch-sidebar-ads',
                    '.ytd-companion-slot-renderer',
                    
                    // Data attributes for ads
                    '[data-ad-status]:not(video):not(.html5-video-player)',
                    '[data-is-ad=""true""]:not(video):not(.html5-video-player)',
                    '[data-promoted=""true""]:not(video):not(.html5-video-player)'
                ];
                
                // Remove ads safely
                safeAdSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            // Double check we're not removing video player elements
                            if (!el.closest('.html5-video-player') && 
                                !el.closest('.ytp-chrome-bottom') &&
                                !el.closest('.ytp-chrome-controls') &&
                                !el.classList.contains('ytp-play-button') &&
                                !el.classList.contains('ytp-large-play-button')) {
                                el.remove();
                                removedCount++;
                            }
                        });
                    } catch (e) {
                        console.warn('[AdBlocker] Error with selector:', selector, e);
                    }
                });
                
                // Auto-skip ads (safe approach)
                setTimeout(() => {
                    const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                    if (skipButton && skipButton.offsetParent !== null && skipButton.style.display !== 'none') {
                        console.log('[AdBlocker] Auto-clicking skip button');
                        skipButton.click();
                    }
                }, 1000);
                
                // Ensure video player functionality is preserved
                const videoElement = document.querySelector('video.html5-main-video');
                if (videoElement) {
                    // Re-enable any disabled event listeners
                    videoElement.style.pointerEvents = 'auto';
                    
                    // Ensure click events work on video
                    if (!videoElement.hasAttribute('data-adblock-processed')) {
                        videoElement.setAttribute('data-adblock-processed', 'true');
                        
                        // Preserve original click behavior
                        videoElement.addEventListener('click', function(e) {
                            // Allow normal video click behavior
                            if (!e.defaultPrevented) {
                                // Let YouTube handle the click
                                return true;
                            }
                        }, false);
                    }
                }
                
                // Ensure player controls are clickable
                const playerControls = document.querySelector('.ytp-chrome-bottom');
                if (playerControls) {
                    playerControls.style.pointerEvents = 'auto';
                    playerControls.style.zIndex = '9999';
                }
                
                console.log('[AdBlocker] Safe ad removal completed. Removed:', removedCount);
                return removedCount;
            })();
        ";

        try
        {
            var result = await _webView.ExecuteScriptAsync(script);
            if (int.TryParse(result, out int blockedCount) && blockedCount > 0)
            {
                IncrementBlockedStat("Elements", blockedCount);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error executing ad removal script: {ex.Message}");
        }
    }

    private string GetAdBlockingCSS()
    {
        return @"
            /* Video Ad Containers - Enhanced 2024 */
            .video-ads,
            .ytp-ad-module,
            .ytp-ad-overlay-container,
            .ytp-ad-image-overlay,
            .ytp-ad-text-overlay,
            .ytp-ad-player-overlay,
            .ytp-ad-skip-button-container,
            .ytp-ad-preview-container,
            .ytp-ad-display-container,
            .ytp-ad-persistent-progress-bar-container,
            .ytp-ad-action-interstitial,
            .ytp-ad-button-container,
            .ytp-ad-image-overlay-container,
            .ytp-ad-text-overlay-container,
            .ytp-ad-overlay-close-button,
            .ytp-ad-overlay-image,
            .ytp-ad-overlay-text,
            .ytp-ad-overlay-link,
            .ytp-ad-overlay-duration-text,
            .ytp-ad-overlay-skip-or-preview,
            .ytp-ad-overlay-instream-banner,
            .ytp-ad-overlay-container-v2,
            .ytp-ad-image-overlay-v2,
            .ytp-ad-text-overlay-v2,
            .ytp-ad-overlay-slot,
            .ytp-ad-overlay-close-container,
            .ytp-ad-overlay-close-button-container,
            .ytp-ad-overlay-close-button-v2,
            .ytp-ad-overlay-image-container,
            .ytp-ad-overlay-text-container,
            .ytp-ad-overlay-link-container,
            .ytp-ad-overlay-duration-container,
            .ytp-ad-overlay-skip-container,
            .ytp-ad-overlay-preview-container,
            .ytp-ad-overlay-banner-container,
            [class*='ad-container'],
            [id*='ad-container'],
            [class*='ytp-ad'],
            [id*='ytp-ad'] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
                position: absolute !important;
                left: -9999px !important;
                top: -9999px !important;
                z-index: -1 !important;
                pointer-events: none !important;
            }
            
            /* Banner and Display Ads - Enhanced */
            .ytd-promoted-sparkles-web-renderer,
            .ytd-promoted-video-renderer,
            .ytd-banner-promo-renderer,
            .ytd-in-feed-ad-layout-renderer,
            .ytd-ad-slot-renderer,
            .ytd-promoted-sparkles-text-search-renderer,
            .ytd-search-promoted-sparkles-web-renderer,
            .ytd-promoted-video-renderer,
            .ytd-compact-promoted-video-renderer,
            .ytd-promoted-sparkles-web-renderer,
            .ytd-ad-slot-renderer,
            .ytd-display-ad-renderer,
            .ytd-promoted-sparkles-text-search-renderer,
            .ytd-search-promoted-sparkles-web-renderer,
            .ytd-search-promoted-item-renderer,
            .ytd-carousel-ad-renderer,
            .ytd-video-masthead-ad-renderer,
            .ytd-primetime-promo-renderer,
            .ytd-statement-banner-renderer,
            .ytd-brand-video-singleton-renderer,
            .ytd-brand-video-shelf-renderer,
            .ytd-player-legacy-desktop-watch-ads-renderer,
            .ytd-ads-engagement-panel-content-renderer,
            .ytd-structured-description-ad-content-renderer,
            .ytd-video-owner-renderer[is-empty],
            .ytd-merch-shelf-renderer,
            .ytd-product-details-renderer,
            .ytd-expandable-video-description-body-renderer .ytd-structured-description-ad-content-renderer,
            [data-ad-status],
            [data-is-ad='true'],
            [data-ad='true'],
            [data-promoted='true'],
            [data-is-sponsored='true'],
            [aria-label*='Ad'],
            [aria-label*='Sponsored'],
            [aria-label*='Promotion'],
            [title*='Ad'],
            [title*='Sponsored'],
            [title*='Promotion'],
            [class*='promoted'],
            [class*='sponsor'],
            [class*='advertisement'],
            [id*='promoted'],
            [id*='sponsor'],
            [id*='advertisement'] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Sidebar and Companion Ads - Enhanced */
            #player-ads,
            #masthead-ad,
            #watch-sidebar-ads,
            #watch-channel-brand-div,
            #watch7-sidebar-ads,
            #watch-branded-actions,
            #watch-channel-brand-div,
            #watch7-branded-banner,
            #watch-branded-banner,
            #player-branded-banner,
            .ytd-companion-slot-renderer,
            .ytd-action-companion-ad-renderer,
            .ytd-compact-promoted-video-renderer,
            .ytd-promoted-sparkles-web-renderer,
            .ytd-carousel-ad-renderer,
            .ytd-video-masthead-ad-renderer,
            .ytd-primetime-promo-renderer,
            .ytd-statement-banner-renderer,
            .ytd-brand-video-singleton-renderer,
            .ytd-brand-video-shelf-renderer,
            .ytd-player-legacy-desktop-watch-ads-renderer,
            .ytd-ads-engagement-panel-content-renderer,
            .ytd-structured-description-ad-content-renderer,
            .companion-ad-container,
            .masthead-ad-container,
            .sidebar-ad-container,
            .watch-sidebar-ad-container,
            .branded-banner-container,
            .companion-slot-container,
            .action-companion-ad-container {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Hide ad-related overlays - Enhanced */
            .ytp-ad-overlay-container *,
            .ytp-ad-image-overlay *,
            .ytp-ad-text-overlay *,
            .ytp-ad-overlay-container-v2 *,
            .ytp-ad-image-overlay-v2 *,
            .ytp-ad-text-overlay-v2 *,
            .ytp-ad-overlay-slot *,
            .ytp-ad-overlay-close-container *,
            .ytp-ad-overlay-image-container *,
            .ytp-ad-overlay-text-container *,
            .ytp-ad-overlay-link-container *,
            .ytp-ad-overlay-duration-container *,
            .ytp-ad-overlay-skip-container *,
            .ytp-ad-overlay-preview-container *,
            .ytp-ad-overlay-banner-container * {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
            
            /* Premium upgrade prompts and notifications - Enhanced */
            .ytd-popup-container[dialog][role='dialog'],
            .ytd-mealbar-promo-renderer,
            .ytd-consent-bump-v2-lightbox,
            .ytd-consent-bump-v2-renderer,
            .ytd-single-option-survey-renderer,
            .ytd-multi-option-survey-renderer,
            .ytd-survey-renderer,
            .ytd-premium-upsell-renderer,
            .ytd-upsell-dialog-renderer,
            .ytd-offline-promo-renderer,
            .ytd-background-promo-renderer,
            .ytd-donation-shelf-renderer,
            .ytd-fundraiser-support-renderer,
            .ytd-membership-item-renderer,
            .ytd-sponsorships-live-chat-header-renderer,
            .ytd-sponsorships-live-chat-gift-purchase-announcement-renderer,
            .ytd-sponsorships-live-chat-gift-redemption-announcement-renderer,
            .ytd-paid-content-overlay-renderer,
            .ytd-player-overlay-renderer[overlay-style='PAID_CONTENT'],
            .ytd-player-overlay-autoplay-renderer,
            .ytd-clarification-renderer,
            .ytd-info-panel-content-renderer,
            .ytd-single-action-engagement-panel-content-renderer,
            .ytd-engagement-panel-section-list-renderer[target-id*='engagement-panel-ads'],
            .ytd-engagement-panel-section-list-renderer[target-id*='engagement-panel-promoted'],
            .ytd-engagement-panel-section-list-renderer[target-id*='engagement-panel-sponsor'],
            ytd-mealbar-promo-renderer,
            ytd-consent-bump-v2-lightbox,
            ytd-consent-bump-v2-renderer,
            ytd-single-option-survey-renderer,
            ytd-multi-option-survey-renderer,
            ytd-survey-renderer,
            ytd-premium-upsell-renderer,
            ytd-upsell-dialog-renderer,
            ytd-offline-promo-renderer,
            ytd-background-promo-renderer,
            ytd-donation-shelf-renderer,
            ytd-fundraiser-support-renderer,
            ytd-membership-item-renderer,
            ytd-sponsorships-live-chat-header-renderer,
            ytd-sponsorships-live-chat-gift-purchase-announcement-renderer,
            ytd-sponsorships-live-chat-gift-redemption-announcement-renderer,
            ytd-paid-content-overlay-renderer,
            ytd-player-overlay-renderer[overlay-style='PAID_CONTENT'],
            ytd-player-overlay-autoplay-renderer,
            ytd-clarification-renderer,
            ytd-info-panel-content-renderer,
            ytd-single-action-engagement-panel-content-renderer {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Shopping and product ads */
            .ytd-product-details-renderer,
            .ytd-merch-shelf-renderer,
            .ytd-shopping-shelf-renderer,
            .ytd-product-shelf-renderer,
            .ytd-shopping-carousel-renderer,
            .ytd-product-carousel-renderer,
            .ytd-shopping-offer-renderer,
            .ytd-product-offer-renderer,
            .ytd-shopping-generic-header-renderer,
            .ytd-product-generic-header-renderer,
            ytd-product-details-renderer,
            ytd-merch-shelf-renderer,
            ytd-shopping-shelf-renderer,
            ytd-product-shelf-renderer,
            ytd-shopping-carousel-renderer,
            ytd-product-carousel-renderer,
            ytd-shopping-offer-renderer,
            ytd-product-offer-renderer,
            ytd-shopping-generic-header-renderer,
            ytd-product-generic-header-renderer {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Live chat ads and sponsorships */
            .ytd-sponsorships-live-chat-header-renderer,
            .ytd-sponsorships-live-chat-gift-purchase-announcement-renderer,
            .ytd-sponsorships-live-chat-gift-redemption-announcement-renderer,
            .ytd-live-chat-paid-message-renderer,
            .ytd-live-chat-paid-sticker-renderer,
            .ytd-live-chat-membership-item-renderer,
            .ytd-live-chat-sponsor-renderer,
            ytd-sponsorships-live-chat-header-renderer,
            ytd-sponsorships-live-chat-gift-purchase-announcement-renderer,
            ytd-sponsorships-live-chat-gift-redemption-announcement-renderer,
            ytd-live-chat-paid-message-renderer,
            ytd-live-chat-paid-sticker-renderer,
            ytd-live-chat-membership-item-renderer,
            ytd-live-chat-sponsor-renderer {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Shorts ads */
            .ytd-reel-video-renderer[is-ad],
            .ytd-shorts-video-renderer[is-ad],
            .ytd-reel-item-renderer[is-ad],
            .ytd-shorts-item-renderer[is-ad],
            ytd-reel-video-renderer[is-ad],
            ytd-shorts-video-renderer[is-ad],
            ytd-reel-item-renderer[is-ad],
            ytd-shorts-item-renderer[is-ad] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Generic ad patterns */
            [class*='ad-'],
            [class*='ads-'],
            [class*='advertisement'],
            [class*='promoted'],
            [class*='sponsor'],
            [class*='brand-'],
            [id*='ad-'],
            [id*='ads-'],
            [id*='advertisement'],
            [id*='promoted'],
            [id*='sponsor'],
            [id*='brand-'] {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
            
            /* Force hide any remaining ad elements */
            *[class*='ad']:not([class*='add']):not([class*='admin']):not([class*='advance']):not([class*='adventure']):not([class*='advice']):not([class*='advocate']):not([class*='adapter']):not([class*='address']):not([class*='adjacent']):not([class*='adjust']),
            *[id*='ad']:not([id*='add']):not([id*='admin']):not([id*='advance']):not([id*='adventure']):not([id*='advice']):not([id*='advocate']):not([id*='adapter']):not([id*='address']):not([id*='adjacent']):not([id*='adjust']) {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
        ";
    }

    private string GetAdBlockingJavaScript()
    {
        return @"
            (function() {
                'use strict';
                console.log('[AdBlocker] Initializing safe ad blocking JavaScript...');
                
                // Safe ad selectors that won't interfere with video player functionality
                const SAFE_AD_SELECTORS = [
                    // Video ad containers
                    '.video-ads',
                    '.ytp-ad-module',
                    '.ytp-ad-overlay-container',
                    '.ytp-ad-image-overlay',
                    '.ytp-ad-text-overlay',
                    '.ytp-ad-player-overlay',
                    '.ytp-ad-skip-button-container',
                    '.ytp-ad-preview-container',
                    '.ytp-ad-display-container',
                    '.ytp-ad-persistent-progress-bar-container',
                    '.ytp-ad-action-interstitial',
                    '.ytp-ad-button-container',
                    '.ytp-ad-image-overlay-container',
                    '.ytp-ad-text-overlay-container',
                    '.ytp-ad-overlay-close-button',
                    '.ytp-ad-overlay-image',
                    '.ytp-ad-overlay-text',
                    '.ytp-ad-overlay-link',
                    '.ytp-ad-overlay-duration-text',
                    '.ytp-ad-overlay-skip-or-preview',
                    '.ytp-ad-overlay-instream-banner',
                    '.ytp-ad-overlay-container-v2',
                    '.ytp-ad-image-overlay-v2',
                    '.ytp-ad-text-overlay-v2',
                    '.ytp-ad-overlay-slot',
                    '.ytp-ad-overlay-close-container',
                    '.ytp-ad-overlay-close-button-container',
                    '.ytp-ad-overlay-close-button-v2',
                    '.ytp-ad-overlay-image-container',
                    '.ytp-ad-overlay-text-container',
                    '.ytp-ad-overlay-link-container',
                    '.ytp-ad-overlay-duration-container',
                    '.ytp-ad-overlay-skip-container',
                    '.ytp-ad-overlay-preview-container',
                    '.ytp-ad-overlay-banner-container',
                    '[class*=""ad-container""]',
                    '[id*=""ad-container""]',
                    '[class*=""ytp-ad""]',
                    '[id*=""ytp-ad""]',
                    
                    // Banner and display ads
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-promoted-video-renderer',
                    '.ytd-banner-promo-renderer',
                    '.ytd-in-feed-ad-layout-renderer',
                    '.ytd-ad-slot-renderer',
                    '.ytd-promoted-sparkles-text-search-renderer',
                    '.ytd-search-promoted-sparkles-web-renderer',
                    '.ytd-compact-promoted-video-renderer',
                    '.ytd-display-ad-renderer',
                    '.ytd-search-promoted-item-renderer',
                    '.ytd-carousel-ad-renderer',
                    '.ytd-video-masthead-ad-renderer',
                    '.ytd-primetime-promo-renderer',
                    '.ytd-statement-banner-renderer',
                    '.ytd-brand-video-singleton-renderer',
                    '.ytd-brand-video-shelf-renderer',
                    '.ytd-player-legacy-desktop-watch-ads-renderer',
                    '.ytd-ads-engagement-panel-content-renderer',
                    '.ytd-structured-description-ad-content-renderer',
                    '[data-ad-status]',
                    '[data-is-ad=""true""]',
                    '[data-ad=""true""]',
                    '[data-promoted=""true""]',
                    '[data-is-sponsored=""true""]',
                    '[aria-label*=""Ad""]',
                    '[aria-label*=""Sponsored""]',
                    '[aria-label*=""Promotion""]',
                    '[title*=""Ad""]',
                    '[title*=""Sponsored""]',
                    '[title*=""Promotion""]',
                    
                    // Sidebar and companion ads
                    '#player-ads',
                    '#masthead-ad',
                    '#watch-sidebar-ads',
                    '#watch-channel-brand-div',
                    '#watch7-sidebar-ads',
                    '#watch-branded-actions',
                    '#watch7-branded-banner',
                    '#watch-branded-banner',
                    '#player-branded-banner',
                    '.ytd-companion-slot-renderer',
                    '.ytd-action-companion-ad-renderer',
                    '.companion-ad-container',
                    '.masthead-ad-container',
                    '.sidebar-ad-container',
                    '.watch-sidebar-ad-container',
                    '.branded-banner-container',
                    '.companion-slot-container',
                    '.action-companion-ad-container',
                    
                    // Premium and upgrade prompts
                    '.ytd-popup-container[dialog][role=""dialog""]',
                    '.ytd-mealbar-promo-renderer',
                    '.ytd-consent-bump-v2-lightbox',
                    '.ytd-consent-bump-v2-renderer',
                    '.ytd-single-option-survey-renderer',
                    '.ytd-multi-option-survey-renderer',
                    '.ytd-survey-renderer',
                    '.ytd-premium-upsell-renderer',
                    '.ytd-upsell-dialog-renderer',
                    '.ytd-offline-promo-renderer',
                    '.ytd-background-promo-renderer',
                    '.ytd-donation-shelf-renderer',
                    '.ytd-fundraiser-support-renderer',
                    '.ytd-membership-item-renderer',
                    '.ytd-paid-content-overlay-renderer',
                    '.ytd-player-overlay-renderer[overlay-style=""PAID_CONTENT""]',
                    '.ytd-player-overlay-autoplay-renderer',
                    '.ytd-clarification-renderer',
                    '.ytd-info-panel-content-renderer',
                    '.ytd-single-action-engagement-panel-content-renderer',
                    '.ytd-engagement-panel-section-list-renderer[target-id*=""engagement-panel-ads""]',
                    '.ytd-engagement-panel-section-list-renderer[target-id*=""engagement-panel-promoted""]',
                    '.ytd-engagement-panel-section-list-renderer[target-id*=""engagement-panel-sponsor""]',
                    
                    // Shopping and product ads
                    '.ytd-product-details-renderer',
                    '.ytd-merch-shelf-renderer',
                    '.ytd-shopping-shelf-renderer',
                    '.ytd-product-shelf-renderer',
                    '.ytd-shopping-carousel-renderer',
                    '.ytd-product-carousel-renderer',
                    '.ytd-shopping-offer-renderer',
                    '.ytd-product-offer-renderer',
                    '.ytd-shopping-generic-header-renderer',
                    '.ytd-product-generic-header-renderer',
                    
                    // Live chat ads and sponsorships
                    '.ytd-sponsorships-live-chat-header-renderer',
                    '.ytd-sponsorships-live-chat-gift-purchase-announcement-renderer',
                    '.ytd-sponsorships-live-chat-gift-redemption-announcement-renderer',
                    '.ytd-live-chat-paid-message-renderer',
                    '.ytd-live-chat-paid-sticker-renderer',
                    '.ytd-live-chat-membership-item-renderer',
                    '.ytd-live-chat-sponsor-renderer',
                    
                    // Shorts ads
                    '.ytd-reel-video-renderer[is-ad]',
                    '.ytd-shorts-video-renderer[is-ad]',
                    '.ytd-reel-item-renderer[is-ad]',
                    '.ytd-shorts-item-renderer[is-ad]',
                    
                    // Generic ad patterns
                    '[class*=""promoted""]',
                    '[class*=""sponsor""]',
                    '[class*=""advertisement""]',
                    '[id*=""promoted""]',
                    '[id*=""sponsor""]',
                    '[id*=""advertisement""]'
                ];
                
                const AD_SELECTOR_STRING = AD_SELECTORS.join(',');
                
                // Throttled DOM cleaner for better performance
                let cleanupTimeout = null;
                let mutationCount = 0;
                const MAX_MUTATIONS_PER_BATCH = 50;
                
                function throttledCleanup() {
                    if (cleanupTimeout) return;
                    
                    cleanupTimeout = setTimeout(() => {
                        try {
                            // Batch cleanup for better performance
                            const adElements = document.querySelectorAll(AD_SELECTOR_STRING);
                            let removedCount = 0;
                            
                            adElements.forEach(el => {
                                if (el.offsetParent !== null) { // Only remove visible elements
                                    el.remove();
                                    removedCount++;
                                }
                            });
                            
                            if (removedCount > 0) {
                                console.debug(`[AdBlocker] Removed ${removedCount} ad elements`);
                            }
                        } catch (error) {
                            console.warn('[AdBlocker] Error during cleanup:', error);
                        } finally {
                            cleanupTimeout = null;
                            mutationCount = 0;
                        }
                    }, 100); // 100ms throttle
                }
                
                // Optimized mutation observer with throttling
                const observer = new MutationObserver(function(mutations) {
                    mutationCount += mutations.length;
                    
                    // Quick inline check for new ad elements
                    let hasAds = false;
                    for (const mutation of mutations) {
                        for (const node of mutation.addedNodes) {
                            if (node.nodeType === 1 && 
                                (node.matches?.(AD_SELECTOR_STRING) || 
                                 node.querySelector?.(AD_SELECTOR_STRING))) {
                                hasAds = true;
                                break;
                            }
                        }
                        if (hasAds) break;
                    }
                    
                    // Only trigger cleanup if ads detected or mutation threshold reached
                    if (hasAds || mutationCount >= MAX_MUTATIONS_PER_BATCH) {
                        throttledCleanup();
                    }
                });
                
                // Start observing with optimized config
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: false, // Don't watch attribute changes for performance
                    characterData: false // Don't watch text changes
                });
                
                // Enhanced YouTube API overrides - 2024 Update
                function overrideYouTubeAPI() {
                    try {
                        // Override ad configuration
                        if (window.yt?.config_) {
                            const config = window.yt.config_;
                            config.EXPERIMENT_FLAGS = config.EXPERIMENT_FLAGS || {};
                            Object.assign(config.EXPERIMENT_FLAGS, {
                                // Core ad blocking flags
                                web_player_ads_control_config: { enabled: false },
                                html5_disable_av01_hdr: true,
                                web_player_move_autonav_toggle: true,
                                kevlar_dropdown_fix: true,
                                
                                // Enhanced 2024 ad blocking flags
                                disable_child_node_auto_formatted_strings: true,
                                disable_simple_mixed_direction_formatted_strings: true,
                                enable_server_stitched_dai: false,
                                html5_disable_preroll_ads: true,
                                html5_disable_midroll_ads: true,
                                html5_disable_postroll_ads: true,
                                html5_disable_overlay_ads: true,
                                html5_disable_companion_ads: true,
                                html5_disable_bumper_ads: true,
                                html5_disable_masthead_ads: true,
                                html5_disable_promoted_content: true,
                                html5_disable_sponsored_content: true,
                                html5_disable_brand_video: true,
                                html5_disable_trueview_ads: true,
                                html5_disable_video_ads: true,
                                html5_disable_display_ads: true,
                                html5_disable_banner_ads: true,
                                html5_disable_sidebar_ads: true,
                                html5_disable_search_ads: true,
                                html5_disable_shopping_ads: true,
                                html5_disable_product_ads: true,
                                html5_disable_live_chat_ads: true,
                                html5_disable_shorts_ads: true,
                                html5_disable_reel_ads: true,
                                html5_disable_premium_upsell: true,
                                html5_disable_membership_promo: true,
                                html5_disable_donation_shelf: true,
                                html5_disable_fundraiser_support: true,
                                html5_disable_survey_renderer: true,
                                html5_disable_consent_bump: true,
                                html5_disable_mealbar_promo: true,
                                html5_disable_popup_container: true,
                                html5_disable_clarification_renderer: true,
                                html5_disable_info_panel_content: true,
                                html5_disable_engagement_panel_ads: true,
                                html5_disable_structured_description_ads: true,
                                html5_disable_paid_content_overlay: true,
                                html5_disable_player_overlay_ads: true,
                                html5_disable_autoplay_ads: true,
                                html5_disable_background_promo: true,
                                html5_disable_offline_promo: true,
                                html5_disable_upsell_dialog: true,
                                html5_disable_primetime_promo: true,
                                html5_disable_statement_banner: true,
                                html5_disable_brand_video_singleton: true,
                                html5_disable_brand_video_shelf: true,
                                html5_disable_legacy_desktop_watch_ads: true,
                                html5_disable_ads_engagement_panel: true,
                                html5_disable_carousel_ads: true,
                                html5_disable_video_masthead_ads: true,
                                html5_disable_compact_promoted_video: true,
                                html5_disable_promoted_sparkles: true,
                                html5_disable_search_promoted_sparkles: true,
                                html5_disable_search_promoted_item: true,
                                html5_disable_display_ad_renderer: true,
                                html5_disable_ad_slot_renderer: true,
                                html5_disable_in_feed_ad_layout: true,
                                html5_disable_banner_promo_renderer: true,
                                html5_disable_promoted_video_renderer: true,
                                html5_disable_companion_slot_renderer: true,
                                html5_disable_action_companion_ad: true,
                                
                                // Additional experimental flags
                                web_playback_associated_log_ctt: false,
                                web_playback_associated_ve: false,
                                web_player_response_playback_tracking_parsing: false,
                                web_player_watch_next_response: false,
                                web_player_music_visualizer: false,
                                web_player_api_logging_fraction: 0.0,
                                web_player_ike_phase_1: false,
                                web_player_sentinel_is_uniplayer: false,
                                web_player_show_music_in_this_video_graphic: false,
                                web_player_music_visualizer_treatment: false,
                                web_player_api_logging_fraction: 0.0,
                                web_player_contextual_info_sampling_rate: 0.0,
                                web_player_watch_next_response_parsing: false,
                                web_player_response_playback_tracking_parsing: false,
                                web_playback_associated_log_ctt: false,
                                web_playback_associated_ve: false,
                                
                                // Tracking and analytics disabling
                                html5_log_audio_abr: false,
                                html5_log_experiment_id_from_player_response_to_ctmp: false,
                                html5_log_readahead_on_playback_start: false,
                                html5_log_rebuffer_events: false,
                                html5_log_request_time_from_ctmp: false,
                                html5_log_video_abr: false,
                                html5_log_watch_time: false,
                                html5_player_log_click_before_polymer_upgrade: false,
                                html5_player_log_js_exceptions: false,
                                html5_rewrite_manifestless_for_sync_lookback: false,
                                html5_track_audio_quality: false,
                                html5_track_video_quality: false,
                                html5_unreported_seek_reseek_delay_count: 0,
                                html5_use_adaptive_live_readahead: false,
                                html5_use_av1_hdr: false,
                                html5_use_multitag_webm_tags: false,
                                html5_use_performance_logger: false,
                                html5_use_spine_manifest: false,
                                html5_use_video_overlay_for_persistent_audio: false,
                                html5_video_tbd_min_kb: 0,
                                html5_viewport_underscan_vertical_threshold: 0.0,
                                html5_vp9_hdr: false,
                                html5_webpo_idle_priority_job: false,
                                html5_woffle_resume: false,
                                html5_workaround_delay_trigger: false,
                                html5_ytp3p_preloaded_shaka_player: false,
                                html5_ytpo_token_header_passthrough: false
                            });
                        }
                        
                        // Override player ads
                        if (window.ytplayer?.config?.args) {
                            const args = window.ytplayer.config.args;
                            args.fflags = (args.fflags || '') + 
                                '&html5_disable_av01_hdr=true' +
                                '&html5_disable_preroll_ads=true' +
                                '&html5_disable_midroll_ads=true' +
                                '&html5_disable_postroll_ads=true' +
                                '&html5_disable_overlay_ads=true' +
                                '&html5_disable_companion_ads=true' +
                                '&html5_disable_bumper_ads=true' +
                                '&html5_disable_masthead_ads=true' +
                                '&html5_disable_promoted_content=true' +
                                '&html5_disable_sponsored_content=true' +
                                '&html5_disable_brand_video=true' +
                                '&html5_disable_trueview_ads=true' +
                                '&html5_disable_video_ads=true' +
                                '&html5_disable_display_ads=true' +
                                '&html5_disable_banner_ads=true' +
                                '&html5_disable_sidebar_ads=true' +
                                '&html5_disable_search_ads=true' +
                                '&html5_disable_shopping_ads=true' +
                                '&html5_disable_product_ads=true' +
                                '&html5_disable_live_chat_ads=true' +
                                '&html5_disable_shorts_ads=true' +
                                '&html5_disable_reel_ads=true';
                                
                            // Remove ad-related parameters
                            delete args.ad_device;
                            delete args.ad_flags;
                            delete args.ad_logging_flag;
                            delete args.ad_preroll;
                            delete args.ad_tag;
                            delete args.adsense_video_doc_id;
                            delete args.advideo;
                            delete args.afv;
                            delete args.afv_ad_tag;
                            delete args.afv_ad_tag_restricted_to_instream;
                            delete args.afv_instream_max;
                            delete args.allow_below_the_player_companion;
                            delete args.allow_html5_ads;
                            delete args.allow_live_autoplay;
                            delete args.allow_ratings;
                            delete args.allow_embed;
                            delete args.autoplay;
                            delete args.cc3_module;
                            delete args.cos;
                            delete args.csi_page_type;
                            delete args.cver;
                            delete args.enabled_engage_types;
                            delete args.external_play_video;
                            delete args.feature;
                            delete args.fflags;
                            delete args.gapi_hint_params;
                            delete args.hl;
                            delete args.host_language;
                            delete args.innertube_api_key;
                            delete args.innertube_api_version;
                            delete args.innertube_context_client_version;
                            delete args.is_listed;
                            delete args.jsapicallback;
                            delete args.keywords;
                            delete args.length_seconds;
                            delete args.list;
                            delete args.loaderUrl;
                            delete args.loudness;
                            delete args.midroll_freqcap;
                            delete args.midroll_prefetch_size;
                            delete args.muted;
                            delete args.oid;
                            delete args.origin;
                            delete args.player_response;
                            delete args.playertype;
                            delete args.plid;
                            delete args.pltype;
                            delete args.ptchn;
                            delete args.ptk;
                            delete args.q;
                            delete args.rel;
                            delete args.remarketing_url;
                            delete args.require_js_api;
                            delete args.rm;
                            delete args.sourceid;
                            delete args.ssl;
                            delete args.t;
                            delete args.tag_for_child_directed_treatment;
                            delete args.tag_for_under_age_of_consent;
                            delete args.timestamp;
                            delete args.title;
                            delete args.token;
                            delete args.track;
                            delete args.ucid;
                            delete args.url_encoded_fmt_stream_map;
                            delete args.use_cipher_signature;
                            delete args.use_fast_sizing_on_watch_default;
                            delete args.user_age;
                            delete args.videostats_playback_base_url;
                            delete args.view_count;
                            delete args.vm;
                            delete args.watermark;
                            delete args.wide;
                        }
                        
                        // Override global YouTube configuration
                        if (window.ytInitialData) {
                            // Remove ads from initial data
                            const removeAdsFromObject = (obj) => {
                                if (typeof obj !== 'object' || obj === null) return;
                                
                                for (const key in obj) {
                                    if (key.toLowerCase().includes('ad') || 
                                        key.toLowerCase().includes('promoted') ||
                                        key.toLowerCase().includes('sponsor')) {
                                        delete obj[key];
                                    } else if (typeof obj[key] === 'object') {
                                        removeAdsFromObject(obj[key]);
                                    }
                                }
                            };
                            
                            removeAdsFromObject(window.ytInitialData);
                        }
                        
                        // Override player creation
                        if (window.YT && window.YT.Player) {
                            const originalPlayer = window.YT.Player;
                            window.YT.Player = function(elementId, config) {
                                if (config && config.playerVars) {
                                    // Disable ads in player configuration
                                    config.playerVars.iv_load_policy = 3;
                                    config.playerVars.modestbranding = 1;
                                    config.playerVars.rel = 0;
                                    config.playerVars.showinfo = 0;
                                    config.playerVars.fs = 1;
                                    config.playerVars.cc_load_policy = 0;
                                    config.playerVars.disablekb = 0;
                                    config.playerVars.autohide = 2;
                                    config.playerVars.theme = 'dark';
                                    config.playerVars.color = 'red';
                                    config.playerVars.controls = 1;
                                    config.playerVars.playsinline = 1;
                                    config.playerVars.enablejsapi = 1;
                                    config.playerVars.origin = window.location.origin;
                                    config.playerVars.widget_referrer = window.location.href;
                                    
                                    // Remove ad-related parameters
                                    delete config.playerVars.ad_device;
                                    delete config.playerVars.ad_flags;
                                    delete config.playerVars.ad_logging_flag;
                                    delete config.playerVars.ad_preroll;
                                    delete config.playerVars.ad_tag;
                                    delete config.playerVars.adsense_video_doc_id;
                                    delete config.playerVars.advideo;
                                    delete config.playerVars.afv;
                                    delete config.playerVars.afv_ad_tag;
                                    delete config.playerVars.afv_ad_tag_restricted_to_instream;
                                    delete config.playerVars.afv_instream_max;
                                    delete config.playerVars.allow_below_the_player_companion;
                                    delete config.playerVars.allow_html5_ads;
                                }
                                
                                return new originalPlayer(elementId, config);
                            };
                            
                            // Copy static properties
                            Object.setPrototypeOf(window.YT.Player, originalPlayer);
                            Object.getOwnPropertyNames(originalPlayer).forEach(name => {
                                if (name !== 'length' && name !== 'name' && name !== 'prototype') {
                                    window.YT.Player[name] = originalPlayer[name];
                                }
                            });
                        }
                        
                    } catch (error) {
                        console.warn('[AdBlocker] API override error:', error);
                    }
                }
                
                // Intelligent ad skip with backoff
                let skipAttempts = 0;
                const MAX_SKIP_ATTEMPTS = 3;
                let skipBackoffDelay = 500;
                
                function smartAdSkip() {
                    const skipButton = document.querySelector(
                        '.ytp-ad-skip-button, .ytp-skip-ad-button, .ytp-ad-skip-button-modern'
                    );
                    
                    if (skipButton?.offsetParent !== null && skipButton.click) {
                        skipButton.click();
                        skipAttempts++;
                        
                        if (skipAttempts >= MAX_SKIP_ATTEMPTS) {
                            skipBackoffDelay = Math.min(skipBackoffDelay * 2, 5000); // Max 5s
                        }
                        
                        console.debug('[AdBlocker] Ad skip attempted');
                        return true;
                    }
                    return false;
                }
                
                // Adaptive skip interval based on success rate
                let skipInterval = setInterval(() => {
                    if (smartAdSkip()) {
                        // Reset attempts on successful skip
                        skipAttempts = 0;
                        skipBackoffDelay = 500;
                    }
                }, skipBackoffDelay);
                
                // Enhanced fetch blocking
                const originalFetch = window.fetch;
                const originalXHR = window.XMLHttpRequest.prototype.open;
                
                const adUrlPatterns = [
                    /\/pagead\//,
                    /googleads/,
                    /doubleclick/,
                    /googlesyndication/,
                    /googletagservices/,
                    /youtube\.com\/ptracking/,
                    /youtube\.com\/api\/stats/,
                    /get_midroll_info/,
                    /generate_204/
                ];
                
                function shouldBlockURL(url) {
                    return adUrlPatterns.some(pattern => pattern.test(url));
                }
                
                // Override fetch
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && shouldBlockURL(url)) {
                        return Promise.reject(new Error('[AdBlocker] Request blocked'));
                    }
                    return originalFetch.apply(this, args);
                };
                
                // Override XMLHttpRequest
                XMLHttpRequest.prototype.open = function(method, url, ...rest) {
                    if (typeof url === 'string' && shouldBlockURL(url)) {
                        console.debug('[AdBlocker] XHR blocked:', url);
                        // Return a dummy response instead of throwing
                        this.status = 204;
                        this.statusText = 'Blocked';
                        return;
                    }
                    return originalXHR.apply(this, arguments);
                };
                
                // Initialize
                overrideYouTubeAPI();
                
                // Re-apply overrides after navigation
                let lastUrl = location.href;
                setInterval(() => {
                    if (location.href !== lastUrl) {
                        lastUrl = location.href;
                        setTimeout(overrideYouTubeAPI, 1000);
                    }
                }, 2000);
                
                // Initial cleanup
                setTimeout(throttledCleanup, 500);
                
                console.log('[AdBlocker] Enhanced YouTube ad blocker activated');
            })();
        ";
    }

    private static readonly HashSet<string> StaticBlockedDomains = new(StringComparer.OrdinalIgnoreCase)
    {
        // Google Ads - Enhanced 2024
        "googleads.g.doubleclick.net",
        "googlesyndication.com", 
        "googletagservices.com",
        "doubleclick.net",
        "googleadservices.com",
        "google-analytics.com",
        "googletagmanager.com",
        "googletag.com",
        "gstatic.com/ads",
        "gstatic.com/pagead",
        "gstatic.com/doubleclick",
        "gstatic.com/adsense",
        "gstatic.com/adx",
        "gstatic.com/afma",
        "gstatic.com/admob",
        "gstatic.com/adnxs",
        "gstatic.com/adsystem",
        "gstatic.com/adservice",
        "gstatic.com/adserver",
        "gstatic.com/adchoices",
        "gstatic.com/adnw",
        "gstatic.com/adimg",
        "gstatic.com/adx1",
        "gstatic.com/adx2",
        "gstatic.com/adx3",
        "gstatic.com/adx4",
        "gstatic.com/adx5",
        "pagead2.googlesyndication.com",
        "pagead.googlesyndication.com",
        "partner.googleadservices.com",
        "tpc.googlesyndication.com",
        "www.googleadservices.com",
        "www.google-analytics.com",
        "www.googletagmanager.com",
        "www.googletagservices.com",
        "www.googlesyndication.com",
        "ssl.google-analytics.com",
        "stats.g.doubleclick.net",
        "cm.g.doubleclick.net",
        "ad.doubleclick.net",
        "ads.doubleclick.net",
        "bid.g.doubleclick.net",
        "pubads.g.doubleclick.net",
        "securepubads.g.doubleclick.net",
        "static.doubleclick.net",
        "m.doubleclick.net",
        "mediavisor.doubleclick.net",
        "n4403ad.doubleclick.net",
        "fls.doubleclick.net",
        "ad-delivery.net",
        "adsystem.amazon.com",
        "amazon-adsystem.com",
        "s.amazon-adsystem.com",
        "c.amazon-adsystem.com",
        "aax.amazon-adsystem.com",
        "aax-us-east.amazon-adsystem.com",
        "aax-us-west.amazon-adsystem.com",
        "aax-eu.amazon-adsystem.com",
        "aax-fe.amazon-adsystem.com",
        
        // YouTube specific - Enhanced
        "youtube.com/pagead",
        "youtube.com/ptracking", 
        "youtube.com/api/stats",
        "youtube.com/youtubei/v1/log_event",
        "youtube.com/youtubei/v1/player/ad_break",
        "youtube.com/youtubei/v1/next",
        "youtube.com/youtubei/v1/browse",
        "youtube.com/api/stats/ads",
        "youtube.com/api/stats/atr",
        "youtube.com/api/stats/qoe",
        "youtube.com/generate_204",
        "youtube.com/pcs/activeview",
        "youtube.com/pagead/interaction",
        "youtube.com/pagead/adview",
        "youtube.com/pagead/conversion",
        "youtube.com/ads/measurement",
        "youtube.com/doubleclick/instream/ad_status",
        "youtube.com/video_ads_serving",
        "youtube.com/ad_break",
        "youtube.com/midroll_ad",
        "youtube.com/preroll_ad",
        "youtube.com/postroll_ad",
        "youtube.com/companion_ad",
        "youtube.com/overlay_ad",
        "youtube.com/bumper_ad",
        "youtube.com/masthead_ad",
        "youtube.com/promoted_content",
        "youtube.com/sponsored_content",
        "youtube.com/brand_video",
        "youtube.com/trueview",
        "s.youtube.com/api/stats",
        "www.youtube.com/pagead",
        "www.youtube.com/ptracking",
        "www.youtube.com/api/stats",
        "m.youtube.com/pagead",
        "m.youtube.com/ptracking",
        "m.youtube.com/api/stats",
        
        // Facebook/Meta Ads
        "facebook.com/tr",
        "www.facebook.com/tr",
        "connect.facebook.net",
        "www.connect.facebook.net",
        "pixel.facebook.com",
        "analytics.facebook.com",
        "graph.facebook.com/insights",
        "business.facebook.com/tr",
        "developers.facebook.com/tr",
        "instagram.com/logging",
        "instagram.com/api/v1/ads",
        "instagram.com/api/v1/insights",
        "whatsapp.com/business/api/ads",
        "audience-network.com",
        "fbcdn.net/ads",
        "fbsbx.com/ads",
        "facebook.net/ads",
        "fb.com/tr",
        "fb.me/ads",
        "messenger.com/ads",
        "workplace.com/ads",
        "oculus.com/ads",
        "threads.net/ads",
        "meta.com/ads",
        
        // TikTok/ByteDance Ads
        "analytics.tiktok.com",
        "ads.tiktok.com",
        "business-api.tiktok.com",
        "ads-api.tiktok.com",
        "analytics-sg.tiktok.com",
        "analytics-va.tiktok.com",
        "business.tiktok.com",
        "ads.bytedance.com",
        "ad.bytedance.com",
        "analytics.bytedance.com",
        "business.bytedance.com",
        "pangle.io",
        "pangolin-sdk-toutiao.com",
        "pangolin-sdk-toutiao-b.com",
        "toblog.ctobsnssdk.com",
        "mon.toutiaoapi.com",
        "mon.snssdk.com",
        "mon.isnssdk.com",
        "log.snssdk.com",
        "log.byteoversea.com",
        "applog.musical.ly",
        "va.tiktokv.com",
        "sg.tiktokv.com",
        
        // Microsoft/Bing Ads
        "bat.bing.com",
        "ads.bing.com",
        "bingads.microsoft.com",
        "ads.microsoft.com",
        "bing.com/ads",
        "msn.com/ads",
        "outlook.com/ads",
        "hotmail.com/ads",
        "live.com/ads",
        "xbox.com/ads",
        "skype.com/ads",
        "linkedin.com/ads",
        "microsoftadvertising.com",
        "adnxs.com",
        "adsystem.microsoft.com",
        "c.bing.com",
        "r.bing.com",
        "t.bing.com",
        "choice.microsoft.com",
        "c.msn.com",
        "rad.msn.com",
        "ads1.msn.com",
        "ads2.msn.com",
        "flex.msn.com",
        "ac3.msn.com",
        
        // Twitter/X Ads
        "ads-api.twitter.com",
        "analytics.twitter.com",
        "business.twitter.com",
        "ads.twitter.com",
        "twitter.com/i/ads",
        "t.co/ads",
        "twimg.com/ads",
        "ads-twitter.com",
        "advertising.twitter.com",
        "business.x.com",
        "ads.x.com",
        "analytics.x.com",
        "x.com/i/ads",
        
        // Amazon Ads - Enhanced
        "adsystem.amazon.com",
        "amazon-adsystem.com",
        "s.amazon-adsystem.com",
        "c.amazon-adsystem.com",
        "aax.amazon-adsystem.com",
        "aax-us-east.amazon-adsystem.com",
        "aax-us-west.amazon-adsystem.com",
        "aax-eu.amazon-adsystem.com",
        "aax-fe.amazon-adsystem.com",
        "aan.amazon.com",
        "advertising.amazon.com",
        "kdp.amazon.com/ads",
        "merch.amazon.com/ads",
        "affiliate-program.amazon.com",
        "associates.amazon.com",
        "dsp.amazon.com",
        "advertising-api.amazon.com",
        "advertising-api-eu.amazon.com",
        "advertising-api-fe.amazon.com",
        "rcm-images.amazon.com",
        "fls-na.amazon.com",
        "fls-eu.amazon.com",
        "fls-fe.amazon.com",
        "completion.amazon.com",
        "mads.amazon.com",
        "unagi.amazon.com",
        "unagi-na.amazon.com",
        "unagi-eu.amazon.com",
        "unagi-fe.amazon.com",
        
        // Content recommendation and native ads
        "outbrain.com",
        "widgets.outbrain.com",
        "paid.outbrain.com",
        "amplify.outbrain.com",
        "log.outbrain.com",
        "odb.outbrain.com",
        "tr.outbrain.com",
        "storage.googleapis.com/outbrain-images",
        "taboola.com",
        "cdn.taboola.com",
        "images.taboola.com",
        "trc.taboola.com",
        "api.taboola.com",
        "backstage.taboola.com",
        "console.taboola.com",
        "trends.taboola.com",
        "newsroom.taboola.com",
        "blog.taboola.com",
        "help.taboola.com",
        "revealmobile.com",
        "revcontent.com",
        "trends.revcontent.com",
        "labs.revcontent.com",
        "performance.revcontent.com",
        "img.revcontent.com",
        "cdn.revcontent.com",
        "api.revcontent.com",
        "mgid.com",
        "cdn.mgid.com",
        "jsc.mgid.com",
        "servicer.mgid.com",
        "cm.mgid.com",
        "c.mgid.com",
        "native.mgid.com",
        "widgets.mgid.com",
        
        // Ad verification and fraud detection
        "adsafeprotected.com",
        "fw.adsafeprotected.com",
        "pixel.adsafeprotected.com",
        "static.adsafeprotected.com",
        "cdn.adsafeprotected.com",
        "dt.adsafeprotected.com",
        "moatads.com",
        "z.moatads.com",
        "px.moatads.com",
        "js.moatads.com",
        "v4.moatads.com",
        "v5.moatads.com",
        "sejs.moatads.com",
        "sv.moatads.com",
        "p.moatads.com",
        "s.moatads.com",
        "y.moatads.com",
        "scorecardresearch.com",
        "sb.scorecardresearch.com",
        "udm.scorecardresearch.com",
        "beacon.scorecardresearch.com",
        "quantserve.com",
        "pixel.quantserve.com",
        "secure.quantserve.com",
        "rules.quantcount.com",
        "edge.quantserve.com",
        "doubleverify.com",
        "cdn.doubleverify.com",
        "tps.doubleverify.com",
        "rtb0.doubleverify.com",
        "rtb1.doubleverify.com",
        "rtb2.doubleverify.com",
        "integralads.com",
        "pixel.integralads.com",
        "cdn.integralads.com",
        "secure-gl.imrworldwide.com",
        "secure-us.imrworldwide.com",
        "secure-eu.imrworldwide.com",
        "secure-apac.imrworldwide.com",
        "comscore.com",
        "sb.comscore.com",
        "udm.comscore.com",
        "beacon.comscore.com",
        "nielsen.com/digitalsdk",
        "secure-dcr.imrworldwide.com",
        "secure-gg.imrworldwide.com",
        
        // Programmatic advertising platforms
        "criteo.com",
        "cas.criteo.com",
        "gum.criteo.com",
        "dis.criteo.com",
        "sslwidget.criteo.com",
        "widget.criteo.com",
        "static.criteo.net",
        "rtax.criteo.com",
        "bidder.criteo.com",
        "gpp.criteo.com",
        "privacy.criteo.com",
        "pubmatic.com",
        "ads.pubmatic.com",
        "image2.pubmatic.com",
        "image4.pubmatic.com",
        "image6.pubmatic.com",
        "image8.pubmatic.com",
        "showads.pubmatic.com",
        "ow.pubmatic.com",
        "t.pubmatic.com",
        "rubiconproject.com",
        "ads.rubiconproject.com",
        "pixel.rubiconproject.com",
        "tap.rubiconproject.com",
        "tap2-cdn.rubiconproject.com",
        "eus.rubiconproject.com",
        "optimized-by.rubiconproject.com",
        "fastlane.rubiconproject.com",
        "openx.net",
        "ox-d.openx.net",
        "d.openx.net",
        "i.openx.net",
        "u.openx.net",
        "w.openx.net",
        "rtb.openx.net",
        "ads.openx.net",
        "openx.com",
        "servedby.openx.com",
        "ox-d.openx.com",
        "appnexus.com",
        "adnxs.com",
        "ib.adnxs.com",
        "nym1.ib.adnxs.com",
        "lax1.ib.adnxs.com",
        "fra1.ib.adnxs.com",
        "sin1.ib.adnxs.com",
        "hkg1.ib.adnxs.com",
        "acdn.adnxs.com",
        "cdn.adnxs.com",
        "mediation.adnxs.com",
        "mobile.adnxs.com",
        "prebid.adnxs.com",
        "console.adnxs.com",
        "invest.adnxs.com",
        "wiki.adnxs.com",
        "learn.adnxs.com",
        
        // Supply-side platforms (SSP)
        "indexexchange.com",
        "js-sec.indexww.com",
        "as-sec.casalemedia.com",
        "ssum-sec.casalemedia.com",
        "htlb.casalemedia.com",
        "ads.adaptv.advertising.com",
        "match.adsystem.com",
        "c1.adform.net",
        "s1.adform.net",
        "s2.adform.net",
        "track.adform.net",
        "banners.adform.net",
        "server.adform.net",
        "adx.adform.net",
        "go.adform.net",
        "cm.adform.net",
        "ads.yahoo.com",
        "global.ard.yahoo.com",
        "udc.yahoo.com",
        "partnerads.ysm.yahoo.com",
        "ads.yap.yahoo.com",
        "advertising.yahoo.com",
        "gemini.yahoo.com",
        "flurry.com",
        "data.flurry.com",
        "ads.flurry.com",
        "y.analytics.yahoo.com",
        "sp.analytics.yahoo.com",
        "geo.yahoo.com",
        "beap.gemini.yahoo.com",
        "adserver.yahoo.com",
        "yads.yahoo.com",
        "yads.c.yimg.jp",
        "promotions.yahoo.com",
        "clicks.beap.bc.yahoo.com",
        "us.bc.yahoo.com",
        "clicks.beap.bc.yahoo.com",
        "s.yimg.com/rq/darla",
        "s.yimg.com/wi/ytc.js",
        "l.yimg.com/wi/ytc.js",
        
        // Mobile advertising networks
        "unity3d.com/ads",
        "unityads.unity3d.com",
        "config.unityads.unity3d.com",
        "webview.unityads.unity3d.com",
        "auction.unityads.unity3d.com",
        "publisher-event-tracker.unityads.unity3d.com",
        "cdp.cloud.unity3d.com",
        "remote-config-proxy-prd.uca.cloud.unity3d.com",
        "thind-gke-euw.prd.data.corp.unity3d.com",
        "thind-gke-usc.prd.data.corp.unity3d.com",
        "admob.com",
        "media.admob.com",
        "pagead2.googlesyndication.com/pagead/gen_204",
        "googleads.g.doubleclick.net/pagead/ads",
        "googleads.g.doubleclick.net/pagead/conversion",
        "www.googleadservices.com/pagead/conversion",
        "googleadservices.com/pagead/conversion",
        "google.com/ads/ga-audiences",
        "adservice.google.com",
        "adservice.google.ca",
        "adservice.google.co.uk",
        "adservice.google.de",
        "adservice.google.fr",
        "adservice.google.it",
        "adservice.google.es",
        "adservice.google.com.br",
        "adservice.google.co.jp",
        "adservice.google.com.au",
        "adservice.google.co.in",
        "adservice.google.com.mx",
        "adservice.google.com.ar",
        "adservice.google.co.za",
        "adservice.google.ru",
        "adservice.google.com.tr",
        "adservice.google.pl",
        "adservice.google.nl",
        "adservice.google.se",
        "adservice.google.no",
        "adservice.google.dk",
        "adservice.google.fi",
        "adservice.google.be",
        "adservice.google.at",
        "adservice.google.ch",
        "adservice.google.ie",
        "adservice.google.pt",
        "adservice.google.gr",
        "adservice.google.cz",
        "adservice.google.hu",
        "adservice.google.ro",
        "adservice.google.bg",
        "adservice.google.hr",
        "adservice.google.sk",
        "adservice.google.si",
        "adservice.google.lt",
        "adservice.google.lv",
        "adservice.google.ee",
        
        // Video advertising platforms
        "spotxchange.com",
        "search.spotxchange.com",
        "www.spotx.tv",
        "js.spotx.tv",
        "cdn.spotxcdn.com",
        "www.spotxchange.com",
        "console.spotxchange.com",
        "ads.spotxchange.com",
        "sync.search.spotxchange.com",
        "tremormedia.com",
        "ads.tremormedia.com",
        "tag.tremormedia.com",
        "cdn.tremormedia.com",
        "ads.adaptv.advertising.com",
        "cdn.adaptv.advertising.com",
        "ads.yahoo.com/adaptv",
        "brightroll.com",
        "ads.brightroll.com",
        "cdn.brightroll.com",
        "s.brightroll.com",
        "tag.brightroll.com",
        "ads.yahoo.com/brightroll",
        "innovid.com",
        "s-static.innovid.com",
        "s.innovid.com",
        "cdn.innovid.com",
        "tag.innovid.com",
        "ads.innovid.com",
        "tracking.innovid.com",
        "liverail.com",
        "cdn-ssl.liverail.com",
        "cdn-gl.liverail.com",
        "ads.liverail.com",
        "tag.liverail.com",
        "t.liverail.com",
        "freewheel.tv",
        "cdn.freewheel.tv",
        "ads.freewheel.tv",
        "mfw.freewheel.tv",
        "g1.freewheel.tv",
        "g2.freewheel.tv",
        "g3.freewheel.tv",
        "g4.freewheel.tv",
        "g5.freewheel.tv",
        "g6.freewheel.tv",
        "g7.freewheel.tv",
        "g8.freewheel.tv",
        "g9.freewheel.tv",
        "g10.freewheel.tv",
        
        // Retargeting and audience platforms
        "rlcdn.com",
        "d.rlcdn.com",
        "ib.rlcdn.com",
        "tags.rlcdn.com",
        "c.rlcdn.com",
        "s.rlcdn.com",
        "p.rlcdn.com",
        "t.rlcdn.com",
        "w.rlcdn.com",
        "x.rlcdn.com",
        "y.rlcdn.com",
        "z.rlcdn.com",
        "bluekai.com",
        "tags.bluekai.com",
        "stags.bluekai.com",
        "bkrtx.com",
        "oracle.com/cx/audience",
        "addthis.com",
        "s7.addthis.com",
        "cache.addthiscdn.com",
        "m.addthis.com",
        "ct1.addthis.com",
        "s9.addthis.com",
        "v1.addthisedge.com",
        "v3.addthisedge.com",
        "sharethis.com",
        "t.sharethis.com",
        "l.sharethis.com",
        "ws.sharethis.com",
        "seg.sharethis.com",
        "c.sharethis.com",
        "buttons-config.sharethis.com",
        "platform-api.sharethis.com",
        "count-server.sharethis.com",
        "wd-edge.sharethis.com",
        "gen204.sharethis.com",
        "p.sharethis.com",
        "r.sharethis.com",
        "s.sharethis.com",
        "w.sharethis.com",
        "x.sharethis.com",
        "y.sharethis.com",
        "z.sharethis.com",
        
        // Additional tracking and analytics
        "hotjar.com",
        "static.hotjar.com",
        "script.hotjar.com",
        "surveys.hotjar.com",
        "insights.hotjar.com",
        "careers.hotjar.com",
        "help.hotjar.com",
        "mixpanel.com",
        "api.mixpanel.com",
        "cdn.mxpnl.com",
        "decide.mixpanel.com",
        "track.mixpanel.com",
        "data.mixpanel.com",
        "engage.mixpanel.com",
        "people.mixpanel.com",
        "amplitude.com",
        "api.amplitude.com",
        "api2.amplitude.com",
        "cdn.amplitude.com",
        "analytics.amplitude.com",
        "cohort-v2.amplitude.com",
        "p.typekit.net",
        "use.typekit.net",
        "use.typekit.com",
        "p.typekit.com",
        "typekit.com/embed",
        "fonts.googleapis.com/css",
        "fonts.gstatic.com",
        "ajax.googleapis.com/ajax/libs/webfont",
        "webfonts.googleapis.com",
        "fonts-api.googleapis.com",
        "googlefonts.googleapis.com",
        "fonts.google.com",
        "optimize.google.com",
        "www.google-analytics.com/gtm/optimize",
        "www.googleoptimize.com",
        "ssl.google-analytics.com/gtm/optimize",
        "tagmanager.google.com",
        "www.googletagmanager.com/gtm.js",
        "www.googletagmanager.com/gtag/js",
        "googletagmanager.com/gtm.js",
        "googletagmanager.com/gtag/js",
        "google-analytics.com/gtm/js",
        "google-analytics.com/gtag/js",
        "google-analytics.com/analytics.js",
        "google-analytics.com/ga.js",
        "ssl.google-analytics.com/ga.js",
        "ssl.google-analytics.com/analytics.js",
        "www.google-analytics.com/analytics.js",
        "www.google-analytics.com/ga.js",
        "stats.wp.com",
        "pixel.wp.com",
        "widgets.wp.com",
        "s0.wp.com",
        "s1.wp.com",
        "s2.wp.com",
        "i0.wp.com",
        "i1.wp.com",
        "i2.wp.com",
        "c0.wp.com",
        "c1.wp.com",
        "c2.wp.com",
        "gravatar.com/avatar",
        "secure.gravatar.com/avatar",
        "0.gravatar.com/avatar",
        "1.gravatar.com/avatar",
        "2.gravatar.com/avatar",
        "en.gravatar.com/avatar",
        "s.gravatar.com/avatar",
        "www.gravatar.com/avatar",
        "jetpack.wordpress.com",
        "stats.jetpack.wordpress.com",
        "pixel.jetpack.wordpress.com"
    };

    private static readonly CompiledRegex[] StaticUrlPatterns = new[]
    {
        // Core ad serving patterns - Enhanced 2024
        new CompiledRegex(@"/pagead/", "Page ads"),
        new CompiledRegex(@"/ads\?", "Ad queries"),
        new CompiledRegex(@"/ad\?", "Ad queries short"),
        new CompiledRegex(@"[&?]ad_type=", "Ad type parameter"),
        new CompiledRegex(@"[&?]adurl=", "Ad URL parameter"),
        new CompiledRegex(@"[&?]ad_tag=", "Ad tag parameter"),
        new CompiledRegex(@"[&?]ad_system=", "Ad system parameter"),
        new CompiledRegex(@"[&?]ad_pod=", "Ad pod parameter"),
        new CompiledRegex(@"[&?]ad_creative=", "Ad creative parameter"),
        new CompiledRegex(@"[&?]ad_campaign=", "Ad campaign parameter"),
        new CompiledRegex(@"[&?]ad_advertiser=", "Ad advertiser parameter"),
        new CompiledRegex(@"[&?]ad_break_type=", "Ad break type parameter"),
        new CompiledRegex(@"[&?]ad_format=", "Ad format parameter"),
        new CompiledRegex(@"[&?]ad_skippable=", "Ad skippable parameter"),
        new CompiledRegex(@"[&?]ad_duration=", "Ad duration parameter"),
        new CompiledRegex(@"[&?]ad_position=", "Ad position parameter"),
        new CompiledRegex(@"[&?]ad_sequence=", "Ad sequence parameter"),
        new CompiledRegex(@"[&?]ad_break_start=", "Ad break start parameter"),
        new CompiledRegex(@"[&?]ad_break_end=", "Ad break end parameter"),
        new CompiledRegex(@"[&?]ad_impression=", "Ad impression parameter"),
        new CompiledRegex(@"[&?]ad_click=", "Ad click parameter"),
        new CompiledRegex(@"[&?]ad_view=", "Ad view parameter"),
        new CompiledRegex(@"[&?]ad_complete=", "Ad complete parameter"),
        new CompiledRegex(@"[&?]ad_skip=", "Ad skip parameter"),
        new CompiledRegex(@"[&?]ad_mute=", "Ad mute parameter"),
        new CompiledRegex(@"[&?]ad_unmute=", "Ad unmute parameter"),
        new CompiledRegex(@"[&?]ad_pause=", "Ad pause parameter"),
        new CompiledRegex(@"[&?]ad_resume=", "Ad resume parameter"),
        new CompiledRegex(@"[&?]ad_fullscreen=", "Ad fullscreen parameter"),
        new CompiledRegex(@"[&?]ad_exitfullscreen=", "Ad exit fullscreen parameter"),
        new CompiledRegex(@"[&?]ad_volume_change=", "Ad volume change parameter"),
        new CompiledRegex(@"[&?]ad_progress=", "Ad progress parameter"),
        new CompiledRegex(@"[&?]ad_quartile=", "Ad quartile parameter"),
        new CompiledRegex(@"[&?]ad_midpoint=", "Ad midpoint parameter"),
        new CompiledRegex(@"[&?]ad_third_quartile=", "Ad third quartile parameter"),
        new CompiledRegex(@"[&?]ad_error=", "Ad error parameter"),
        new CompiledRegex(@"[&?]ad_loaded=", "Ad loaded parameter"),
        new CompiledRegex(@"[&?]ad_started=", "Ad started parameter"),
        new CompiledRegex(@"[&?]ad_first_quartile=", "Ad first quartile parameter"),
        
        // Google Ads patterns - Enhanced
        new CompiledRegex(@"googleads\.g\.doubleclick\.net", "Google DoubleClick"),
        new CompiledRegex(@"googlesyndication\.com", "Google Syndication"),
        new CompiledRegex(@"googletagservices\.com", "Google Tag Services"),
        new CompiledRegex(@"googleadservices\.com", "Google Ad Services"),
        new CompiledRegex(@"doubleclick\.net", "DoubleClick"),
        new CompiledRegex(@"google-analytics\.com", "Google Analytics"),
        new CompiledRegex(@"googletagmanager\.com", "Google Tag Manager"),
        new CompiledRegex(@"googletag\.com", "Google Tag"),
        new CompiledRegex(@"gstatic\.com/ads", "Google Static Ads"),
        new CompiledRegex(@"gstatic\.com/pagead", "Google Static Page Ads"),
        new CompiledRegex(@"gstatic\.com/doubleclick", "Google Static DoubleClick"),
        new CompiledRegex(@"gstatic\.com/adsense", "Google Static AdSense"),
        new CompiledRegex(@"gstatic\.com/adx", "Google Static AdX"),
        new CompiledRegex(@"gstatic\.com/afma", "Google Static AFMA"),
        new CompiledRegex(@"gstatic\.com/admob", "Google Static AdMob"),
        new CompiledRegex(@"gstatic\.com/adnxs", "Google Static AdNXS"),
        new CompiledRegex(@"gstatic\.com/adsystem", "Google Static Ad System"),
        new CompiledRegex(@"gstatic\.com/adservice", "Google Static Ad Service"),
        new CompiledRegex(@"gstatic\.com/adserver", "Google Static Ad Server"),
        new CompiledRegex(@"gstatic\.com/adchoices", "Google Static Ad Choices"),
        new CompiledRegex(@"gstatic\.com/adnw", "Google Static ADNW"),
        new CompiledRegex(@"gstatic\.com/adimg", "Google Static Ad Images"),
        
        // YouTube specific patterns - Enhanced
        new CompiledRegex(@"youtube\.com/ptracking", "YouTube tracking"),
        new CompiledRegex(@"youtube\.com/api/stats/", "YouTube stats"),
        new CompiledRegex(@"youtube\.com/youtubei/v1/log_event", "YouTube logging"),
        new CompiledRegex(@"youtube\.com/youtubei/v1/player/ad_break", "YouTube ad break"),
        new CompiledRegex(@"youtube\.com/youtubei/v1/next.*[&?]ad=", "YouTube next ads"),
        new CompiledRegex(@"youtube\.com/youtubei/v1/browse.*[&?]ad=", "YouTube browse ads"),
        new CompiledRegex(@"youtube\.com/api/stats/ads", "YouTube ad stats"),
        new CompiledRegex(@"youtube\.com/api/stats/atr", "YouTube ATR stats"),
        new CompiledRegex(@"youtube\.com/api/stats/qoe", "YouTube QoE stats"),
        new CompiledRegex(@"youtube\.com/generate_204.*[&?]ad", "YouTube generate 204 ads"),
        new CompiledRegex(@"youtube\.com/pcs/activeview", "YouTube active view"),
        new CompiledRegex(@"youtube\.com/pagead/interaction", "YouTube pagead interaction"),
        new CompiledRegex(@"youtube\.com/pagead/adview", "YouTube pagead adview"),
        new CompiledRegex(@"youtube\.com/pagead/conversion", "YouTube pagead conversion"),
        new CompiledRegex(@"youtube\.com/ads/measurement", "YouTube ads measurement"),
        new CompiledRegex(@"youtube\.com/doubleclick/instream/ad_status", "YouTube DoubleClick instream"),
        new CompiledRegex(@"youtube\.com/video_ads_serving", "YouTube video ads serving"),
        new CompiledRegex(@"youtube\.com/ad_break", "YouTube ad break"),
        new CompiledRegex(@"youtube\.com/midroll_ad", "YouTube midroll ad"),
        new CompiledRegex(@"youtube\.com/preroll_ad", "YouTube preroll ad"),
        new CompiledRegex(@"youtube\.com/postroll_ad", "YouTube postroll ad"),
        new CompiledRegex(@"youtube\.com/companion_ad", "YouTube companion ad"),
        new CompiledRegex(@"youtube\.com/overlay_ad", "YouTube overlay ad"),
        new CompiledRegex(@"youtube\.com/bumper_ad", "YouTube bumper ad"),
        new CompiledRegex(@"youtube\.com/masthead_ad", "YouTube masthead ad"),
        new CompiledRegex(@"youtube\.com/promoted_content", "YouTube promoted content"),
        new CompiledRegex(@"youtube\.com/sponsored_content", "YouTube sponsored content"),
        new CompiledRegex(@"youtube\.com/brand_video", "YouTube brand video"),
        new CompiledRegex(@"youtube\.com/trueview", "YouTube TrueView"),
        
        // Generic ad patterns - Enhanced
        new CompiledRegex(@"[&?]gir=yes", "GIR parameter"),
        new CompiledRegex(@"/gen_204\?", "Gen 204 beacon"),
        new CompiledRegex(@"/generate_204\?", "Generate 204 beacon"),
        new CompiledRegex(@"/pcs/activeview", "Active view"),
        new CompiledRegex(@"/pagead/interaction", "Pagead interaction"),
        new CompiledRegex(@"/pagead/adview", "Pagead adview"),
        new CompiledRegex(@"/pagead/conversion", "Pagead conversion"),
        new CompiledRegex(@"/ads/measurement", "Ads measurement"),
        new CompiledRegex(@"/doubleclick/instream/ad_status", "DoubleClick instream"),
        new CompiledRegex(@"/video_ads_serving", "Video ads serving"),
        new CompiledRegex(@"/ad_break", "Ad break"),
        new CompiledRegex(@"/midroll_ad", "Midroll ad"),
        new CompiledRegex(@"/preroll_ad", "Preroll ad"),
        new CompiledRegex(@"/postroll_ad", "Postroll ad"),
        new CompiledRegex(@"/companion_ad", "Companion ad"),
        new CompiledRegex(@"/overlay_ad", "Overlay ad"),
        new CompiledRegex(@"/bumper_ad", "Bumper ad"),
        new CompiledRegex(@"/masthead_ad", "Masthead ad"),
        new CompiledRegex(@"/promoted_content", "Promoted content"),
        new CompiledRegex(@"/sponsored_content", "Sponsored content"),
        new CompiledRegex(@"/brand_video", "Brand video"),
        new CompiledRegex(@"/trueview", "TrueView"),
        new CompiledRegex(@"/adnxs\.com", "AppNexus"),
        new CompiledRegex(@"/adsystem\.amazon\.com", "Amazon Ad System"),
        new CompiledRegex(@"/facebook\.com/tr", "Facebook Tracking"),
        new CompiledRegex(@"/analytics\.tiktok\.com", "TikTok Analytics"),
        new CompiledRegex(@"/bat\.bing\.com", "Bing Ads Tracking"),
        new CompiledRegex(@"/outbrain\.com", "Outbrain"),
        new CompiledRegex(@"/taboola\.com", "Taboola"),
        new CompiledRegex(@"/criteo\.com", "Criteo"),
        new CompiledRegex(@"/adsafeprotected\.com", "AdSafe Protected"),
        new CompiledRegex(@"/moatads\.com", "Moat Ads"),
        new CompiledRegex(@"/scorecardresearch\.com", "Scorecard Research"),
        new CompiledRegex(@"/quantserve\.com", "Quantcast"),
        new CompiledRegex(@"/amazon-adsystem\.com", "Amazon Ad System"),
        new CompiledRegex(@"/googletag", "Google Tag"),
        new CompiledRegex(@"/gtag/js", "Google Tag JS"),
        new CompiledRegex(@"/gtm\.js", "Google Tag Manager JS"),
        new CompiledRegex(@"/analytics\.js", "Analytics JS"),
        new CompiledRegex(@"/ga\.js", "Google Analytics JS"),
        new CompiledRegex(@"/dc\.js", "DoubleClick JS"),
        new CompiledRegex(@"/ads\.js", "Ads JS"),
        new CompiledRegex(@"/adsense", "AdSense"),
        new CompiledRegex(@"/adsbygoogle", "Ads by Google"),
        new CompiledRegex(@"/show_ads", "Show ads"),
        new CompiledRegex(@"/ad_status", "Ad status"),
        new CompiledRegex(@"/ad_break_heartbeat", "Ad break heartbeat"),
        new CompiledRegex(@"/ad_video_id", "Ad video ID"),
        new CompiledRegex(@"/ad_cpn", "Ad CPN"),
        new CompiledRegex(@"/ad_docid", "Ad document ID"),
        new CompiledRegex(@"/ad_mt", "Ad MT"),
        
        // Mobile and app advertising patterns
        new CompiledRegex(@"unity3d\.com/ads", "Unity Ads"),
        new CompiledRegex(@"unityads\.unity3d\.com", "Unity Ads"),
        new CompiledRegex(@"admob\.com", "AdMob"),
        new CompiledRegex(@"media\.admob\.com", "AdMob Media"),
        new CompiledRegex(@"adservice\.google\.", "Google Ad Service"),
        
        // Video advertising patterns
        new CompiledRegex(@"spotxchange\.com", "SpotX"),
        new CompiledRegex(@"tremormedia\.com", "Tremor Media"),
        new CompiledRegex(@"brightroll\.com", "BrightRoll"),
        new CompiledRegex(@"innovid\.com", "Innovid"),
        new CompiledRegex(@"liverail\.com", "LiveRail"),
        new CompiledRegex(@"freewheel\.tv", "FreeWheel"),
        
        // Social media advertising patterns
        new CompiledRegex(@"facebook\.com/tr", "Facebook Pixel"),
        new CompiledRegex(@"connect\.facebook\.net", "Facebook Connect"),
        new CompiledRegex(@"pixel\.facebook\.com", "Facebook Pixel"),
        new CompiledRegex(@"analytics\.facebook\.com", "Facebook Analytics"),
        new CompiledRegex(@"ads\.twitter\.com", "Twitter Ads"),
        new CompiledRegex(@"analytics\.twitter\.com", "Twitter Analytics"),
        new CompiledRegex(@"ads\.tiktok\.com", "TikTok Ads"),
        new CompiledRegex(@"analytics\.tiktok\.com", "TikTok Analytics"),
        new CompiledRegex(@"ads\.linkedin\.com", "LinkedIn Ads"),
        new CompiledRegex(@"ads\.pinterest\.com", "Pinterest Ads"),
        new CompiledRegex(@"ads\.snapchat\.com", "Snapchat Ads"),
        new CompiledRegex(@"ads\.reddit\.com", "Reddit Ads"),
        
        // Programmatic advertising patterns
        new CompiledRegex(@"pubmatic\.com", "PubMatic"),
        new CompiledRegex(@"rubiconproject\.com", "Rubicon Project"),
        new CompiledRegex(@"openx\.net", "OpenX"),
        new CompiledRegex(@"appnexus\.com", "AppNexus"),
        new CompiledRegex(@"indexexchange\.com", "Index Exchange"),
        new CompiledRegex(@"casalemedia\.com", "Casale Media"),
        new CompiledRegex(@"adform\.net", "Adform"),
        new CompiledRegex(@"ads\.yahoo\.com", "Yahoo Ads"),
        
        // Content recommendation patterns
        new CompiledRegex(@"outbrain\.com", "Outbrain"),
        new CompiledRegex(@"taboola\.com", "Taboola"),
        new CompiledRegex(@"revcontent\.com", "RevContent"),
        new CompiledRegex(@"mgid\.com", "MGID"),
        
        // Ad verification patterns
        new CompiledRegex(@"adsafeprotected\.com", "AdSafe Protected"),
        new CompiledRegex(@"moatads\.com", "Moat"),
        new CompiledRegex(@"doubleverify\.com", "DoubleVerify"),
        new CompiledRegex(@"integralads\.com", "Integral Ad Science"),
        new CompiledRegex(@"imrworldwide\.com", "Nielsen"),
        new CompiledRegex(@"comscore\.com", "ComScore"),
        
        // Analytics and tracking patterns
        new CompiledRegex(@"google-analytics\.com", "Google Analytics"),
        new CompiledRegex(@"googletagmanager\.com", "Google Tag Manager"),
        new CompiledRegex(@"hotjar\.com", "Hotjar"),
        new CompiledRegex(@"mixpanel\.com", "Mixpanel"),
        new CompiledRegex(@"amplitude\.com", "Amplitude"),
        
        // Additional tracking patterns
        new CompiledRegex(@"[&?]utm_", "UTM tracking parameters"),
        new CompiledRegex(@"[&?]fbclid=", "Facebook click ID"),
        new CompiledRegex(@"[&?]gclid=", "Google click ID"),
        new CompiledRegex(@"[&?]msclkid=", "Microsoft click ID"),
        new CompiledRegex(@"[&?]twclid=", "Twitter click ID"),
        new CompiledRegex(@"[&?]ttclid=", "TikTok click ID"),
        new CompiledRegex(@"[&?]li_fat_id=", "LinkedIn fat ID"),
        new CompiledRegex(@"[&?]mc_cid=", "Mailchimp campaign ID"),
        new CompiledRegex(@"[&?]mc_eid=", "Mailchimp email ID"),
        new CompiledRegex(@"[&?]_ga=", "Google Analytics parameter"),
        new CompiledRegex(@"[&?]_gac=", "Google Analytics campaign"),
        new CompiledRegex(@"[&?]_gat=", "Google Analytics tracker"),
        new CompiledRegex(@"[&?]_gid=", "Google Analytics ID"),
        new CompiledRegex(@"[&?]dclid=", "DoubleClick ID"),
        new CompiledRegex(@"[&?]zanpid=", "Zanox partner ID"),
        new CompiledRegex(@"[&?]kenshoo_", "Kenshoo tracking"),
        new CompiledRegex(@"[&?]marin_", "Marin tracking"),
        new CompiledRegex(@"[&?]adgroupid=", "Ad group ID"),
        new CompiledRegex(@"[&?]campaignid=", "Campaign ID"),
        new CompiledRegex(@"[&?]creative=", "Creative ID"),
        new CompiledRegex(@"[&?]keyword=", "Keyword tracking"),
        new CompiledRegex(@"[&?]matchtype=", "Match type tracking"),
        new CompiledRegex(@"[&?]network=", "Network tracking"),
        new CompiledRegex(@"[&?]device=", "Device tracking"),
        new CompiledRegex(@"[&?]placement=", "Placement tracking"),
        new CompiledRegex(@"[&?]target=", "Target tracking"),
        new CompiledRegex(@"[&?]loc_interest_ms=", "Location interest"),
        new CompiledRegex(@"[&?]loc_physical_ms=", "Physical location"),
        new CompiledRegex(@"[&?]aceid=", "ACE ID"),
        new CompiledRegex(@"[&?]adposition=", "Ad position"),
        new CompiledRegex(@"[&?]ds_", "Data studio parameters"),
        new CompiledRegex(@"[&?]vero_", "Vero tracking"),
        new CompiledRegex(@"[&?]wickedid=", "Wicked tracking"),
        new CompiledRegex(@"[&?]yclid=", "Yandex click ID"),
        new CompiledRegex(@"[&?]_openstat=", "OpenStat tracking"),
        new CompiledRegex(@"[&?]rb_clickid=", "RB click ID"),
        new CompiledRegex(@"[&?]s_kwcid=", "Adobe search keyword"),
        new CompiledRegex(@"[&?]ef_id=", "Adobe Advertising Cloud"),
        new CompiledRegex(@"[&?]c=", "Campaign parameter"),
        new CompiledRegex(@"[&?]cid=", "Campaign ID parameter"),
        new CompiledRegex(@"[&?]cmpid=", "Campaign ID parameter"),
        new CompiledRegex(@"[&?]src=", "Source parameter"),
        new CompiledRegex(@"[&?]source=", "Source parameter"),
        new CompiledRegex(@"[&?]medium=", "Medium parameter"),
        new CompiledRegex(@"[&?]campaign=", "Campaign parameter"),
        new CompiledRegex(@"[&?]content=", "Content parameter"),
        new CompiledRegex(@"[&?]term=", "Term parameter")
    };

    private HashSet<string> LoadBlockedDomains()
    {
        return StaticBlockedDomains;
    }

    private CompiledRegex[] LoadUrlPatterns()
    {
        return StaticUrlPatterns;
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private void IncrementBlockedStat(string category, int count = 1)
    {
        _blockedStats.AddOrUpdate(category, count, (key, oldValue) => oldValue + count);
    }

    public Dictionary<string, int> GetBlockedStats()
    {
        return new Dictionary<string, int>(_blockedStats);
    }

    public void ClearStats()
    {
        _blockedStats.Clear();
    }

    public int GetTotalBlocked()
    {
        return _blockedStats.Values.Sum();
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        
        if (_webView != null)
        {
            try
            {
                _webView.WebResourceRequested -= OnWebResourceRequested;
                _webView.DOMContentLoaded -= OnDOMContentLoaded;
                _webView.NavigationCompleted -= OnNavigationCompleted;
                _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing AdBlocker: {ex.Message}");
            }
        }
        
        _blockedStats.Clear();
        _webView = null;
    }
}
