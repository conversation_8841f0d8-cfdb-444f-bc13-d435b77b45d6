using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Threading;

namespace YouTubePlayerApp;

public partial class MemoryProfilerWindow : Window
{
    private readonly DispatcherTimer _refreshTimer;
    private readonly MemoryProfiler _memoryProfiler;
    private readonly PerformanceMonitor _performanceMonitor;
    private int _snapshotCounter = 1;

    public MemoryProfilerWindow()
    {
        InitializeComponent();
        
        _memoryProfiler = MemoryProfiler.Instance;
        _performanceMonitor = PerformanceMonitor.Instance;
        
        // Set up auto-refresh timer
        _refreshTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(2)
        };
        _refreshTimer.Tick += RefreshTimer_Tick;
        
        LoadInitialData();
        UpdateProfilingStatus(false);
    }

    private void LoadInitialData()
    {
        RefreshCurrentState();
        RefreshTrends();
        RefreshSnapshots();
    }

    private void StartProfiling_Click(object sender, RoutedEventArgs e)
    {
        _memoryProfiler.SetProfilingEnabled(true);
        _refreshTimer.Start();
        
        StartProfilingButton.IsEnabled = false;
        StopProfilingButton.IsEnabled = true;
        
        UpdateProfilingStatus(true);
        StatusText.Text = "Profiling started";
    }

    private void StopProfiling_Click(object sender, RoutedEventArgs e)
    {
        _memoryProfiler.SetProfilingEnabled(false);
        _refreshTimer.Stop();
        
        StartProfilingButton.IsEnabled = true;
        StopProfilingButton.IsEnabled = false;
        
        UpdateProfilingStatus(false);
        StatusText.Text = "Profiling stopped";
    }

    private void TakeSnapshot_Click(object sender, RoutedEventArgs e)
    {
        var label = $"Snapshot_{_snapshotCounter++}_{DateTime.Now:HHmmss}";
        TakeSnapshotWithLabel(label);
    }

    private void TakeSnapshotWithLabel_Click(object sender, RoutedEventArgs e)
    {
        var label = SnapshotLabelTextBox.Text.Trim();
        if (string.IsNullOrEmpty(label))
        {
            label = $"Snapshot_{_snapshotCounter++}";
        }
        
        TakeSnapshotWithLabel(label);
        SnapshotLabelTextBox.Text = "Snapshot";
    }

    private void TakeSnapshotWithLabel(string label)
    {
        try
        {
            var snapshot = _memoryProfiler.TakeSnapshot(label);
            RefreshSnapshots();
            StatusText.Text = $"Snapshot '{label}' taken at {snapshot.Timestamp:HH:mm:ss}";
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Error taking snapshot: {ex.Message}";
        }
    }

    private void ForceGC_Click(object sender, RoutedEventArgs e)
    {
        StatusText.Text = "Forcing garbage collection...";
        
        Task.Run(() =>
        {
            _performanceMonitor.ForceGarbageCollection();
            
            Dispatcher.Invoke(() =>
            {
                RefreshCurrentState();
                StatusText.Text = "Garbage collection completed";
            });
        });
    }

    private void ForceGCWithAnalysis_Click(object sender, RoutedEventArgs e)
    {
        StatusText.Text = "Forcing GC and analyzing effectiveness...";
        
        Task.Run(() =>
        {
            var report = _memoryProfiler.ForceGCAndMeasure();
            
            Dispatcher.Invoke(() =>
            {
                DisplayGCAnalysis(report);
                RefreshCurrentState();
                LastGCAnalysisText.Text = $"{report.MemoryFreedMB:F2} MB freed ({report.Effectiveness})";
                StatusText.Text = "GC analysis completed";
            });
        });
    }

    private void ClearData_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("Clear all profiling data and snapshots?", "Confirm Clear", 
            MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            _memoryProfiler.ClearHistory();
            RefreshTrends();
            RefreshSnapshots();
            StatusText.Text = "Profiling data cleared";
        }
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        RefreshCurrentState();
        RefreshTrends();
        RefreshSnapshots();
        StatusText.Text = "Data refreshed";
    }

    private void CompareSnapshots_Click(object sender, RoutedEventArgs e)
    {
        var fromLabel = CompareFromComboBox.SelectedItem?.ToString();
        var toLabel = CompareToComboBox.SelectedItem?.ToString();
        
        if (string.IsNullOrEmpty(fromLabel) || string.IsNullOrEmpty(toLabel))
        {
            StatusText.Text = "Select two snapshots to compare";
            return;
        }

        try
        {
            var comparison = _memoryProfiler.CompareSnapshots(fromLabel, toLabel);
            DisplaySnapshotComparison(comparison);
            StatusText.Text = $"Compared '{fromLabel}' vs '{toLabel}'";
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Error comparing snapshots: {ex.Message}";
        }
    }

    private void SnapshotsListBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (SnapshotsListBox.SelectedItem is SnapshotDisplayItem selectedSnapshot)
        {
            DisplaySnapshotDetails(selectedSnapshot);
        }
    }

    private void RefreshTimer_Tick(object? sender, EventArgs e)
    {
        RefreshCurrentState();
        RefreshTrends();
    }

    private void RefreshCurrentState()
    {
        try
        {
            var breakdown = _memoryProfiler.GetMemoryBreakdown();
            var metrics = _performanceMonitor.GetCurrentMetrics();

            // Update quick stats
            ManagedMemoryText.Text = $"{breakdown.ManagedMemoryMB:F2} MB";
            WorkingSetText.Text = $"{breakdown.WorkingSetMB:F2} MB";
            PrivateMemoryText.Text = $"{breakdown.PrivateMemoryMB:F2} MB";
            GCCollectionsText.Text = $"G0:{metrics.Gen0Collections} G1:{metrics.Gen1Collections} G2:{metrics.Gen2Collections}";

            // Update detailed breakdown
            UpdateMemoryBreakdownGrid(breakdown);
            UpdatePerformanceMetricsGrid(metrics);
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Error refreshing state: {ex.Message}";
        }
    }

    private void RefreshTrends()
    {
        try
        {
            var trends = _memoryProfiler.GetMemoryTrends();
            var leakAnalysis = _memoryProfiler.AnalyzeMemoryLeaks();

            // Update trends summary
            AverageMemoryText.Text = $"{trends.AverageMemoryMB:F2} MB";
            PeakMemoryText.Text = $"{trends.PeakMemoryMB:F2} MB";
            GrowthRateText.Text = $"{trends.MemoryGrowthRate:F3} MB/min";

            // Update leak analysis
            DisplayMemoryLeakAnalysis(leakAnalysis);
            DisplayMemoryHistory(trends);
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Error refreshing trends: {ex.Message}";
        }
    }

    private void RefreshSnapshots()
    {
        // This would need to be implemented based on actual snapshot storage
        // For now, we'll just update the ComboBoxes
        // In a real implementation, you'd get the list from MemoryProfiler
        
        // Update comparison ComboBoxes
        CompareFromComboBox.Items.Clear();
        CompareToComboBox.Items.Clear();
        
        // Add sample items (replace with actual snapshot labels)
        var sampleSnapshots = new[] { "Initial", "After_Load", "After_Navigation" };
        foreach (var snapshot in sampleSnapshots)
        {
            CompareFromComboBox.Items.Add(snapshot);
            CompareToComboBox.Items.Add(snapshot);
        }
    }

    private void UpdateMemoryBreakdownGrid(MemoryBreakdown breakdown)
    {
        MemoryBreakdownGrid.Children.Clear();
        MemoryBreakdownGrid.RowDefinitions.Clear();

        var memoryItems = new[]
        {
            ("Managed Memory", $"{breakdown.ManagedMemoryMB:F2} MB"),
            ("Unmanaged Memory", $"{breakdown.UnmanagedMemoryMB:F2} MB"),
            ("Working Set", $"{breakdown.WorkingSetMB:F2} MB"),
            ("Private Memory", $"{breakdown.PrivateMemoryMB:F2} MB"),
            ("Virtual Memory", $"{breakdown.VirtualMemoryMB:F2} MB"),
            ("Paged Memory", $"{breakdown.PagedMemoryMB:F2} MB"),
            ("Non-Paged Memory", $"{breakdown.NonPagedMemoryMB:F2} MB")
        };

        for (int i = 0; i < memoryItems.Length; i++)
        {
            MemoryBreakdownGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var label = new TextBlock
            {
                Text = memoryItems[i].Item1,
                Margin = new Thickness(0, 2, 10, 2),
                FontWeight = FontWeights.SemiBold
            };
            Grid.SetRow(label, i);
            Grid.SetColumn(label, 0);
            MemoryBreakdownGrid.Children.Add(label);

            var value = new TextBlock
            {
                Text = memoryItems[i].Item2,
                Margin = new Thickness(0, 2, 0, 2),
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetRow(value, i);
            Grid.SetColumn(value, 1);
            MemoryBreakdownGrid.Children.Add(value);
        }
    }

    private void UpdatePerformanceMetricsGrid(PerformanceMetrics metrics)
    {
        PerformanceMetricsGrid.Children.Clear();
        PerformanceMetricsGrid.RowDefinitions.Clear();

        var performanceItems = new[]
        {
            ("CPU Usage", $"{metrics.CpuUsagePercent:F1}%"),
            ("Peak Memory", $"{metrics.PeakMemoryMB:F2} MB"),
            ("Uptime", $"{metrics.UptimeMinutes:F1} minutes"),
            ("Gen 0 Collections", metrics.Gen0Collections.ToString()),
            ("Gen 1 Collections", metrics.Gen1Collections.ToString()),
            ("Gen 2 Collections", metrics.Gen2Collections.ToString())
        };

        for (int i = 0; i < performanceItems.Length; i++)
        {
            PerformanceMetricsGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            var label = new TextBlock
            {
                Text = performanceItems[i].Item1,
                Margin = new Thickness(0, 2, 10, 2),
                FontWeight = FontWeights.SemiBold
            };
            Grid.SetRow(label, i);
            Grid.SetColumn(label, 0);
            PerformanceMetricsGrid.Children.Add(label);

            var value = new TextBlock
            {
                Text = performanceItems[i].Item2,
                Margin = new Thickness(0, 2, 0, 2),
                HorizontalAlignment = HorizontalAlignment.Right
            };
            Grid.SetRow(value, i);
            Grid.SetColumn(value, 1);
            PerformanceMetricsGrid.Children.Add(value);
        }
    }

    private void DisplayMemoryLeakAnalysis(MemoryLeakAnalysis analysis)
    {
        LeakAnalysisPanel.Children.Clear();

        if (!analysis.HasSufficientData)
        {
            LeakAnalysisPanel.Children.Add(new TextBlock
            {
                Text = "Insufficient data for leak analysis. Let profiling run for a few minutes.",
                FontStyle = FontStyles.Italic,
                Foreground = Brushes.Gray
            });
            return;
        }

        var statusColor = analysis.SuspiciousGrowthDetected ? Brushes.Red : Brushes.Green;
        var statusText = analysis.SuspiciousGrowthDetected ? "⚠️ Potential Issue Detected" : "✅ Memory Usage Stable";

        LeakAnalysisPanel.Children.Add(new TextBlock
        {
            Text = statusText,
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = statusColor,
            Margin = new Thickness(0, 0, 0, 10)
        });

        LeakAnalysisPanel.Children.Add(new TextBlock
        {
            Text = $"Growth Points: {analysis.ConsecutiveGrowthPoints}/{analysis.TotalDataPoints} ({analysis.GrowthPercentage:P1})",
            Margin = new Thickness(0, 0, 0, 5)
        });

        LeakAnalysisPanel.Children.Add(new TextBlock
        {
            Text = $"Average Growth: {analysis.AverageGrowthMB:F2} MB per measurement",
            Margin = new Thickness(0, 0, 0, 5)
        });

        LeakAnalysisPanel.Children.Add(new TextBlock
        {
            Text = $"Recommendation: {analysis.Recommendation}",
            FontWeight = FontWeights.SemiBold,
            Margin = new Thickness(0, 10, 0, 0)
        });
    }

    private void DisplayMemoryHistory(MemoryTrends trends)
    {
        MemoryHistoryPanel.Children.Clear();

        MemoryHistoryPanel.Children.Add(new TextBlock
        {
            Text = $"Data Points: {trends.TotalDataPoints} over {trends.TimeSpan.TotalMinutes:F1} minutes",
            FontWeight = FontWeights.SemiBold,
            Margin = new Thickness(0, 0, 0, 10)
        });

        MemoryHistoryPanel.Children.Add(new TextBlock
        {
            Text = $"Memory Range: {trends.MinMemoryMB:F2} MB - {trends.PeakMemoryMB:F2} MB",
            Margin = new Thickness(0, 0, 0, 5)
        });

        MemoryHistoryPanel.Children.Add(new TextBlock
        {
            Text = $"Recent Trend: {(trends.RecentTrendMB >= 0 ? "+" : "")}{trends.RecentTrendMB:F2} MB",
            Margin = new Thickness(0, 0, 0, 5),
            Foreground = trends.RecentTrendMB > 5 ? Brushes.Red : trends.RecentTrendMB < -1 ? Brushes.Green : Brushes.Black
        });

        MemoryHistoryPanel.Children.Add(new TextBlock
        {
            Text = $"Total GC Collections: {trends.TotalGCCollections}",
            Margin = new Thickness(0, 0, 0, 5)
        });
    }

    private void DisplayGCAnalysis(GCEffectivenessReport report)
    {
        GCAnalysisPanel.Children.Clear();

        GCAnalysisPanel.Children.Add(new TextBlock
        {
            Text = "Garbage Collection Analysis Results",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 15)
        });

        var effectivenessColor = report.MemoryFreedMB > 1 ? Brushes.Green : 
                               report.MemoryFreedMB > 0 ? Brushes.Orange : Brushes.Red;

        GCAnalysisPanel.Children.Add(new TextBlock
        {
            Text = $"Effectiveness: {report.Effectiveness}",
            FontSize = 14,
            FontWeight = FontWeights.Bold,
            Foreground = effectivenessColor,
            Margin = new Thickness(0, 0, 0, 10)
        });

        var details = new[]
        {
            $"Duration: {report.GCDurationMs} ms",
            $"Memory Freed: {report.MemoryFreedMB:F2} MB",
            $"Working Set Freed: {report.WorkingSetFreedMB:F2} MB",
            $"Gen 0 Collections: {report.Gen0Collections}",
            $"Gen 1 Collections: {report.Gen1Collections}",
            $"Gen 2 Collections: {report.Gen2Collections}"
        };

        foreach (var detail in details)
        {
            GCAnalysisPanel.Children.Add(new TextBlock
            {
                Text = detail,
                Margin = new Thickness(0, 0, 0, 5)
            });
        }

        // Add interpretation
        GCAnalysisPanel.Children.Add(new TextBlock
        {
            Text = "Interpretation:",
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 15, 0, 5)
        });

        string interpretation;
        if (report.MemoryFreedMB > 10)
            interpretation = "Excellent: GC freed significant memory, indicating good cleanup of unused objects.";
        else if (report.MemoryFreedMB > 1)
            interpretation = "Good: GC freed some memory. Normal for applications with moderate object allocation.";
        else if (report.MemoryFreedMB > 0)
            interpretation = "Limited: Only small amount of memory freed. May indicate most objects are still in use.";
        else
            interpretation = "No Effect: No memory was freed. Objects may be held by strong references or in long-lived generations.";

        GCAnalysisPanel.Children.Add(new TextBlock
        {
            Text = interpretation,
            TextWrapping = TextWrapping.Wrap,
            Margin = new Thickness(0, 0, 0, 0)
        });
    }

    private void DisplaySnapshotDetails(SnapshotDisplayItem snapshot)
    {
        SnapshotDetailsPanel.Children.Clear();

        SnapshotDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Snapshot: {snapshot.Label}",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 10)
        });

        SnapshotDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Timestamp: {snapshot.Timestamp:yyyy-MM-dd HH:mm:ss}",
            Margin = new Thickness(0, 0, 0, 15)
        });

        var details = new[]
        {
            $"Managed Memory: {snapshot.ManagedMemoryMB:F2} MB",
            $"Working Set: {snapshot.WorkingSetMB:F2} MB",
            $"Private Memory: {snapshot.PrivateMemoryMB:F2} MB",
            $"Virtual Memory: {snapshot.VirtualMemoryMB:F2} MB",
            $"Paged Memory: {snapshot.PagedMemoryMB:F2} MB"
        };

        foreach (var detail in details)
        {
            SnapshotDetailsPanel.Children.Add(new TextBlock
            {
                Text = detail,
                Margin = new Thickness(0, 0, 0, 5)
            });
        }
    }

    private void DisplaySnapshotComparison(MemoryComparison comparison)
    {
        SnapshotDetailsPanel.Children.Clear();

        SnapshotDetailsPanel.Children.Add(new TextBlock
        {
            Text = "Snapshot Comparison",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 10)
        });

        SnapshotDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"{comparison.BeforeSnapshot.Label} → {comparison.AfterSnapshot.Label}",
            FontSize = 14,
            Margin = new Thickness(0, 0, 0, 5)
        });

        SnapshotDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Time Difference: {comparison.TimeDelta.TotalMinutes:F1} minutes",
            Margin = new Thickness(0, 0, 0, 15)
        });

        var deltas = new[]
        {
            ("Managed Memory", comparison.ManagedMemoryDelta),
            ("Working Set", comparison.WorkingSetDelta),
            ("Private Memory", comparison.PrivateMemoryDelta),
            ("Virtual Memory", comparison.VirtualMemoryDelta)
        };

        foreach (var (name, delta) in deltas)
        {
            var deltaText = $"{name}: {(delta >= 0 ? "+" : "")}{delta / 1024.0 / 1024.0:F2} MB";
            var textBlock = new TextBlock
            {
                Text = deltaText,
                Margin = new Thickness(0, 0, 0, 5),
                Foreground = delta > 1024 * 1024 ? Brushes.Red : delta < -1024 * 1024 ? Brushes.Green : Brushes.Black
            };
            SnapshotDetailsPanel.Children.Add(textBlock);
        }

        // GC Collections
        SnapshotDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"GC Collections - G0: +{comparison.Gen0CollectionsDelta}, G1: +{comparison.Gen1CollectionsDelta}, G2: +{comparison.Gen2CollectionsDelta}",
            Margin = new Thickness(0, 10, 0, 0)
        });
    }

    private void UpdateProfilingStatus(bool isEnabled)
    {
        ProfilingIndicator.Fill = isEnabled ? Brushes.Green : Brushes.Red;
        ProfilingStatusText.Text = isEnabled ? "Active" : "Stopped";
    }

    protected override void OnClosed(EventArgs e)
    {
        _refreshTimer?.Stop();
        base.OnClosed(e);
    }
}

// Helper class for snapshot display
public class SnapshotDisplayItem
{
    public string Label { get; set; } = "";
    public DateTime Timestamp { get; set; }
    public double ManagedMemoryMB { get; set; }
    public double WorkingSetMB { get; set; }
    public double PrivateMemoryMB { get; set; }
    public double VirtualMemoryMB { get; set; }
    public double PagedMemoryMB { get; set; }
}
