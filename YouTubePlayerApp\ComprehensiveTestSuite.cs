using Microsoft.Web.WebView2.Core;
using System.Text;

namespace YouTubePlayerApp;

/// <summary>
/// Comprehensive test suite for verifying ad blocking and video playback across all YouTube content types
/// </summary>
public static class ComprehensiveTestSuite
{
    public static async Task<string> RunFullTestSuiteAsync(CoreWebView2 webView)
    {
        var results = new StringBuilder();
        results.AppendLine("=== COMPREHENSIVE YOUTUBE FUNCTIONALITY TEST SUITE ===");
        results.AppendLine($"Test Started: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        results.AppendLine($"Current URL: {webView.Source}");
        results.AppendLine();

        var testResults = new Dictionary<string, bool>();

        // Test 1: Basic Video Playback
        results.AppendLine("🎥 TEST 1: BASIC VIDEO PLAYBACK");
        results.AppendLine("=" + new string('=', 50));
        try
        {
            var videoPlaybackWorking = await VideoPlaybackDiagnostics.TestVideoPlaybackFunctionalityAsync(webView);
            testResults["Basic Video Playback"] = videoPlaybackWorking;
            results.AppendLine($"Status: {(videoPlaybackWorking ? "✅ PASS" : "❌ FAIL")}");
            results.AppendLine($"Details: Video playback functionality is {(videoPlaybackWorking ? "working correctly" : "impaired")}");
        }
        catch (Exception ex)
        {
            testResults["Basic Video Playback"] = false;
            results.AppendLine($"Status: ❌ ERROR - {ex.Message}");
        }
        results.AppendLine();

        // Test 2: Ad Blocking Effectiveness
        results.AppendLine("🚫 TEST 2: AD BLOCKING EFFECTIVENESS");
        results.AppendLine("=" + new string('=', 50));
        try
        {
            var adBlockingEffective = await AdBlockDiagnostics.TestAdBlockingEffectivenessAsync(webView);
            testResults["Ad Blocking"] = adBlockingEffective;
            results.AppendLine($"Status: {(adBlockingEffective ? "✅ PASS" : "⚠️ PARTIAL")}");
            results.AppendLine($"Details: Ad blocking is {(adBlockingEffective ? "highly effective" : "partially effective or needs improvement")}");
        }
        catch (Exception ex)
        {
            testResults["Ad Blocking"] = false;
            results.AppendLine($"Status: ❌ ERROR - {ex.Message}");
        }
        results.AppendLine();

        // Test 3: Player Controls Functionality
        results.AppendLine("🎮 TEST 3: PLAYER CONTROLS FUNCTIONALITY");
        results.AppendLine("=" + new string('=', 50));
        try
        {
            var controlsWorking = await TestPlayerControlsAsync(webView);
            testResults["Player Controls"] = controlsWorking;
            results.AppendLine($"Status: {(controlsWorking ? "✅ PASS" : "❌ FAIL")}");
            results.AppendLine($"Details: Player controls are {(controlsWorking ? "fully functional" : "not working properly")}");
        }
        catch (Exception ex)
        {
            testResults["Player Controls"] = false;
            results.AppendLine($"Status: ❌ ERROR - {ex.Message}");
        }
        results.AppendLine();

        // Test 4: Different Content Types
        results.AppendLine("📺 TEST 4: CONTENT TYPE COMPATIBILITY");
        results.AppendLine("=" + new string('=', 50));
        var contentTypeResults = await TestContentTypesAsync(webView);
        foreach (var contentType in contentTypeResults)
        {
            testResults[$"Content Type: {contentType.Key}"] = contentType.Value;
            results.AppendLine($"{contentType.Key}: {(contentType.Value ? "✅ PASS" : "❌ FAIL")}");
        }
        results.AppendLine();

        // Test 5: Performance Impact
        results.AppendLine("⚡ TEST 5: PERFORMANCE IMPACT");
        results.AppendLine("=" + new string('=', 50));
        try
        {
            var performanceGood = await TestPerformanceImpactAsync(webView);
            testResults["Performance"] = performanceGood;
            results.AppendLine($"Status: {(performanceGood ? "✅ PASS" : "⚠️ NEEDS ATTENTION")}");
            results.AppendLine($"Details: Performance impact is {(performanceGood ? "minimal and acceptable" : "significant and may need optimization")}");
        }
        catch (Exception ex)
        {
            testResults["Performance"] = false;
            results.AppendLine($"Status: ❌ ERROR - {ex.Message}");
        }
        results.AppendLine();

        // Test 6: Memory Usage
        results.AppendLine("🧠 TEST 6: MEMORY USAGE");
        results.AppendLine("=" + new string('=', 50));
        try
        {
            var memoryUsageGood = await TestMemoryUsageAsync(webView);
            testResults["Memory Usage"] = memoryUsageGood;
            results.AppendLine($"Status: {(memoryUsageGood ? "✅ PASS" : "⚠️ HIGH USAGE")}");
            results.AppendLine($"Details: Memory usage is {(memoryUsageGood ? "within acceptable limits" : "higher than expected")}");
        }
        catch (Exception ex)
        {
            testResults["Memory Usage"] = false;
            results.AppendLine($"Status: ❌ ERROR - {ex.Message}");
        }
        results.AppendLine();

        // Test 7: Network Blocking Accuracy
        results.AppendLine("🌐 TEST 7: NETWORK BLOCKING ACCURACY");
        results.AppendLine("=" + new string('=', 50));
        try
        {
            var networkBlockingAccurate = await TestNetworkBlockingAccuracyAsync(webView);
            testResults["Network Blocking"] = networkBlockingAccurate;
            results.AppendLine($"Status: {(networkBlockingAccurate ? "✅ PASS" : "⚠️ NEEDS TUNING")}");
            results.AppendLine($"Details: Network blocking is {(networkBlockingAccurate ? "accurate and precise" : "may be blocking legitimate content or missing ads")}");
        }
        catch (Exception ex)
        {
            testResults["Network Blocking"] = false;
            results.AppendLine($"Status: ❌ ERROR - {ex.Message}");
        }
        results.AppendLine();

        // Test Summary
        results.AppendLine("📊 TEST SUMMARY");
        results.AppendLine("=" + new string('=', 50));
        var totalTests = testResults.Count;
        var passedTests = testResults.Values.Count(v => v);
        var failedTests = totalTests - passedTests;
        var successRate = totalTests > 0 ? (double)passedTests / totalTests * 100 : 0;

        results.AppendLine($"Total Tests: {totalTests}");
        results.AppendLine($"Passed: {passedTests} ✅");
        results.AppendLine($"Failed: {failedTests} ❌");
        results.AppendLine($"Success Rate: {successRate:F1}%");
        results.AppendLine();

        if (successRate >= 90)
        {
            results.AppendLine("🎉 OVERALL RESULT: EXCELLENT - System is working optimally!");
        }
        else if (successRate >= 75)
        {
            results.AppendLine("✅ OVERALL RESULT: GOOD - System is working well with minor issues.");
        }
        else if (successRate >= 50)
        {
            results.AppendLine("⚠️ OVERALL RESULT: NEEDS IMPROVEMENT - Several issues detected.");
        }
        else
        {
            results.AppendLine("❌ OVERALL RESULT: CRITICAL ISSUES - Major problems need immediate attention.");
        }

        results.AppendLine();
        results.AppendLine("🔧 RECOMMENDATIONS:");
        if (failedTests > 0)
        {
            results.AppendLine("• Run individual diagnostic tools for failed tests");
            results.AppendLine("• Apply fixes using the Fix Video Playback tool");
            results.AppendLine("• Check settings and ensure VideoSafeAdBlocker is enabled");
            results.AppendLine("• Consider restarting the application if issues persist");
        }
        else
        {
            results.AppendLine("• System is working optimally - no action needed");
            results.AppendLine("• Continue monitoring performance during regular use");
        }

        results.AppendLine();
        results.AppendLine($"Test Completed: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        results.AppendLine("=== END OF COMPREHENSIVE TEST SUITE ===");

        return results.ToString();
    }

    private static async Task<bool> TestPlayerControlsAsync(CoreWebView2 webView)
    {
        var script = @"
            (function() {
                // Test if all essential player controls are present and functional
                const requiredControls = [
                    '.ytp-play-button',
                    '.ytp-progress-bar-container',
                    '.ytp-volume-panel',
                    '.ytp-settings-button',
                    '.ytp-fullscreen-button'
                ];
                
                let workingControls = 0;
                requiredControls.forEach(selector => {
                    const control = document.querySelector(selector);
                    if (control) {
                        const style = getComputedStyle(control);
                        if (style.display !== 'none' && 
                            style.visibility !== 'hidden' && 
                            style.pointerEvents !== 'none') {
                            workingControls++;
                        }
                    }
                });
                
                // At least 80% of controls should be working
                return workingControls >= (requiredControls.length * 0.8);
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(script);
            return result.Trim().ToLower() == "true";
        }
        catch
        {
            return false;
        }
    }

    private static async Task<Dictionary<string, bool>> TestContentTypesAsync(CoreWebView2 webView)
    {
        var results = new Dictionary<string, bool>();
        
        var script = @"
            (function() {
                const results = {};
                
                // Detect current content type
                const url = window.location.href;
                
                if (url.includes('/watch?v=')) {
                    results['Regular Video'] = true;
                } else {
                    results['Regular Video'] = false;
                }
                
                if (url.includes('/shorts/')) {
                    results['YouTube Shorts'] = true;
                } else {
                    results['YouTube Shorts'] = false;
                }
                
                if (url.includes('/live/') || document.querySelector('.ytp-live-badge')) {
                    results['Live Stream'] = true;
                } else {
                    results['Live Stream'] = false;
                }
                
                // Check if video player is working regardless of content type
                const video = document.querySelector('video.html5-main-video');
                const playerWorking = video && 
                                    getComputedStyle(video).display !== 'none' &&
                                    video.readyState >= 2;
                
                // Apply working status to detected content types
                Object.keys(results).forEach(key => {
                    if (results[key]) {
                        results[key] = playerWorking;
                    }
                });
                
                // If no specific content type detected, test general compatibility
                if (!Object.values(results).some(v => v)) {
                    results['General Content'] = playerWorking;
                }
                
                return JSON.stringify(results);
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(script);
            var jsonResult = result.Trim('"').Replace("\\\"", "\"");
            var contentResults = System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, bool>>(jsonResult);
            return contentResults ?? new Dictionary<string, bool> { { "Unknown Content", false } };
        }
        catch
        {
            return new Dictionary<string, bool> { { "Content Type Test", false } };
        }
    }

    private static async Task<bool> TestPerformanceImpactAsync(CoreWebView2 webView)
    {
        var script = @"
            (function() {
                // Test page load performance
                const perf = performance.getEntriesByType('navigation')[0];
                if (!perf) return false;
                
                const loadTime = perf.loadEventEnd - perf.loadEventStart;
                const domTime = perf.domContentLoadedEventEnd - perf.domContentLoadedEventStart;
                
                // Consider performance good if load times are reasonable
                // Load time under 3 seconds and DOM time under 1 second
                return loadTime < 3000 && domTime < 1000;
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(script);
            return result.Trim().ToLower() == "true";
        }
        catch
        {
            return true; // Assume good if can't measure
        }
    }

    private static async Task<bool> TestMemoryUsageAsync(CoreWebView2 webView)
    {
        try
        {
            // Get current process memory usage
            var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
            var memoryUsageMB = currentProcess.WorkingSet64 / (1024 * 1024);
            
            // Consider memory usage good if under 500MB for the entire application
            return memoryUsageMB < 500;
        }
        catch
        {
            return true; // Assume good if can't measure
        }
    }

    private static async Task<bool> TestNetworkBlockingAccuracyAsync(CoreWebView2 webView)
    {
        var script = @"
            (function() {
                // Test if video content is loading while ads are blocked
                const video = document.querySelector('video.html5-main-video');
                if (!video) return false;
                
                // Check if video has a source and is loading
                const hasVideoSource = video.src || video.currentSrc;
                const isLoading = video.networkState === 2; // NETWORK_LOADING
                const hasData = video.readyState >= 2; // HAVE_CURRENT_DATA or better
                
                // Check if obvious ads are blocked
                const adElements = document.querySelectorAll('.video-ads, .ytp-ad-module, .ytd-promoted-video-renderer');
                const adsBlocked = adElements.length === 0 || 
                                 Array.from(adElements).every(ad => 
                                     getComputedStyle(ad).display === 'none' ||
                                     getComputedStyle(ad).visibility === 'hidden'
                                 );
                
                // Good if video content loads and ads are blocked
                return (hasVideoSource && (isLoading || hasData)) && adsBlocked;
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(script);
            return result.Trim().ToLower() == "true";
        }
        catch
        {
            return false;
        }
    }

    public static async Task<string> RunQuickHealthCheckAsync(CoreWebView2 webView)
    {
        var results = new StringBuilder();
        results.AppendLine("🏥 QUICK HEALTH CHECK");
        results.AppendLine("=" + new string('=', 30));
        
        try
        {
            // Quick video playback test
            var videoWorking = await VideoPlaybackDiagnostics.TestVideoPlaybackFunctionalityAsync(webView);
            results.AppendLine($"Video Playback: {(videoWorking ? "✅ OK" : "❌ ISSUE")}");
            
            // Quick ad blocking test
            var adsBlocked = await AdBlockDiagnostics.TestAdBlockingEffectivenessAsync(webView);
            results.AppendLine($"Ad Blocking: {(adsBlocked ? "✅ OK" : "⚠️ PARTIAL")}");
            
            // Quick controls test
            var controlsWorking = await TestPlayerControlsAsync(webView);
            results.AppendLine($"Player Controls: {(controlsWorking ? "✅ OK" : "❌ ISSUE")}");
            
            var allGood = videoWorking && adsBlocked && controlsWorking;
            results.AppendLine();
            results.AppendLine($"Overall Status: {(allGood ? "✅ HEALTHY" : "⚠️ NEEDS ATTENTION")}");
            
            if (!allGood)
            {
                results.AppendLine();
                results.AppendLine("💡 Recommendation: Run full test suite for detailed analysis");
            }
        }
        catch (Exception ex)
        {
            results.AppendLine($"❌ ERROR: {ex.Message}");
        }
        
        return results.ToString();
    }
}