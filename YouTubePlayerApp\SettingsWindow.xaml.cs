using System.Windows;
using System.Windows.Controls;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for SettingsWindow.xaml
/// </summary>
public partial class SettingsWindow : Window
{
    public SettingsWindow()
    {
        InitializeComponent();
        LoadSettings();
    }

    private void LoadSettings()
    {
        // Load settings from configuration
        // This is a placeholder - in a real application, you would load from a config file or registry
        StartWithWindowsCheckBox.IsChecked = AppSettings.StartWithWindows;
        StartFullScreenCheckBox.IsChecked = AppSettings.StartFullScreen;
        RememberLastPositionCheckBox.IsChecked = AppSettings.RememberLastPosition;
        
        VideoQualityComboBox.SelectedIndex = AppSettings.DefaultVideoQuality;
        AutoplayCheckBox.IsChecked = AppSettings.EnableAutoplay;
        MuteOnStartupCheckBox.IsChecked = AppSettings.MuteOnStartup;
        VolumeSlider.Value = AppSettings.DefaultVolume;
        
        // Video quality control settings
        ForceVideoQualityCheckBox.IsChecked = AppSettings.ForceVideoQuality;
        SetQualityComboBoxValue(AppSettings.ForcedVideoQuality);
        LockQualityControlsCheckBox.IsChecked = AppSettings.LockQualityControls;
        HideQualityControlsCheckBox.IsChecked = AppSettings.HideQualityControls;
        OverrideUserQualityChangesCheckBox.IsChecked = AppSettings.OverrideUserQualityChanges;
        
        ClearCookiesOnExitCheckBox.IsChecked = AppSettings.ClearCookiesOnExit;
        BlockThirdPartyCookiesCheckBox.IsChecked = AppSettings.BlockThirdPartyCookies;
        DoNotTrackCheckBox.IsChecked = AppSettings.DoNotTrack;
        
        // Ad blocking settings
        BlockAdsCheckBox.IsChecked = AppSettings.BlockAds;
        BlockVideoAdsCheckBox.IsChecked = AppSettings.BlockVideoAds;
        BlockBannerAdsCheckBox.IsChecked = AppSettings.BlockBannerAds;
        BlockTrackingScriptsCheckBox.IsChecked = AppSettings.BlockTrackingScripts;
        AutoSkipAdsCheckBox.IsChecked = AppSettings.AutoSkipAds;
        ShowAdBlockStatsCheckBox.IsChecked = AppSettings.ShowAdBlockStats;
        
        // YouTube enhancement settings
        DisableAutoPreviewCheckBox.IsChecked = AppSettings.DisableAutoPreview;
        
        // Set home page options
        switch (AppSettings.HomePage)
        {
            case "https://www.youtube.com":
                YouTubeHomeRadio.IsChecked = true;
                break;
            case "https://www.youtube.com/feed/trending":
                YouTubeTrendingRadio.IsChecked = true;
                break;
            default:
                CustomUrlRadio.IsChecked = true;
                CustomUrlTextBox.Text = AppSettings.HomePage;
                break;
        }
    }

    private void SaveSettings()
    {
        // Save settings to configuration
        AppSettings.StartWithWindows = StartWithWindowsCheckBox.IsChecked ?? false;
        AppSettings.StartFullScreen = StartFullScreenCheckBox.IsChecked ?? false;
        AppSettings.RememberLastPosition = RememberLastPositionCheckBox.IsChecked ?? false;
        
        AppSettings.DefaultVideoQuality = VideoQualityComboBox.SelectedIndex;
        AppSettings.EnableAutoplay = AutoplayCheckBox.IsChecked ?? true;
        AppSettings.MuteOnStartup = MuteOnStartupCheckBox.IsChecked ?? false;
        AppSettings.DefaultVolume = (int)VolumeSlider.Value;
        
        // Video quality control settings
        AppSettings.ForceVideoQuality = ForceVideoQualityCheckBox.IsChecked ?? false;
        AppSettings.ForcedVideoQuality = GetQualityComboBoxValue();
        AppSettings.LockQualityControls = LockQualityControlsCheckBox.IsChecked ?? false;
        AppSettings.HideQualityControls = HideQualityControlsCheckBox.IsChecked ?? false;
        AppSettings.OverrideUserQualityChanges = OverrideUserQualityChangesCheckBox.IsChecked ?? true;
        
        AppSettings.ClearCookiesOnExit = ClearCookiesOnExitCheckBox.IsChecked ?? false;
        AppSettings.BlockThirdPartyCookies = BlockThirdPartyCookiesCheckBox.IsChecked ?? false;
        AppSettings.DoNotTrack = DoNotTrackCheckBox.IsChecked ?? false;
        
        // Ad blocking settings
        AppSettings.BlockAds = BlockAdsCheckBox.IsChecked ?? false;
        AppSettings.BlockVideoAds = BlockVideoAdsCheckBox.IsChecked ?? true;
        AppSettings.BlockBannerAds = BlockBannerAdsCheckBox.IsChecked ?? true;
        AppSettings.BlockTrackingScripts = BlockTrackingScriptsCheckBox.IsChecked ?? true;
        AppSettings.AutoSkipAds = AutoSkipAdsCheckBox.IsChecked ?? true;
        AppSettings.ShowAdBlockStats = ShowAdBlockStatsCheckBox.IsChecked ?? false;
        
        // YouTube enhancement settings
        AppSettings.DisableAutoPreview = DisableAutoPreviewCheckBox.IsChecked ?? false;
        
        // Save home page setting
        if (YouTubeHomeRadio.IsChecked == true)
            AppSettings.HomePage = "https://www.youtube.com";
        else if (YouTubeTrendingRadio.IsChecked == true)
            AppSettings.HomePage = "https://www.youtube.com/feed/trending";
        else
            AppSettings.HomePage = CustomUrlTextBox.Text;
        
        AppSettings.Save();
    }

    private async void OkButton_Click(object sender, RoutedEventArgs e)
    {
        SaveSettings();
        
        // Apply quality settings to main window
        var mainWindow = Owner as MainWindow;
        if (mainWindow != null)
        {
            try
            {
                await mainWindow.UpdateQualitySettings();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating quality settings: {ex.Message}");
            }
        }
        
        DialogResult = true;
        Close();
    }

    private void CancelButton_Click(object sender, RoutedEventArgs e)
    {
        DialogResult = false;
        Close();
    }

    private void ClearDataButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "This will clear all cookies, cache, and stored data. Are you sure?",
            "Clear All Data",
            MessageBoxButton.YesNo,
            MessageBoxImage.Warning);
            
        if (result == MessageBoxResult.Yes)
        {
            // Clear WebView2 data through main window if available
            MessageBox.Show("Data cleared successfully.", "Clear Data", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void BlockAdsCheckBox_Changed(object sender, RoutedEventArgs e)
    {
        // Enable/disable ad blocking options based on main checkbox
        var isEnabled = BlockAdsCheckBox.IsChecked == true;
        AdBlockingOptionsPanel.IsEnabled = isEnabled;
    }

    private async void DisableAutoPreviewCheckBox_Changed(object sender, RoutedEventArgs e)
    {
        // Apply auto preview setting immediately when changed
        AppSettings.DisableAutoPreview = DisableAutoPreviewCheckBox.IsChecked ?? false;
        
        // Apply to main window immediately if available
        var mainWindow = Owner as MainWindow;
        if (mainWindow?.GetYouTubeEnhancer() != null)
        {
            try
            {
                await mainWindow.GetYouTubeEnhancer().UpdateSettings();
            }
            catch (Exception ex)
            {
                AppLogger.Instance.LogError("SettingsWindow", ex, "Error updating auto preview settings");
            }
        }
    }

    private void ViewBlockedStatsButton_Click(object sender, RoutedEventArgs e)
    {
        var statsWindow = new AdBlockStatsWindow();
        statsWindow.Owner = this;
        statsWindow.ShowDialog();
    }

    private void ResetAdBlockerButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "This will reset the ad blocker and clear all statistics. Continue?",
            "Reset Ad Blocker",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);
            
        if (result == MessageBoxResult.Yes)
        {
            // Reset ad blocker settings to defaults
            BlockVideoAdsCheckBox.IsChecked = true;
            BlockBannerAdsCheckBox.IsChecked = true;
            BlockTrackingScriptsCheckBox.IsChecked = true;
            AutoSkipAdsCheckBox.IsChecked = true;
            ShowAdBlockStatsCheckBox.IsChecked = false;
            
            MessageBox.Show("Ad blocker reset successfully.", "Reset Complete", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // Video Quality Control Event Handlers
    private void ForceVideoQualityCheckBox_Changed(object sender, RoutedEventArgs e)
    {
        var isEnabled = ForceVideoQualityCheckBox.IsChecked == true;
        QualityControlPanel.IsEnabled = isEnabled;
    }

    private void TestQualityButton_Click(object sender, RoutedEventArgs e)
    {
        if (ForceVideoQualityCheckBox.IsChecked != true)
        {
            MessageBox.Show("Enable 'Force specific video quality' first.", "Quality Control", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        var selectedQuality = GetQualityComboBoxValue();
        var qualityName = VideoQualityController.GetQualityDisplayName(selectedQuality);
        
        MessageBox.Show($"Quality will be forced to: {qualityName}\n\n" +
                       $"Lock Controls: {(LockQualityControlsCheckBox.IsChecked == true ? "Yes" : "No")}\n" +
                       $"Hide Controls: {(HideQualityControlsCheckBox.IsChecked == true ? "Yes" : "No")}\n" +
                       $"Override Changes: {(OverrideUserQualityChangesCheckBox.IsChecked == true ? "Yes" : "No")}", 
                       "Quality Control Test", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ResetQualityButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "Reset video quality control to defaults?",
            "Reset Quality Control",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);
            
        if (result == MessageBoxResult.Yes)
        {
            ForceVideoQualityCheckBox.IsChecked = false;
            ForcedVideoQualityComboBox.SelectedIndex = 0; // Auto
            LockQualityControlsCheckBox.IsChecked = false;
            HideQualityControlsCheckBox.IsChecked = false;
            OverrideUserQualityChangesCheckBox.IsChecked = true;
            
            MessageBox.Show("Quality control reset successfully.", "Reset Complete", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    // Helper methods for quality combo box
    private void SetQualityComboBoxValue(string quality)
    {
        for (int i = 0; i < ForcedVideoQualityComboBox.Items.Count; i++)
        {
            if (ForcedVideoQualityComboBox.Items[i] is ComboBoxItem item && 
                item.Tag?.ToString() == quality)
            {
                ForcedVideoQualityComboBox.SelectedIndex = i;
                return;
            }
        }
        ForcedVideoQualityComboBox.SelectedIndex = 0; // Default to Auto
    }

    private string GetQualityComboBoxValue()
    {
        if (ForcedVideoQualityComboBox.SelectedItem is ComboBoxItem selectedItem)
        {
            return selectedItem.Tag?.ToString() ?? "auto";
        }
        return "auto";
    }
}
