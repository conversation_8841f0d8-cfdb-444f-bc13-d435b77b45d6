using System.Diagnostics;
using System.Runtime;
using System.Collections.Concurrent;

namespace YouTubePlayerApp;

/// <summary>
/// Advanced memory profiler for detailed memory usage analysis
/// </summary>
public sealed class MemoryProfiler : IDisposable
{
    private static readonly Lazy<MemoryProfiler> _instance = new(() => new MemoryProfiler());
    public static MemoryProfiler Instance => _instance.Value;

    private readonly ConcurrentDictionary<string, MemorySnapshot> _snapshots;
    private readonly Timer _continuousProfileTimer;
    private readonly List<MemoryDataPoint> _memoryHistory;
    private readonly object _lockObject = new();
    private bool _disposed;
    private bool _isProfilingEnabled;

    // Memory tracking configuration
    private const int MAX_HISTORY_POINTS = 1000;
    private const int PROFILING_INTERVAL_MS = 5000; // 5 seconds

    private MemoryProfiler()
    {
        _snapshots = new ConcurrentDictionary<string, MemorySnapshot>();
        _memoryHistory = new List<MemoryDataPoint>();
        
        // Start continuous profiling timer
        _continuousProfileTimer = new Timer(ContinuousProfile, null, 
            TimeSpan.FromMilliseconds(PROFILING_INTERVAL_MS), 
            TimeSpan.FromMilliseconds(PROFILING_INTERVAL_MS));
    }

    /// <summary>
    /// Enable or disable continuous memory profiling
    /// </summary>
    public void SetProfilingEnabled(bool enabled)
    {
        _isProfilingEnabled = enabled;
        Debug.WriteLine($"Memory profiling {(enabled ? "enabled" : "disabled")}");
    }

    /// <summary>
    /// Take a memory snapshot with a given label
    /// </summary>
    public MemorySnapshot TakeSnapshot(string label)
    {
        var snapshot = new MemorySnapshot
        {
            Label = label,
            Timestamp = DateTime.UtcNow,
            ManagedMemoryBytes = GC.GetTotalMemory(false),
            WorkingSetBytes = Environment.WorkingSet,
            Gen0Collections = GC.CollectionCount(0),
            Gen1Collections = GC.CollectionCount(1),
            Gen2Collections = GC.CollectionCount(2)
        };

        // Get process memory info
        try
        {
            using var process = Process.GetCurrentProcess();
            snapshot.PrivateMemoryBytes = process.PrivateMemorySize64;
            snapshot.VirtualMemoryBytes = process.VirtualMemorySize64;
            snapshot.PagedMemoryBytes = process.PagedMemorySize64;
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error getting process memory info: {ex.Message}");
        }

        _snapshots.TryAdd(label, snapshot);
        return snapshot;
    }

    /// <summary>
    /// Compare two memory snapshots
    /// </summary>
    public MemoryComparison CompareSnapshots(string beforeLabel, string afterLabel)
    {
        if (!_snapshots.TryGetValue(beforeLabel, out var before) ||
            !_snapshots.TryGetValue(afterLabel, out var after))
        {
            throw new ArgumentException("One or both snapshot labels not found");
        }

        return new MemoryComparison
        {
            BeforeSnapshot = before,
            AfterSnapshot = after,
            ManagedMemoryDelta = after.ManagedMemoryBytes - before.ManagedMemoryBytes,
            WorkingSetDelta = after.WorkingSetBytes - before.WorkingSetBytes,
            PrivateMemoryDelta = after.PrivateMemoryBytes - before.PrivateMemoryBytes,
            VirtualMemoryDelta = after.VirtualMemoryBytes - before.VirtualMemoryBytes,
            Gen0CollectionsDelta = after.Gen0Collections - before.Gen0Collections,
            Gen1CollectionsDelta = after.Gen1Collections - before.Gen1Collections,
            Gen2CollectionsDelta = after.Gen2Collections - before.Gen2Collections,
            TimeDelta = after.Timestamp - before.Timestamp
        };
    }

    /// <summary>
    /// Get memory usage trends over time
    /// </summary>
    public MemoryTrends GetMemoryTrends()
    {
        lock (_lockObject)
        {
            if (_memoryHistory.Count < 2)
                return new MemoryTrends();

            var recent = _memoryHistory.TakeLast(10).ToList();
            var all = _memoryHistory.ToList();

            return new MemoryTrends
            {
                TotalDataPoints = all.Count,
                TimeSpan = all.Last().Timestamp - all.First().Timestamp,
                AverageMemoryMB = all.Average(p => p.ManagedMemoryBytes) / 1024.0 / 1024.0,
                PeakMemoryMB = all.Max(p => p.ManagedMemoryBytes) / 1024.0 / 1024.0,
                MinMemoryMB = all.Min(p => p.ManagedMemoryBytes) / 1024.0 / 1024.0,
                RecentTrendMB = recent.Count > 1 ? 
                    (recent.Last().ManagedMemoryBytes - recent.First().ManagedMemoryBytes) / 1024.0 / 1024.0 : 0,
                TotalGCCollections = all.LastOrDefault()?.Gen0Collections + 
                                   all.LastOrDefault()?.Gen1Collections + 
                                   all.LastOrDefault()?.Gen2Collections ?? 0,
                MemoryGrowthRate = CalculateGrowthRate(all)
            };
        }
    }

    /// <summary>
    /// Analyze memory leaks by looking for continuous growth patterns
    /// </summary>
    public MemoryLeakAnalysis AnalyzeMemoryLeaks()
    {
        lock (_lockObject)
        {
            if (_memoryHistory.Count < 10)
                return new MemoryLeakAnalysis { HasSufficientData = false };

            var recentPoints = _memoryHistory.TakeLast(20).ToList();
            var growthPoints = 0;
            var totalGrowth = 0L;

            for (int i = 1; i < recentPoints.Count; i++)
            {
                var growth = recentPoints[i].ManagedMemoryBytes - recentPoints[i - 1].ManagedMemoryBytes;
                if (growth > 0)
                {
                    growthPoints++;
                    totalGrowth += growth;
                }
            }

            var growthPercentage = (double)growthPoints / (recentPoints.Count - 1);
            var averageGrowthMB = totalGrowth / 1024.0 / 1024.0 / (recentPoints.Count - 1);

            return new MemoryLeakAnalysis
            {
                HasSufficientData = true,
                SuspiciousGrowthDetected = growthPercentage > 0.7 && averageGrowthMB > 1.0, // 70% growth points and >1MB average growth
                GrowthPercentage = growthPercentage,
                AverageGrowthMB = averageGrowthMB,
                ConsecutiveGrowthPoints = growthPoints,
                TotalDataPoints = recentPoints.Count,
                Recommendation = growthPercentage > 0.7 ? "Consider investigating potential memory leaks" : "Memory usage appears stable"
            };
        }
    }

    /// <summary>
    /// Force garbage collection and measure its effectiveness
    /// </summary>
    public GCEffectivenessReport ForceGCAndMeasure()
    {
        var beforeSnapshot = TakeSnapshot("BeforeGC");
        
        var stopwatch = Stopwatch.StartNew();
        
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        stopwatch.Stop();
        
        var afterSnapshot = TakeSnapshot("AfterGC");
        var comparison = CompareSnapshots("BeforeGC", "AfterGC");

        return new GCEffectivenessReport
        {
            GCDurationMs = stopwatch.ElapsedMilliseconds,
            MemoryFreedMB = -comparison.ManagedMemoryDelta / 1024.0 / 1024.0,
            WorkingSetFreedMB = -comparison.WorkingSetDelta / 1024.0 / 1024.0,
            Gen0Collections = comparison.Gen0CollectionsDelta,
            Gen1Collections = comparison.Gen1CollectionsDelta,
            Gen2Collections = comparison.Gen2CollectionsDelta,
            Effectiveness = comparison.ManagedMemoryDelta < 0 ? "Effective" : "Limited Effect"
        };
    }

    /// <summary>
    /// Get detailed memory breakdown
    /// </summary>
    public MemoryBreakdown GetMemoryBreakdown()
    {
        var managedMemory = GC.GetTotalMemory(false);
        var workingSet = Environment.WorkingSet;

        try
        {
            using var process = Process.GetCurrentProcess();
            
            return new MemoryBreakdown
            {
                ManagedMemoryMB = managedMemory / 1024.0 / 1024.0,
                UnmanagedMemoryMB = (process.WorkingSet64 - managedMemory) / 1024.0 / 1024.0,
                WorkingSetMB = process.WorkingSet64 / 1024.0 / 1024.0,
                PrivateMemoryMB = process.PrivateMemorySize64 / 1024.0 / 1024.0,
                VirtualMemoryMB = process.VirtualMemorySize64 / 1024.0 / 1024.0,
                PagedMemoryMB = process.PagedMemorySize64 / 1024.0 / 1024.0,
                NonPagedMemoryMB = process.NonpagedSystemMemorySize64 / 1024.0 / 1024.0,
                Gen0HeapSizeMB = GC.GetTotalMemory(false) / 1024.0 / 1024.0 // Approximate
            };
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Error getting memory breakdown: {ex.Message}");
            return new MemoryBreakdown
            {
                ManagedMemoryMB = managedMemory / 1024.0 / 1024.0,
                WorkingSetMB = workingSet / 1024.0 / 1024.0
            };
        }
    }

    private void ContinuousProfile(object? state)
    {
        if (_disposed || !_isProfilingEnabled) return;

        var dataPoint = new MemoryDataPoint
        {
            Timestamp = DateTime.UtcNow,
            ManagedMemoryBytes = GC.GetTotalMemory(false),
            WorkingSetBytes = Environment.WorkingSet,
            Gen0Collections = GC.CollectionCount(0),
            Gen1Collections = GC.CollectionCount(1),
            Gen2Collections = GC.CollectionCount(2)
        };

        lock (_lockObject)
        {
            _memoryHistory.Add(dataPoint);
            
            // Keep only the most recent data points
            if (_memoryHistory.Count > MAX_HISTORY_POINTS)
            {
                _memoryHistory.RemoveAt(0);
            }
        }
    }

    private double CalculateGrowthRate(List<MemoryDataPoint> points)
    {
        if (points.Count < 2) return 0;

        var first = points.First();
        var last = points.Last();
        var timeDelta = (last.Timestamp - first.Timestamp).TotalMinutes;
        var memoryDelta = (last.ManagedMemoryBytes - first.ManagedMemoryBytes) / 1024.0 / 1024.0;

        return timeDelta > 0 ? memoryDelta / timeDelta : 0; // MB per minute
    }

    public void ClearHistory()
    {
        lock (_lockObject)
        {
            _memoryHistory.Clear();
            _snapshots.Clear();
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        _continuousProfileTimer?.Dispose();
        ClearHistory();
    }
}

// Data structures for memory profiling
public class MemorySnapshot
{
    public string Label { get; set; } = "";
    public DateTime Timestamp { get; set; }
    public long ManagedMemoryBytes { get; set; }
    public long WorkingSetBytes { get; set; }
    public long PrivateMemoryBytes { get; set; }
    public long VirtualMemoryBytes { get; set; }
    public long PagedMemoryBytes { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
}

public class MemoryComparison
{
    public MemorySnapshot BeforeSnapshot { get; set; } = new();
    public MemorySnapshot AfterSnapshot { get; set; } = new();
    public long ManagedMemoryDelta { get; set; }
    public long WorkingSetDelta { get; set; }
    public long PrivateMemoryDelta { get; set; }
    public long VirtualMemoryDelta { get; set; }
    public int Gen0CollectionsDelta { get; set; }
    public int Gen1CollectionsDelta { get; set; }
    public int Gen2CollectionsDelta { get; set; }
    public TimeSpan TimeDelta { get; set; }
}

public class MemoryDataPoint
{
    public DateTime Timestamp { get; set; }
    public long ManagedMemoryBytes { get; set; }
    public long WorkingSetBytes { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
}

public class MemoryTrends
{
    public int TotalDataPoints { get; set; }
    public TimeSpan TimeSpan { get; set; }
    public double AverageMemoryMB { get; set; }
    public double PeakMemoryMB { get; set; }
    public double MinMemoryMB { get; set; }
    public double RecentTrendMB { get; set; }
    public int TotalGCCollections { get; set; }
    public double MemoryGrowthRate { get; set; } // MB per minute
}

public class MemoryLeakAnalysis
{
    public bool HasSufficientData { get; set; }
    public bool SuspiciousGrowthDetected { get; set; }
    public double GrowthPercentage { get; set; }
    public double AverageGrowthMB { get; set; }
    public int ConsecutiveGrowthPoints { get; set; }
    public int TotalDataPoints { get; set; }
    public string Recommendation { get; set; } = "";
}

public class GCEffectivenessReport
{
    public long GCDurationMs { get; set; }
    public double MemoryFreedMB { get; set; }
    public double WorkingSetFreedMB { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    public string Effectiveness { get; set; } = "";
}

public class MemoryBreakdown
{
    public double ManagedMemoryMB { get; set; }
    public double UnmanagedMemoryMB { get; set; }
    public double WorkingSetMB { get; set; }
    public double PrivateMemoryMB { get; set; }
    public double VirtualMemoryMB { get; set; }
    public double PagedMemoryMB { get; set; }
    public double NonPagedMemoryMB { get; set; }
    public double Gen0HeapSizeMB { get; set; }
}
