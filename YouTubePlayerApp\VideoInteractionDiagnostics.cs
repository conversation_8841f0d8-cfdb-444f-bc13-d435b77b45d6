using Microsoft.Web.WebView2.Core;
using System.Text.Json;

namespace YouTubePlayerApp;

/// <summary>
/// Diagnostics tool for video interaction issues
/// </summary>
public static class VideoInteractionDiagnostics
{
    public static async Task<string> DiagnoseVideoInteractionAsync(CoreWebView2 webView)
    {
        var diagnostics = new List<string>();
        
        try
        {
            // Check if video elements exist
            var videoCheckScript = @"
                (function() {
                    const videos = document.querySelectorAll('video');
                    const result = {
                        videoCount: videos.length,
                        videos: []
                    };
                    
                    videos.forEach((video, index) => {
                        result.videos.push({
                            index: index,
                            src: video.src || 'No src',
                            currentSrc: video.currentSrc || 'No currentSrc',
                            readyState: video.readyState,
                            paused: video.paused,
                            muted: video.muted,
                            volume: video.volume,
                            duration: video.duration,
                            currentTime: video.currentTime,
                            pointerEvents: getComputedStyle(video).pointerEvents,
                            display: getComputedStyle(video).display,
                            visibility: getComputedStyle(video).visibility,
                            zIndex: getComputedStyle(video).zIndex,
                            position: getComputedStyle(video).position,
                            hasClickListener: video.onclick !== null,
                            className: video.className,
                            id: video.id
                        });
                    });
                    
                    return JSON.stringify(result);
                })();
            ";
            
            var videoResult = await webView.ExecuteScriptAsync(videoCheckScript);
            diagnostics.Add($"Video Elements Check: {videoResult}");
            
            // Check YouTube player state
            var playerCheckScript = @"
                (function() {
                    const result = {
                        ytPlayerExists: typeof window.ytplayer !== 'undefined',
                        ytConfigExists: typeof window.yt !== 'undefined',
                        ytInitialDataExists: typeof window.ytInitialData !== 'undefined',
                        playerControls: {
                            playButton: document.querySelector('.ytp-play-button') !== null,
                            largePlayButton: document.querySelector('.ytp-large-play-button') !== null,
                            chromeBottom: document.querySelector('.ytp-chrome-bottom') !== null,
                            chromeControls: document.querySelector('.ytp-chrome-controls') !== null
                        },
                        thumbnails: document.querySelectorAll('ytd-thumbnail, .ytd-thumbnail, #thumbnail').length,
                        videoTitles: document.querySelectorAll('#video-title, .ytd-video-meta-block, a#video-title-link').length
                    };
                    
                    return JSON.stringify(result);
                })();
            ";
            
            var playerResult = await webView.ExecuteScriptAsync(playerCheckScript);
            diagnostics.Add($"YouTube Player Check: {playerResult}");
            
            // Check for blocking overlays or elements
            var overlayCheckScript = @"
                (function() {
                    const result = {
                        overlays: [],
                        blockingElements: []
                    };
                    
                    // Check for elements that might be blocking clicks
                    const allElements = document.querySelectorAll('*');
                    allElements.forEach(el => {
                        const style = getComputedStyle(el);
                        if (style.position === 'fixed' || style.position === 'absolute') {
                            if (parseInt(style.zIndex) > 1000) {
                                result.overlays.push({
                                    tagName: el.tagName,
                                    className: el.className,
                                    id: el.id,
                                    zIndex: style.zIndex,
                                    pointerEvents: style.pointerEvents,
                                    display: style.display,
                                    visibility: style.visibility
                                });
                            }
                        }
                        
                        if (style.pointerEvents === 'none' && el.tagName === 'VIDEO') {
                            result.blockingElements.push({
                                tagName: el.tagName,
                                className: el.className,
                                id: el.id,
                                pointerEvents: style.pointerEvents
                            });
                        }
                    });
                    
                    return JSON.stringify(result);
                })();
            ";
            
            var overlayResult = await webView.ExecuteScriptAsync(overlayCheckScript);
            diagnostics.Add($"Overlay/Blocking Elements Check: {overlayResult}");
            
            // Check event listeners
            var eventCheckScript = @"
                (function() {
                    const result = {
                        documentClickListeners: 0,
                        videoClickListeners: 0,
                        bodyClickListeners: 0
                    };
                    
                    // This is a simplified check - actual event listener detection is complex
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        if (video.onclick || video.addEventListener) {
                            result.videoClickListeners++;
                        }
                    });
                    
                    return JSON.stringify(result);
                })();
            ";
            
            var eventResult = await webView.ExecuteScriptAsync(eventCheckScript);
            diagnostics.Add($"Event Listeners Check: {eventResult}");
            
            // Check CSS that might interfere
            var cssCheckScript = @"
                (function() {
                    const result = {
                        suspiciousCSS: []
                    };
                    
                    const videos = document.querySelectorAll('video');
                    videos.forEach((video, index) => {
                        const style = getComputedStyle(video);
                        if (style.pointerEvents === 'none' || 
                            style.display === 'none' || 
                            style.visibility === 'hidden' ||
                            style.opacity === '0') {
                            result.suspiciousCSS.push({
                                videoIndex: index,
                                pointerEvents: style.pointerEvents,
                                display: style.display,
                                visibility: style.visibility,
                                opacity: style.opacity,
                                cursor: style.cursor
                            });
                        }
                    });
                    
                    return JSON.stringify(result);
                })();
            ";
            
            var cssResult = await webView.ExecuteScriptAsync(cssCheckScript);
            diagnostics.Add($"CSS Issues Check: {cssResult}");
            
        }
        catch (Exception ex)
        {
            diagnostics.Add($"Diagnostics Error: {ex.Message}");
        }
        
        return string.Join("\n\n", diagnostics);
    }
    
    public static async Task<bool> AttemptVideoInteractionFixAsync(CoreWebView2 webView)
    {
        try
        {
            var fixScript = @"
                (function() {
                    console.log('[VideoFix] Attempting to fix video interaction...');
                    let fixesApplied = 0;
                    
                    // Fix 1: Ensure all video elements are clickable
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        video.style.pointerEvents = 'auto';
                        video.style.cursor = 'pointer';
                        video.style.zIndex = '1';
                        fixesApplied++;
                    });
                    
                    // Fix 2: Ensure player controls are clickable
                    const controls = [
                        '.ytp-chrome-bottom',
                        '.ytp-chrome-controls', 
                        '.ytp-play-button',
                        '.ytp-large-play-button',
                        '.ytp-progress-bar',
                        '.ytp-volume-slider'
                    ];
                    
                    controls.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            el.style.pointerEvents = 'auto';
                            el.style.cursor = 'pointer';
                            el.style.zIndex = '9999';
                            fixesApplied++;
                        });
                    });
                    
                    // Fix 3: Remove any blocking overlays
                    const overlays = document.querySelectorAll('[style*=""pointer-events: none""]');
                    overlays.forEach(overlay => {
                        if (overlay.tagName !== 'VIDEO') {
                            overlay.style.pointerEvents = 'auto';
                            fixesApplied++;
                        }
                    });
                    
                    // Fix 4: Ensure thumbnails are clickable
                    const thumbnails = document.querySelectorAll('ytd-thumbnail, .ytd-thumbnail, #thumbnail');
                    thumbnails.forEach(thumb => {
                        thumb.style.pointerEvents = 'auto';
                        thumb.style.cursor = 'pointer';
                        fixesApplied++;
                    });
                    
                    // Fix 5: Force click handlers on videos
                    videos.forEach(video => {
                        if (!video.hasAttribute('data-click-fixed')) {
                            video.setAttribute('data-click-fixed', 'true');
                            video.addEventListener('click', function(e) {
                                console.log('[VideoFix] Video clicked - attempting play/pause');
                                if (video.paused) {
                                    video.play().then(() => {
                                        console.log('[VideoFix] Video play successful');
                                    }).catch(err => {
                                        console.log('[VideoFix] Video play failed:', err);
                                    });
                                } else {
                                    video.pause();
                                    console.log('[VideoFix] Video paused');
                                }
                            }, true);
                            fixesApplied++;
                        }
                    });
                    
                    console.log('[VideoFix] Applied', fixesApplied, 'fixes');
                    return fixesApplied;
                })();
            ";
            
            var result = await webView.ExecuteScriptAsync(fixScript);
            return int.TryParse(result, out int fixes) && fixes > 0;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[VideoFix] Error applying fixes: {ex.Message}");
            return false;
        }
    }
}