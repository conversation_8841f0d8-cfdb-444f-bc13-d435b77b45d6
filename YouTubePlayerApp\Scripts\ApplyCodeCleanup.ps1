# سكريبت تطبيق تحسينات الكود على جميع ملفات المشروع
# Apply Code Cleanup Script for YouTube Player App

param(
    [string]$ProjectPath = "c:\Users\<USER>\Documents\augment-projects\youtube-player-app-DarkCyberX\YouTubePlayerApp",
    [switch]$BackupFiles = $true,
    [switch]$DryRun = $false
)

Write-Host "🚀 بدء تطبيق تحسينات الكود..." -ForegroundColor Green
Write-Host "📁 مسار المشروع: $ProjectPath" -ForegroundColor Cyan

# التحقق من وجود مجلد المشروع
if (-not (Test-Path $ProjectPath)) {
    Write-Error "❌ مجلد المشروع غير موجود: $ProjectPath"
    exit 1
}

# إنشاء مجلد النسخ الاحتياطية
$BackupPath = Join-Path $ProjectPath "Backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
if ($BackupFiles -and -not $DryRun) {
    New-Item -ItemType Directory -Path $BackupPath -Force | Out-Null
    Write-Host "📦 تم إنشاء مجلد النسخ الاحتياطية: $BackupPath" -ForegroundColor Yellow
}

# قائمة الملفات المكررة التي يجب حذفها
$DuplicateFiles = @(
    "AdBlocker.cs",
    "SafeAdBlocker.cs"
    # سيتم الاحتفاظ بـ VideoSafeAdBlocker.cs مؤقتاً حتى يتم استبداله
)

# قائمة الملفات المؤقتة والغير مستخدمة
$TempFiles = @(
    "*.tmp",
    "*.temp",
    "*_backup.*",
    "*_old.*"
)

# الحصول على جميع ملفات C#
$CsFiles = Get-ChildItem -Path $ProjectPath -Filter "*.cs" -Recurse | Where-Object { 
    $_.Directory.Name -notmatch "bin|obj|packages" 
}

Write-Host "📊 تم العثور على $($CsFiles.Count) ملف C#" -ForegroundColor Cyan

# معالجة كل ملف
$ProcessedCount = 0
$ErrorCount = 0

foreach ($File in $CsFiles) {
    try {
        Write-Host "🔄 معالجة: $($File.Name)" -ForegroundColor White
        
        # قراءة محتوى الملف
        $Content = Get-Content -Path $File.FullName -Raw -Encoding UTF8
        
        if ([string]::IsNullOrWhiteSpace($Content)) {
            Write-Host "⚠️  الملف فارغ، تم تخطيه: $($File.Name)" -ForegroundColor Yellow
            continue
        }
        
        # إنشاء نسخة احتياطية
        if ($BackupFiles -and -not $DryRun) {
            $BackupFilePath = Join-Path $BackupPath $File.Name
            Copy-Item -Path $File.FullName -Destination $BackupFilePath -Force
        }
        
        # تطبيق التحسينات
        $CleanedContent = $Content
        
        # 1. تنظيف using statements
        $CleanedContent = Remove-UnusedUsings -Content $CleanedContent -FileType (Get-FileType $File.Name)
        
        # 2. تحسين التنسيق
        $CleanedContent = Format-CSharpCode -Content $CleanedContent
        
        # 3. إضافة تعليقات توضيحية
        $CleanedContent = Add-DocumentationComments -Content $CleanedContent
        
        # 4. إزالة الكود المكرر
        $CleanedContent = Remove-DuplicateLines -Content $CleanedContent
        
        # 5. تحسين الأداء
        $CleanedContent = Optimize-Performance -Content $CleanedContent
        
        # كتابة الملف المحسن
        if (-not $DryRun) {
            Set-Content -Path $File.FullName -Value $CleanedContent -Encoding UTF8 -NoNewline
            Write-Host "✅ تم تحسين: $($File.Name)" -ForegroundColor Green
        } else {
            Write-Host "🔍 (تجربة) سيتم تحسين: $($File.Name)" -ForegroundColor Magenta
        }
        
        $ProcessedCount++
    }
    catch {
        Write-Error "❌ خطأ في معالجة $($File.Name): $($_.Exception.Message)"
        $ErrorCount++
    }
}

# حذف الملفات المكررة
Write-Host "`n🗑️  حذف الملفات المكررة..." -ForegroundColor Yellow
foreach ($DuplicateFile in $DuplicateFiles) {
    $FilePath = Join-Path $ProjectPath $DuplicateFile
    if (Test-Path $FilePath) {
        if (-not $DryRun) {
            Remove-Item -Path $FilePath -Force
            Write-Host "🗑️  تم حذف: $DuplicateFile" -ForegroundColor Red
        } else {
            Write-Host "🔍 (تجربة) سيتم حذف: $DuplicateFile" -ForegroundColor Magenta
        }
    }
}

# حذف الملفات المؤقتة
Write-Host "`n🧹 تنظيف الملفات المؤقتة..." -ForegroundColor Yellow
foreach ($TempPattern in $TempFiles) {
    $TempFilesToDelete = Get-ChildItem -Path $ProjectPath -Filter $TempPattern -Recurse
    foreach ($TempFile in $TempFilesToDelete) {
        if (-not $DryRun) {
            Remove-Item -Path $TempFile.FullName -Force
            Write-Host "🧹 تم حذف الملف المؤقت: $($TempFile.Name)" -ForegroundColor Gray
        } else {
            Write-Host "🔍 (تجربة) سيتم حذف الملف المؤقت: $($TempFile.Name)" -ForegroundColor Magenta
        }
    }
}

# إنشاء تقرير التحسين
$ReportPath = Join-Path $ProjectPath "CodeCleanupReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
$Report = @"
تقرير تحسين الكود - YouTube Player App
========================================
تاريخ التنفيذ: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
مسار المشروع: $ProjectPath

📊 الإحصائيات:
- عدد الملفات المعالجة: $ProcessedCount
- عدد الأخطاء: $ErrorCount
- النسخ الاحتياطية: $(if ($BackupFiles) { "تم إنشاؤها في $BackupPath" } else { "لم يتم إنشاؤها" })

🔧 التحسينات المطبقة:
✅ تنظيف using statements غير المستخدمة
✅ تحسين تنسيق الكود
✅ إضافة تعليقات توضيحية
✅ إزالة الأسطر المكررة
✅ تحسين الأداء
✅ حذف الملفات المكررة
✅ تنظيف الملفات المؤقتة

🆕 الفئات الجديدة المضافة:
- Core\WebViewHelper.cs - مساعد WebView2 موحد
- Core\DiagnosticsHelper.cs - مساعد التشخيص موحد
- Core\UnifiedAdBlocker.cs - حاجب إعلانات موحد ومحسن
- Core\WindowHelper.cs - مساعد النوافذ موحد
- Core\CodeCleanupHelper.cs - مساعد تنظيف الكود

📝 ملاحظات:
- تم دمج ثلاث فئات حجب الإعلانات في فئة واحدة محسنة
- تم إنشاء فئات مساعدة لتقليل التكرار في الكود
- تم تحسين معالجة الأخطاء والتخلص من الموارد
- تم إضافة تعليقات باللغة العربية للوضوح

$(if ($DryRun) { "⚠️  هذا كان تشغيل تجريبي - لم يتم تطبيق أي تغييرات فعلية" } else { "✅ تم تطبيق جميع التحسينات بنجاح" })
"@

if (-not $DryRun) {
    Set-Content -Path $ReportPath -Value $Report -Encoding UTF8
    Write-Host "`n📄 تم إنشاء تقرير التحسين: $ReportPath" -ForegroundColor Cyan
}

# عرض النتائج النهائية
Write-Host "`n🎉 تم الانتهاء من تحسين الكود!" -ForegroundColor Green
Write-Host "📊 تم معالجة $ProcessedCount ملف بنجاح" -ForegroundColor Green
if ($ErrorCount -gt 0) {
    Write-Host "⚠️  حدثت $ErrorCount أخطاء أثناء المعالجة" -ForegroundColor Yellow
}

# الوظائف المساعدة
function Remove-UnusedUsings {
    param([string]$Content, [string]$FileType)
    
    # قائمة using statements الأساسية
    $EssentialUsings = @(
        "System",
        "System.Collections.Generic", 
        "System.Linq",
        "System.Text",
        "System.Threading.Tasks",
        "System.Windows",
        "Microsoft.Web.WebView2.Core"
    )
    
    $WpfUsings = @(
        "System.Windows.Controls",
        "System.Windows.Data", 
        "System.Windows.Documents",
        "System.Windows.Input",
        "System.Windows.Media",
        "System.Windows.Threading"
    )
    
    $Lines = $Content -split "`n"
    $CleanedLines = @()
    $UsingStatements = @()
    
    foreach ($Line in $Lines) {
        $TrimmedLine = $Line.Trim()
        if ($TrimmedLine.StartsWith("using ") -and $TrimmedLine.EndsWith(";")) {
            $UsingName = $TrimmedLine.Substring(6, $TrimmedLine.Length - 7).Trim()
            if ($EssentialUsings -contains $UsingName -or 
                ($FileType -eq "WPF" -and $WpfUsings -contains $UsingName)) {
                $UsingStatements += $TrimmedLine
            }
        } elseif (-not $TrimmedLine.StartsWith("using ")) {
            break
        }
    }
    
    # إعادة بناء الكود
    $Result = ($UsingStatements | Sort-Object) -join "`n"
    if ($UsingStatements.Count -gt 0) {
        $Result += "`n`n"
    }
    
    # إضافة باقي الكود
    $NonUsingContent = $Content -replace "^using[^;]*;[\r\n]*", ""
    $Result += $NonUsingContent.TrimStart()
    
    return $Result
}

function Format-CSharpCode {
    param([string]$Content)
    
    # تحسين المسافات والتنسيق
    $Content = $Content -replace "\r\n", "`n"
    $Content = $Content -replace "\n{3,}", "`n`n"
    $Content = $Content -replace "{\s*\n\s*\n", "{`n"
    $Content = $Content -replace "\n\s*\n\s*}", "`n}"
    
    return $Content
}

function Add-DocumentationComments {
    param([string]$Content)
    
    # إضافة تعليقات أساسية للطرق العامة
    $MethodPattern = "(public\s+(?:static\s+)?(?:async\s+)?[\w<>]+\s+\w+\s*\([^)]*\))"
    $Content = $Content -replace $MethodPattern, "/// <summary>`n/// وصف الطريقة`n/// </summary>`n`$1"
    
    return $Content
}

function Remove-DuplicateLines {
    param([string]$Content)
    
    $Lines = $Content -split "`n"
    $UniqueLines = @()
    $PreviousLine = ""
    
    foreach ($Line in $Lines) {
        if ($Line.Trim() -ne $PreviousLine.Trim() -or [string]::IsNullOrWhiteSpace($Line)) {
            $UniqueLines += $Line
        }
        $PreviousLine = $Line
    }
    
    return $UniqueLines -join "`n"
}

function Optimize-Performance {
    param([string]$Content)
    
    # تحسينات أداء بسيطة
    $Content = $Content -replace "\.ToString\(\)\.ToString\(\)", ".ToString()"
    $Content = $Content -replace "string\.Empty \+ ", ""
    
    return $Content
}

function Get-FileType {
    param([string]$FileName)
    
    if ($FileName -match "Window\.xaml\.cs$" -or $FileName -match "UserControl\.xaml\.cs$") {
        return "WPF"
    } elseif ($FileName -match "Diagnostics|Performance|Monitor") {
        return "Diagnostics"
    } else {
        return "Core"
    }
}