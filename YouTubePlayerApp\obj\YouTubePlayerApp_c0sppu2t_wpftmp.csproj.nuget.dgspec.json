{"format": 1, "restore": {"c:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app-DarkCyberX\\YouTubePlayerApp\\YouTubePlayerApp.csproj": {}}, "projects": {"c:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app-DarkCyberX\\YouTubePlayerApp\\YouTubePlayerApp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "c:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app-DarkCyberX\\YouTubePlayerApp\\YouTubePlayerApp.csproj", "projectName": "YouTubePlayerApp", "projectPath": "c:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app-DarkCyberX\\YouTubePlayerApp\\YouTubePlayerApp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "c:\\Users\\<USER>\\Documents\\augment-projects\\youtube-player-app-DarkCyberX\\YouTubePlayerApp\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"Microsoft.Web.WebView2": {"target": "Package", "version": "[1.0.3351.48, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.412/PortableRuntimeIdentifierGraph.json"}}}}}