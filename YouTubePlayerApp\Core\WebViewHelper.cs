using Microsoft.Web.WebView2.Core;
using System.Text;

namespace YouTubePlayerApp.Core;

/// <summary>
/// مساعد مشترك لوظائف WebView2 لتجنب التكرار في الكود
/// </summary>
public static class WebViewHelper
{
    /// <summary>
    /// تكوين إعدادات WebView2 الأساسية لـ YouTube
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    public static void ConfigureBasicSettings(CoreWebView2 webView)
    {
        if (webView == null) return;

        // تكوين User Agent للتوافق الأمثل مع YouTube
        webView.Settings.UserAgent = 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        
        // تمكين الميزات الأساسية
        webView.Settings.AreHostObjectsAllowed = true;
        webView.Settings.IsWebMessageEnabled = true;
        webView.Settings.AreDevToolsEnabled = false;
        webView.Settings.IsScriptEnabled = true;
        webView.Settings.AreDefaultScriptDialogsEnabled = true;
        webView.Settings.IsGeneralAutofillEnabled = true;
        webView.Settings.IsPasswordAutosaveEnabled = false;
        webView.Settings.AreBrowserAcceleratorKeysEnabled = true;
        webView.Settings.IsSwipeNavigationEnabled = false;
        webView.Settings.AreDefaultContextMenusEnabled = true;
    }

    /// <summary>
    /// تطبيق تحسينات YouTube المتقدمة
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    public static async Task ApplyYouTubeOptimizationsAsync(CoreWebView2 webView)
    {
        if (webView == null) return;

        try
        {
            // تعطيل الإشعارات
            await webView.AddScriptToExecuteOnDocumentCreatedAsync(@"
                Object.defineProperty(Notification, 'permission', {
                    get() { return 'denied'; }
                });
            ");
            
            // تطبيق إعدادات عدم التتبع
            if (AppSettings.DoNotTrack)
            {
                await webView.AddScriptToExecuteOnDocumentCreatedAsync(@"
                    Object.defineProperty(Navigator.prototype, 'doNotTrack', {
                        get() { return '1'; }
                    });
                ");
            }
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تطبيق تحسينات YouTube: {ex.Message}");
        }
    }

    /// <summary>
    /// حقن CSS لحجب الإعلانات
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    public static async Task InjectAdBlockingCssAsync(CoreWebView2 webView)
    {
        if (webView == null || !AppSettings.BlockAds) return;

        try
        {
            await webView.AddScriptToExecuteOnDocumentCreatedAsync(@"
                // حقن CSS فوري لحجب الإعلانات
                const adBlockCSS = `
                    /* إعلانات الفيديو */
                    .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
                    .ytp-ad-image-overlay, .ytp-ad-text-overlay,
                    .ytp-ad-player-overlay, .ytp-ad-skip-button-container,
                    
                    /* إعلانات البانر والعرض */
                    .ytd-promoted-video-renderer, .ytd-promoted-sparkles-web-renderer,
                    .ytd-banner-promo-renderer, .ytd-in-feed-ad-layout-renderer,
                    .ytd-display-ad-renderer, .ytd-companion-slot-renderer,
                    
                    /* إعلانات الشريط الجانبي */
                    #player-ads, #masthead-ad, #watch-sidebar-ads,
                    .ytd-action-companion-ad-renderer,
                    
                    /* عروض Premium */
                    .ytd-popup-container[dialog][role='dialog'],
                    .ytd-mealbar-promo-renderer, .ytd-premium-upsell-renderer,
                    .ytd-upsell-dialog-renderer, .ytd-background-promo-renderer,
                    
                    /* إعلانات التسوق */
                    .ytd-product-details-renderer, .ytd-merch-shelf-renderer,
                    .ytd-shopping-shelf-renderer, .ytd-product-shelf-renderer,
                    
                    /* أنماط الإعلانات العامة */
                    [class*='promoted'], [class*='sponsor'], [class*='advertisement'],
                    [id*='promoted'], [id*='sponsor'], [id*='advertisement'],
                    [data-is-ad='true'], [data-promoted='true'], [data-ad-status]
                    { display: none !important; visibility: hidden !important; }
                `;
                
                const style = document.createElement('style');
                style.textContent = adBlockCSS;
                (document.head || document.documentElement).appendChild(style);
            ");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في حقن CSS لحجب الإعلانات: {ex.Message}");
        }
    }

    /// <summary>
    /// حقن سكريبت لضمان تفاعل الفيديو
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    public static async Task InjectVideoInteractionScriptAsync(CoreWebView2 webView)
    {
        if (webView == null) return;

        try
        {
            await webView.AddScriptToExecuteOnDocumentCreatedAsync(@"
                // ضمان عمل تفاعل الفيديو
                (function() {
                    'use strict';
                    
                    // حماية وظائف الفيديو الأساسية
                    const protectVideoFunctions = () => {
                        if (window.ytplayer && window.ytplayer.config) {
                            // حماية إعدادات المشغل
                            Object.defineProperty(window.ytplayer.config, 'args', {
                                writable: false,
                                configurable: false
                            });
                        }
                        
                        // ضمان عمل أحداث الماوس والكيبورد
                        document.addEventListener('click', function(e) {
                            e.stopPropagation();
                        }, true);
                        
                        document.addEventListener('keydown', function(e) {
                            e.stopPropagation();
                        }, true);
                    };
                    
                    // تطبيق الحماية عند تحميل الصفحة
                    if (document.readyState === 'loading') {
                        document.addEventListener('DOMContentLoaded', protectVideoFunctions);
                    } else {
                        protectVideoFunctions();
                    }
                    
                    // مراقبة مستمرة للحماية
                    setInterval(protectVideoFunctions, 2000);
                })();
            ");
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في حقن سكريبت تفاعل الفيديو: {ex.Message}");
        }
    }

    /// <summary>
    /// تكوين أذونات WebView2
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    public static void ConfigurePermissions(CoreWebView2 webView)
    {
        if (webView == null) return;

        webView.PermissionRequested += (sender, e) =>
        {
            // السماح بالأذونات الضرورية لتشغيل الفيديو مع حجب التتبع
            switch (e.PermissionKind)
            {
                case CoreWebView2PermissionKind.Microphone:
                case CoreWebView2PermissionKind.Camera:
                    e.State = CoreWebView2PermissionState.Allow;
                    break;
                case CoreWebView2PermissionKind.Geolocation:
                    e.State = AppSettings.DoNotTrack ? CoreWebView2PermissionState.Deny : CoreWebView2PermissionState.Allow;
                    break;
                case CoreWebView2PermissionKind.Notifications:
                    e.State = CoreWebView2PermissionState.Deny; // حجب الإشعارات
                    break;
                default:
                    e.State = CoreWebView2PermissionState.Allow;
                    break;
            }
        };
    }

    /// <summary>
    /// تنفيذ سكريبت JavaScript مع معالجة الأخطاء
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    /// <param name="script">السكريبت المراد تنفيذه</param>
    /// <param name="operationName">اسم العملية للتسجيل</param>
    /// <returns>نتيجة تنفيذ السكريبت</returns>
    public static async Task<string?> ExecuteScriptSafelyAsync(CoreWebView2 webView, string script, string operationName)
    {
        if (webView == null || string.IsNullOrEmpty(script)) return null;

        try
        {
            var result = await webView.ExecuteScriptAsync(script);
            AppLogger.LogInfo($"تم تنفيذ {operationName} بنجاح");
            return result;
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تنفيذ {operationName}: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// تحديث عنوان النافذة بناءً على صفحة YouTube
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    /// <param name="window">النافذة الرئيسية</param>
    public static void UpdateWindowTitle(CoreWebView2 webView, System.Windows.Window window)
    {
        if (webView == null || window == null) return;

        try
        {
            var title = webView.DocumentTitle;
            if (!string.IsNullOrEmpty(title))
            {
                window.Title = $"YouTube Desktop Player - {title}";
            }
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في تحديث عنوان النافذة: {ex.Message}");
        }
    }
}