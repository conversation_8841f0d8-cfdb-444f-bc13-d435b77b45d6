﻿#pragma checksum "..\..\..\AdBlockStatsWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C12E17D4E845276DF16992D6D91AF37B8A193BBE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using YouTubePlayerApp;


namespace YouTubePlayerApp {
    
    
    /// <summary>
    /// AdBlockStatsWindow
    /// </summary>
    public partial class AdBlockStatsWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 40 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TotalBlockedText;
        
        #line default
        #line hidden
        
        
        #line 45 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock VideoAdsBlockedText;
        
        #line default
        #line hidden
        
        
        #line 50 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ElementsBlockedText;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TrackingBlockedText;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox BlockedUrlsList;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SessionStartText;
        
        #line default
        #line hidden
        
        
        #line 90 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AdBlockerStatusText;
        
        #line default
        #line hidden
        
        
        #line 95 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DataSavedText;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 107 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearStatsButton;
        
        #line default
        #line hidden
        
        
        #line 109 "..\..\..\AdBlockStatsWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/YouTubePlayerApp;component/adblockstatswindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\AdBlockStatsWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.TotalBlockedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.VideoAdsBlockedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.ElementsBlockedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.TrackingBlockedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.BlockedUrlsList = ((System.Windows.Controls.ListBox)(target));
            return;
            case 6:
            this.SessionStartText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.AdBlockerStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.DataSavedText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 106 "..\..\..\AdBlockStatsWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ClearStatsButton = ((System.Windows.Controls.Button)(target));
            
            #line 108 "..\..\..\AdBlockStatsWindow.xaml"
            this.ClearStatsButton.Click += new System.Windows.RoutedEventHandler(this.ClearStatsButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 110 "..\..\..\AdBlockStatsWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

