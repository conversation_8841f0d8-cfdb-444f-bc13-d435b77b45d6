﻿#pragma checksum "..\..\..\MemoryProfilerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "6E6B5FD829EA77EBF032B75CFC7A899CF9A9F557"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace YouTubePlayerApp {
    
    
    /// <summary>
    /// MemoryProfilerWindow
    /// </summary>
    public partial class MemoryProfilerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 19 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartProfilingButton;
        
        #line default
        #line hidden
        
        
        #line 20 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StopProfilingButton;
        
        #line default
        #line hidden
        
        
        #line 21 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TakeSnapshotButton;
        
        #line default
        #line hidden
        
        
        #line 22 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ForceGCButton;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearDataButton;
        
        #line default
        #line hidden
        
        
        #line 24 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ManagedMemoryText;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkingSetText;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PrivateMemoryText;
        
        #line default
        #line hidden
        
        
        #line 66 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GCCollectionsText;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid MemoryBreakdownGrid;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid PerformanceMetricsGrid;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AverageMemoryText;
        
        #line default
        #line hidden
        
        
        #line 122 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PeakMemoryText;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GrowthRateText;
        
        #line default
        #line hidden
        
        
        #line 136 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LeakAnalysisPanel;
        
        #line default
        #line hidden
        
        
        #line 142 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MemoryHistoryPanel;
        
        #line default
        #line hidden
        
        
        #line 161 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SnapshotLabelTextBox;
        
        #line default
        #line hidden
        
        
        #line 163 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TakeSnapshotWithLabelButton;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CompareFromComboBox;
        
        #line default
        #line hidden
        
        
        #line 167 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox CompareToComboBox;
        
        #line default
        #line hidden
        
        
        #line 168 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CompareSnapshotsButton;
        
        #line default
        #line hidden
        
        
        #line 181 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox SnapshotsListBox;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel SnapshotDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ForceGCWithAnalysisButton;
        
        #line default
        #line hidden
        
        
        #line 221 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastGCAnalysisText;
        
        #line default
        #line hidden
        
        
        #line 226 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel GCAnalysisPanel;
        
        #line default
        #line hidden
        
        
        #line 245 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse ProfilingIndicator;
        
        #line default
        #line hidden
        
        
        #line 249 "..\..\..\MemoryProfilerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProfilingStatusText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/YouTubePlayerApp;component/memoryprofilerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MemoryProfilerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.StartProfilingButton = ((System.Windows.Controls.Button)(target));
            
            #line 19 "..\..\..\MemoryProfilerWindow.xaml"
            this.StartProfilingButton.Click += new System.Windows.RoutedEventHandler(this.StartProfiling_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.StopProfilingButton = ((System.Windows.Controls.Button)(target));
            
            #line 20 "..\..\..\MemoryProfilerWindow.xaml"
            this.StopProfilingButton.Click += new System.Windows.RoutedEventHandler(this.StopProfiling_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.TakeSnapshotButton = ((System.Windows.Controls.Button)(target));
            
            #line 21 "..\..\..\MemoryProfilerWindow.xaml"
            this.TakeSnapshotButton.Click += new System.Windows.RoutedEventHandler(this.TakeSnapshot_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.ForceGCButton = ((System.Windows.Controls.Button)(target));
            
            #line 22 "..\..\..\MemoryProfilerWindow.xaml"
            this.ForceGCButton.Click += new System.Windows.RoutedEventHandler(this.ForceGC_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ClearDataButton = ((System.Windows.Controls.Button)(target));
            
            #line 23 "..\..\..\MemoryProfilerWindow.xaml"
            this.ClearDataButton.Click += new System.Windows.RoutedEventHandler(this.ClearData_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 24 "..\..\..\MemoryProfilerWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.ManagedMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.WorkingSetText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.PrivateMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.GCCollectionsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.MemoryBreakdownGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 12:
            this.PerformanceMetricsGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 13:
            this.AverageMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            this.PeakMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.GrowthRateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 16:
            this.LeakAnalysisPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 17:
            this.MemoryHistoryPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 18:
            this.SnapshotLabelTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.TakeSnapshotWithLabelButton = ((System.Windows.Controls.Button)(target));
            
            #line 164 "..\..\..\MemoryProfilerWindow.xaml"
            this.TakeSnapshotWithLabelButton.Click += new System.Windows.RoutedEventHandler(this.TakeSnapshotWithLabel_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.CompareFromComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 21:
            this.CompareToComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 22:
            this.CompareSnapshotsButton = ((System.Windows.Controls.Button)(target));
            
            #line 169 "..\..\..\MemoryProfilerWindow.xaml"
            this.CompareSnapshotsButton.Click += new System.Windows.RoutedEventHandler(this.CompareSnapshots_Click);
            
            #line default
            #line hidden
            return;
            case 23:
            this.SnapshotsListBox = ((System.Windows.Controls.ListBox)(target));
            
            #line 181 "..\..\..\MemoryProfilerWindow.xaml"
            this.SnapshotsListBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.SnapshotsListBox_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 24:
            this.SnapshotDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 25:
            this.ForceGCWithAnalysisButton = ((System.Windows.Controls.Button)(target));
            
            #line 219 "..\..\..\MemoryProfilerWindow.xaml"
            this.ForceGCWithAnalysisButton.Click += new System.Windows.RoutedEventHandler(this.ForceGCWithAnalysis_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.LastGCAnalysisText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 27:
            this.GCAnalysisPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 28:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.ProfilingIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 30:
            this.ProfilingStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

