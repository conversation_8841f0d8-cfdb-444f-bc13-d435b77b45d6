0:01:09 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:01:09 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:09 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:01:09 Microsoft.Shutdown.TabStripModel.CloseTabs
0:01:09 Microsoft.Shutdown.TabStripModel.CloseTabs_EdgeBeforeClosingAllTabs
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:01:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:01:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:01:09 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:01:09 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:01:09 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:01:09 Microsoft.UnloadController.ClearUnloadState
0:01:09 Browser2 Close Tab2 at 0
0:01:09 Widget Closed: StatusBubble
0:01:09 Tab2 WebContentsDestroyed
0:01:09 Microsoft.UnloadController.TabStripEmpty
0:01:09 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:09 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:09 Microsoft.Shutdown.OnWindowClosing
0:01:09 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:09 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:09 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:09 Widget Closed: BrowserFrame
0:01:09 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:09 Microsoft.Shutdown.OnWindowClosing
0:01:09 Microsoft.Shutdown.OnWindowClosing_DeleteScheduled
0:01:09 Microsoft.Browser_Removed
0:01:12 Tab1 StartNav21 #reload
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Tab1 FinishNav21
0:01:13 Tab1 StartNav22 #renderer-script #auto_subframe
0:01:13 Tab1 FinishNav22
0:01:13 Tab1 PageLoad
0:01:14 Tab1 StartNav23 #renderer-script #auto_subframe
0:01:14 Tab1 FinishNav23
0:01:14 Tab1 PageLoad
0:01:14 Tab1 StartNav24 #renderer-script #auto_subframe
0:01:14 Tab1 FinishNav24
0:01:14 Tab1 PageLoad
0:01:14 Tab1 PageLoad
0:01:21 Widget Closed: MenuHost
0:01:23 Widget Closed: MenuHost
0:01:46 Widget Closed: StatusBubble
0:03:52 Widget Closed: BubbleDialogDelegateView
0:03:56 Tab1 StartNav26 #renderer-user #link
0:03:56 Tab1 FinishNav26
0:03:56 Tab1 StartNav27 #renderer-user #link
0:03:56 Tab1 FinishNav27
0:04:03 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:00 Tab1 FinishNav2
0:00:02 Tab1 StartNav3 #renderer-script #auto_subframe
0:00:02 Tab1 FinishNav3
0:00:02 Tab1 PageLoad
0:00:02 Tab1 StartNav4 #renderer-script #auto_subframe
0:00:02 Tab1 FinishNav4
0:00:02 Tab1 PageLoad
0:00:03 Tab1 StartNav5 #renderer-script #auto_subframe
0:00:03 Tab1 FinishNav5
0:00:03 Tab1 PageLoad
0:00:04 Tab1 PageLoad
0:00:06 Tab1 StartNav6 #renderer-user #link
0:00:06 Tab1 FinishNav6
0:00:06 Tab1 StartNav7 #renderer-user #link
0:00:06 Tab1 FinishNav7
0:00:20 Widget Closed: StatusBubble
0:00:23 Tab1 StartNav8 #renderer-user #link
0:00:23 Tab1 FinishNav8
0:00:24 Tab1 StartNav9 #renderer-user #link
0:00:24 Tab1 FinishNav9
0:00:25 Tab1 StartNav10 #reload
0:00:25 Microsoft.PipelineStatus.ReportNonSuccess
0:00:25 Microsoft.PipelineStatus.ReportNonSuccess
0:00:25 Microsoft.PipelineStatus.ReportNonSuccess
0:00:25 Microsoft.PipelineStatus.ReportNonSuccess
0:00:25 Tab1 FinishNav10
0:00:27 Tab1 StartNav11 #renderer-script #auto_subframe
0:00:27 Tab1 FinishNav11
0:00:27 Tab1 PageLoad
0:00:27 Tab1 StartNav12 #renderer-script #auto_subframe
0:00:27 Tab1 FinishNav12
0:00:27 Tab1 PageLoad
0:00:27 Tab1 StartNav13 #renderer-script #auto_subframe
0:00:27 Tab1 FinishNav13
0:00:27 Tab1 PageLoad
0:00:27 Tab1 PageLoad
0:00:28 Tab1 StartNav14 #renderer-user #link
0:00:28 Tab1 FinishNav14
0:00:28 Tab1 StartNav15 #renderer-user #link
0:00:28 Tab1 FinishNav15
0:00:31 Tab1 StartNav16 #reload
0:00:31 Microsoft.PipelineStatus.ReportNonSuccess
0:00:31 Microsoft.PipelineStatus.ReportNonSuccess
0:00:31 Microsoft.PipelineStatus.ReportNonSuccess
0:00:31 Microsoft.PipelineStatus.ReportNonSuccess
0:00:31 Tab1 FinishNav16
0:00:32 Tab1 StartNav17 #renderer-script #auto_subframe
0:00:32 Tab1 FinishNav17
0:00:32 Tab1 PageLoad
0:00:33 Tab1 StartNav18 #renderer-script #auto_subframe
0:00:33 Tab1 FinishNav18
0:00:33 Tab1 PageLoad
0:00:33 Tab1 StartNav19 #renderer-script #auto_subframe
0:00:33 Tab1 FinishNav19
0:00:33 Tab1 PageLoad
0:00:33 Tab1 PageLoad
0:00:34 Tab1 StartNav20 #renderer-script #auto_subframe
0:00:34 Tab1 FinishNav20
0:00:34 Tab1 PageLoad
0:00:34 Tab1 StartNav21 #renderer-user #auto_subframe
0:00:34 Tab1 FinishNav21 ERR_ABORTED
0:00:34 Tab1 StartNav22 #renderer-script #auto_subframe
0:00:34 Tab1 FinishNav22
0:00:34 Tab1 PageLoad
0:00:34 Tab1 StartNav23 #renderer-user #auto_subframe
0:00:34 Tab1 FinishNav23
0:00:34 Tab1 PageLoad
0:00:37 Media.Hidden
