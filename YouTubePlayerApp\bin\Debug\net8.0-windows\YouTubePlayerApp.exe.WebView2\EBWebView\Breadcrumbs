0:00:04 Memory Pressure: Moderate
0:00:05 Widget Closed: TooltipAura
0:00:15 Memory Pressure: Moderate
0:00:18 Tab1 StartNav6 #renderer-user #link
0:00:18 Tab1 FinishNav6
0:00:18 Tab1 StartNav7 #renderer-user #link
0:00:18 Tab1 FinishNav7
0:00:25 Memory Pressure: Moderate
0:00:28 Widget Closed: StatusBubble
0:00:35 Memory Pressure: Moderate
0:00:45 Memory Pressure: Moderate
0:00:55 Memory Pressure: Moderate
0:01:05 Memory Pressure: Moderate
0:01:15 Memory Pressure: Moderate
0:01:20 Tab1 StartNav8 #reload
0:01:20 Microsoft.PipelineStatus.ReportNonSuccess
0:01:20 Microsoft.PipelineStatus.ReportNonSuccess
0:01:20 Microsoft.PipelineStatus.ReportNonSuccess
0:01:20 Microsoft.PipelineStatus.ReportNonSuccess
0:01:20 Tab1 FinishNav8
0:01:21 Tab1 StartNav9 #renderer-script #auto_subframe
0:01:21 Tab1 FinishNav9
0:01:21 Tab1 PageLoad
0:01:22 Tab1 StartNav10 #renderer-script #auto_subframe
0:01:22 Tab1 FinishNav10
0:01:22 Tab1 PageLoad
0:01:22 Tab1 StartNav11 #renderer-script #auto_subframe
0:01:22 Tab1 FinishNav11
0:01:22 Tab1 PageLoad
0:01:22 Tab1 PageLoad
0:01:23 Tab1 StartNav12 #renderer-script #auto_subframe
0:01:23 Tab1 FinishNav12
0:01:23 Tab1 PageLoad
0:01:23 Tab1 StartNav13 #renderer-user #auto_subframe
0:01:23 Tab1 FinishNav13 ERR_ABORTED
0:01:23 Tab1 StartNav14 #renderer-script #auto_subframe
0:01:23 Tab1 FinishNav14
0:01:23 Tab1 PageLoad
0:01:23 Tab1 StartNav15 #renderer-user #auto_subframe
0:01:23 Tab1 FinishNav15
0:01:23 Tab1 PageLoad
0:01:25 Memory Pressure: Moderate
0:01:35 Memory Pressure: Moderate
0:01:38 Tab1 StartNav16 #renderer-user #link
0:01:38 Tab1 FinishNav16
0:01:38 Tab1 StartNav17 #renderer-user #link
0:01:38 Tab1 FinishNav17
0:01:45 Memory Pressure: Moderate
0:01:53 Widget Closed: StatusBubble
0:01:55 Memory Pressure: Moderate
0:02:05 Memory Pressure: Moderate
0:02:15 Memory Pressure: Moderate
0:02:21 Tab1 StartNav18 #reload
0:02:21 Microsoft.PipelineStatus.ReportNonSuccess
0:02:21 Microsoft.PipelineStatus.ReportNonSuccess
0:02:21 Microsoft.PipelineStatus.ReportNonSuccess
0:02:21 Microsoft.PipelineStatus.ReportNonSuccess
0:02:21 Tab1 FinishNav18
0:02:23 Tab1 StartNav19 #renderer-script #auto_subframe
0:02:23 Tab1 FinishNav19
0:02:23 Tab1 PageLoad
0:02:23 Tab1 StartNav20 #renderer-script #auto_subframe
0:02:23 Tab1 FinishNav20
0:02:23 Tab1 PageLoad
0:02:23 Tab1 StartNav21 #renderer-script #auto_subframe
0:02:23 Tab1 FinishNav21
0:02:23 Tab1 PageLoad
0:02:23 Tab1 PageLoad
0:02:25 Memory Pressure: Moderate
0:02:25 Tab1 StartNav22 #renderer-script #auto_subframe
0:02:25 Tab1 FinishNav22
0:02:25 Tab1 PageLoad
0:02:25 Tab1 StartNav23 #renderer-user #auto_subframe
0:02:25 Tab1 FinishNav23 ERR_ABORTED
0:02:25 Tab1 StartNav24 #renderer-script #auto_subframe
0:02:25 Tab1 FinishNav24
0:02:25 Tab1 PageLoad
0:02:25 Tab1 StartNav25 #renderer-user #auto_subframe
0:02:25 Tab1 FinishNav25
0:02:25 Tab1 PageLoad
0:02:30 Widget Closed: StatusBubble
0:03:05 Memory Pressure: Moderate
0:03:17 Tab1 StartNav26 #renderer-user #link
0:03:17 Tab1 FinishNav26
0:03:17 Tab1 StartNav27 #renderer-user #link
0:03:17 Tab1 FinishNav27
0:03:35 Memory Pressure: Moderate
0:03:45 Memory Pressure: Moderate
0:04:00 Widget Closed: StatusBubble
0:04:00 Memory Pressure: Moderate
