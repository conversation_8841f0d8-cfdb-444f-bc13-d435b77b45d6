0:01:09 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:01:09 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:09 Microsoft.Shutdown.TabStripModel.CloseAllTabs
0:01:09 Microsoft.Shutdown.TabStripModel.CloseTabs
0:01:09 Microsoft.Shutdown.TabStripModel.CloseTabs_EdgeBeforeClosingAllTabs
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_TabClosable
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosingAll
0:01:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses
0:01:09 Microsoft.Shutdown.TabStripModel.CloseWebContentses_FastShutdown
0:01:09 Microsoft.Shutdown.TabStripModel.ShouldRunUnloadListenerBeforeClosing_false
0:01:09 Microsoft.Shutdown.TabStripModel.RunUnloadListenerBeforeClosing_false
0:01:09 Microsoft.Shutdown.TabStripModel.InternalCloseTabsImpl_ClosedAll
0:01:09 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:01:09 Microsoft.UnloadController.ClearUnloadState
0:01:09 Browser2 Close Tab2 at 0
0:01:09 Widget Closed: StatusBubble
0:01:09 Tab2 WebContentsDestroyed
0:01:09 Microsoft.UnloadController.TabStripEmpty
0:01:09 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:09 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:09 Microsoft.Shutdown.OnWindowClosing
0:01:09 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:09 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:09 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:09 Widget Closed: BrowserFrame
0:01:09 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:09 Microsoft.Shutdown.OnWindowClosing
0:01:09 Microsoft.Shutdown.OnWindowClosing_DeleteScheduled
0:01:09 Microsoft.Browser_Removed
0:01:12 Tab1 StartNav21 #reload
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Microsoft.PipelineStatus.ReportNonSuccess
0:01:12 Tab1 FinishNav21
0:01:13 Tab1 StartNav22 #renderer-script #auto_subframe
0:01:13 Tab1 FinishNav22
0:01:13 Tab1 PageLoad
0:01:14 Tab1 StartNav23 #renderer-script #auto_subframe
0:01:14 Tab1 FinishNav23
0:01:14 Tab1 PageLoad
0:01:14 Tab1 StartNav24 #renderer-script #auto_subframe
0:01:14 Tab1 FinishNav24
0:01:14 Tab1 PageLoad
0:01:14 Tab1 PageLoad
0:01:21 Widget Closed: MenuHost
0:01:23 Widget Closed: MenuHost
0:01:46 Widget Closed: StatusBubble
0:03:52 Widget Closed: BubbleDialogDelegateView
0:03:56 Tab1 StartNav26 #renderer-user #link
0:03:56 Tab1 FinishNav26
0:03:56 Tab1 StartNav27 #renderer-user #link
0:03:56 Tab1 FinishNav27
0:04:03 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
