0:00:34 Tab1 PageLoad
0:00:37 Media.Hidden
0:00:00 Startup
0:00:00 Microsoft.DeleteProfileHelper.CleanUpEphemeralProfiles
0:00:00 Microsoft.DeleteProfileHelper.CleanUpDeletedProfiles
0:00:00 Microsoft.NewBrowser_Popup
0:00:00 Microsoft.BrowserList.AddBrowser
0:00:00 Browser1 Insert active Tab1 at 0
0:00:00 Tab1 StartNav1 #auto_toplevel
0:00:00 Tab1 StartNav2 #typed
0:00:00 Tab1 FinishNav1
0:00:00 Tab1 PageLoad
0:00:01 Tab1 FinishNav2
0:00:01 Tab1 StartNav3 #renderer-script #auto_subframe
0:00:02 Tab1 FinishNav3
0:00:02 Tab1 PageLoad
0:00:02 Tab1 StartNav4 #renderer-script #auto_subframe
0:00:02 Tab1 FinishNav4
0:00:02 Tab1 PageLoad
0:00:02 Tab1 StartNav5 #renderer-script #auto_subframe
0:00:02 Tab1 FinishNav5
0:00:02 Tab1 PageLoad
0:00:02 Tab1 StartNav6 #renderer-script #auto_subframe
0:00:02 Tab1 FinishNav6
0:00:02 Tab1 PageLoad
0:00:02 Tab1 PageLoad
0:00:06 Tab1 StartNav7 #renderer-user #link
0:00:06 Tab1 FinishNav7
0:00:06 Tab1 StartNav8 #renderer-user #link
0:00:06 Tab1 FinishNav8
0:00:18 Widget Closed: StatusBubble
0:00:35 Tab1 StartNav9 #renderer-user #link
0:00:35 Tab1 FinishNav9
0:00:36 Tab1 StartNav10 #renderer-user #link
0:00:36 Tab1 FinishNav10
0:00:36 Widget Closed: TooltipAura
0:00:38 Tab1 StartNav11 #renderer-user #link
0:00:38 Tab1 FinishNav11
0:00:48 Tab1 StartNav12 #reload
0:00:48 Microsoft.PipelineStatus.ReportNonSuccess
0:00:48 Microsoft.PipelineStatus.ReportNonSuccess
0:00:48 Microsoft.PipelineStatus.ReportNonSuccess
0:00:48 Microsoft.PipelineStatus.ReportNonSuccess
0:00:48 Tab1 FinishNav12
0:00:49 Tab1 StartNav13 #renderer-script #auto_subframe
0:00:49 Tab1 FinishNav13
0:00:49 Tab1 PageLoad
0:00:49 Tab1 StartNav14 #renderer-script #auto_subframe
0:00:49 Tab1 FinishNav14
0:00:49 Tab1 PageLoad
0:00:49 Tab1 StartNav15 #renderer-script #auto_subframe
0:00:49 Tab1 FinishNav15
0:00:49 Tab1 PageLoad
0:00:49 Tab1 PageLoad
0:00:57 Tab1 StartNav16 #renderer-user #link
0:00:57 Tab1 FinishNav16
0:00:57 Tab1 StartNav17 #renderer-user #link
0:00:57 Tab1 FinishNav17
0:01:01 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:01 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:01 Microsoft.UnloadController.HasCompletedUnloadProcessing_False
0:01:01 Microsoft.Shutdown.TabStripModel.SendDetachWebContentsNotifications
0:01:01 Microsoft.UnloadController.ClearUnloadState
0:01:01 Browser1 Close Tab1 at 0
0:01:01 Widget Closed: StatusBubble
0:01:01 Tab1 WebContentsDestroyed
0:01:01 Microsoft.UnloadController.TabStripEmpty
0:01:01 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:01 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:01 Microsoft.Shutdown.OnWindowClosing
0:01:01 Microsoft.UnloadController.IsUnclosableApp_IsNotWebApp
0:01:01 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:01 Microsoft.Shutdown.OnWindowClosing_MaybeClearBrowsingDataOnExit
0:01:01 Microsoft.Shutdown.OnWindowClosing_ClearBrowsingDataOnExitNotInProgress
0:01:01 Microsoft.Shutdown.OnWindowClosingPostClearBrowsingData_NoBrowsingDataCleared
0:01:01 Widget Closed: BrowserFrame
0:01:01 Microsoft.UnloadController.HasCompletedUnloadProcessing_True
0:01:01 Microsoft.Shutdown.OnWindowClosing
0:01:01 Microsoft.Shutdown.OnWindowClosing_DeleteScheduled
0:01:01 Microsoft.Last_Browser_Removed
0:01:01 Microsoft.Shutdown.ShutdownIfNoBrowsers
0:01:01 Microsoft.Shutdown.SetTryingToQuit_Quitting
0:01:01 Microsoft.Shutdown.NotifyAppTerminating
0:01:01 Microsoft.Shutdown.OnAppExiting
0:01:01 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura
0:01:01 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.NotificationUIManager_StartShutdown
0:01:01 Microsoft.Shutdown.HandleAppExitingForPlatform_Aura.CloseAllSecondaryWidgets
0:01:02 Shutdown
