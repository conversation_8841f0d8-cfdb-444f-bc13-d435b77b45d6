using Microsoft.Web.WebView2.Core;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp;

/// <summary>
/// Video-playback-safe ad blocker that ensures flawless YouTube functionality
/// while effectively blocking all ads
/// </summary>
public sealed class VideoSafeAdBlocker : IDisposable
{
    private readonly HashSet<string> _blockedDomains;
    private readonly CompiledRegex[] _urlPatterns;
    private readonly ConcurrentDictionary<string, int> _blockedStats;
    private readonly SettingsCache _settingsCache;
    private CoreWebView2? _webView;
    private bool _disposed;
    private readonly Timer _videoProtectionTimer;

    // Compiled regex patterns for better performance
    private readonly struct CompiledRegex
    {
        public readonly Regex Pattern;
        public readonly string Description;

        public CompiledRegex(string pattern, string description)
        {
            Pattern = new Regex(pattern, RegexOptions.Compiled | RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
            Description = description;
        }
    }

    public VideoSafeAdBlocker()
    {
        _settingsCache = SettingsCache.Instance;
        _blockedDomains = LoadVideoSafeBlockedDomains();
        _urlPatterns = LoadVideoSafeUrlPatterns();
        _blockedStats = new ConcurrentDictionary<string, int>();
        
        // Timer to continuously protect video functionality
        _videoProtectionTimer = new Timer(ProtectVideoPlayback, null, TimeSpan.FromSeconds(2), TimeSpan.FromSeconds(5));
    }

    public void Initialize(CoreWebView2 webView)
    {
        _webView = webView;
        
        if (AppSettings.BlockAds)
        {
            EnableAdBlocking();
        }
    }

    public void EnableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("VideoSafeAdBlocker.EnableAdBlocking", () =>
        {
            // 1. Request filtering - block only confirmed ad domains
            _webView.WebResourceRequested += OnWebResourceRequested;
            _webView.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

            // 2. DOM content loaded - inject video-safe scripts
            _webView.DOMContentLoaded += OnDOMContentLoaded;

            // 3. Navigation completed - apply safe blocking
            _webView.NavigationCompleted += OnNavigationCompleted;
        });
    }

    public void DisableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("VideoSafeAdBlocker.DisableAdBlocking", () =>
        {
            _webView.WebResourceRequested -= OnWebResourceRequested;
            _webView.DOMContentLoaded -= OnDOMContentLoaded;
            _webView.NavigationCompleted -= OnNavigationCompleted;
            _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
        });
    }

    private void OnWebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        if (_disposed) return;
        
        var uri = e.Request.Uri;
        
        // Only block confirmed ad requests - never block video content
        if (ShouldBlockRequest(uri) && !IsVideoContent(uri))
        {
            e.Response = _webView?.Environment.CreateWebResourceResponse(
                null, 200, "OK", "");
            
            IncrementBlockedStat("Requests");
            
            // Log blocked request
            AppLogger.Instance.LogNetworkRequest(uri, true, "Ad domain/pattern blocked (video-safe)");
            
            if (_settingsCache.ShowAdBlockStats)
            {
                AppLogger.Instance.LogAdBlockingActivity("Request blocked (video-safe)", 1, uri);
            }
        }
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("VideoSafeAdBlocker.OnDOMContentLoaded");
            try
            {
                // Inject video-safe CSS first
                await InjectVideoSafeCSS();
                
                // Small delay to let CSS take effect
                await Task.Delay(100);
                
                // Inject video-safe JavaScript
                await InjectVideoSafeJavaScript();
                
                // Ensure video functionality is protected
                await ProtectVideoFunctionality();
                
                AppLogger.Instance.LogInfo("VideoSafeAdBlocker", "DOM Content Loaded - Video-safe ad blocking active");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("VideoSafeAdBlocker.OnDOMContentLoaded");
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("VideoSafeAdBlocker", ex, "Error injecting video-safe ad blocking code");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("VideoSafeAdBlocker.OnNavigationCompleted");
            try
            {
                AppLogger.Instance.LogInfo("VideoSafeAdBlocker", $"Navigation completed to: {_webView.Source}");
                
                // Wait for page to stabilize
                await Task.Delay(1500);
                
                // Apply video-safe ad removal
                await RemoveAdsVideoSafe();
                
                // Ensure video functionality is protected
                await ProtectVideoFunctionality();
                
                System.Diagnostics.Debug.WriteLine("[VideoSafeAdBlocker] Post-navigation video-safe cleanup completed");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("VideoSafeAdBlocker.OnNavigationCompleted");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[VideoSafeAdBlocker] Error in post-load processing: {ex.Message}");
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private bool ShouldBlockRequest(string uri)
    {
        try
        {
            // Never block video content URLs
            if (IsVideoContent(uri))
                return false;

            // Fast string-based checks for confirmed ad patterns only
            if (IsConfirmedAdRequest(uri))
                return true;

            // Check against safe URL patterns
            foreach (var pattern in _urlPatterns)
            {
                if (pattern.Pattern.IsMatch(uri))
                    return true;
            }

            // Domain check for confirmed ad domains only
            var uriObj = new Uri(uri);
            var host = uriObj.Host;

            return _blockedDomains.Contains(host) || 
                   _blockedDomains.Any(domain => host.EndsWith(domain, StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    private bool IsVideoContent(string uri)
    {
        // Protect all video-related URLs
        var videoPatterns = new[]
        {
            "/videoplayback",
            "/api/timedtext",
            "/get_video_info",
            "/player_ias",
            "/youtubei/v1/player",
            "/youtubei/v1/next",
            "/youtubei/v1/browse",
            "/watch?v=",
            "/embed/",
            "/v/",
            "/shorts/",
            "/live/",
            "/stream",
            ".mp4",
            ".webm",
            ".m4s",
            ".ts",
            "/manifest",
            "/playlist",
            "/chunk",
            "/segment",
            "/init.mp4",
            "/range/",
            "/sqp/",
            "/hqdefault",
            "/maxresdefault",
            "/sddefault",
            "/mqdefault",
            "/default.jpg",
            "/vi/",
            "/vi_webp/",
            "/sb/",
            "/storyboard",
            "/generate_204", // YouTube analytics - needed for video functionality
            "/api/stats/watchtime", // Watch time tracking - needed for video progress
            "/api/stats/playback", // Playback stats - needed for video functionality
            "/ptracking", // Only block if it's clearly ad-related
            "/log_event" // Only block if it's clearly ad-related
        };

        return videoPatterns.Any(pattern => uri.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private bool IsConfirmedAdRequest(string uri)
    {
        // Only block URLs that are definitely ads, never video content
        var confirmedAdPatterns = new[]
        {
            // Confirmed ad serving domains
            "googleads.g.doubleclick.net",
            "googlesyndication.com",
            "googletagservices.com",
            "doubleclick.net/instream/ad_status",
            "doubleclick.net/gampad/ads",
            
            // Confirmed ad endpoints
            "/pagead/ads",
            "/pagead/interaction",
            "/pagead/conversion",
            "/ads/measurement",
            "/video_ads_serving",
            "/companion_ad",
            "/overlay_ad",
            "/bumper_ad",
            "/masthead_ad",
            "/promoted_content",
            "/sponsored_content",
            "/trueview",
            
            // Third-party ad networks
            "/adnxs.com",
            "/adsystem.amazon.com",
            "/facebook.com/tr",
            "/outbrain.com",
            "/taboola.com",
            "/criteo.com",
            "/adsafeprotected.com",
            "/moatads.com",
            
            // Ad-specific parameters (but not general tracking)
            "ad_type=",
            "ad_video_id=",
            "ad_cpn=",
            "ad_mt=",
            "gir=yes", // Google ad impression reporting
            
            // Confirmed ad scripts
            "/adsbygoogle.js",
            "/show_ads.js",
            "/ads.js"
        };

        return confirmedAdPatterns.Any(pattern => 
            uri.Contains(pattern, StringComparison.OrdinalIgnoreCase));
    }

    private async Task InjectVideoSafeCSS()
    {
        if (_webView == null) return;

        var css = GetVideoSafeCSS();
        var script = $@"
            (function() {{
                console.log('[VideoSafeAdBlocker] Injecting video-safe CSS...');
                var style = document.createElement('style');
                style.id = 'video-safe-adblock-css';
                style.textContent = `{css}`;
                document.head.appendChild(style);
                console.log('[VideoSafeAdBlocker] Video-safe CSS injected');
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task InjectVideoSafeJavaScript()
    {
        if (_webView == null) return;

        var script = GetVideoSafeJavaScript();
        await _webView.ExecuteScriptAsync(script);
    }

    private async Task RemoveAdsVideoSafe()
    {
        if (_webView == null) return;

        var script = @"
            (function() {
                console.log('[VideoSafeAdBlocker] Starting video-safe ad removal...');
                let removedCount = 0;
                
                // CRITICAL: Protect video functionality first
                function protectVideoElements() {
                    // Protect main video element
                    const video = document.querySelector('video.html5-main-video');
                    if (video) {
                        video.style.pointerEvents = 'auto';
                        video.style.display = 'block';
                        video.style.visibility = 'visible';
                        video.style.opacity = '1';
                    }
                    
                    // Protect video player container
                    const player = document.querySelector('.html5-video-player');
                    if (player) {
                        player.style.pointerEvents = 'auto';
                        player.style.display = 'block';
                        player.style.visibility = 'visible';
                    }
                    
                    // Protect player controls
                    const controls = document.querySelector('.ytp-chrome-bottom');
                    if (controls) {
                        controls.style.pointerEvents = 'auto';
                        controls.style.display = 'flex';
                        controls.style.visibility = 'visible';
                        controls.style.zIndex = '9999';
                    }
                    
                    // Protect play buttons
                    const playButtons = document.querySelectorAll('.ytp-play-button, .ytp-large-play-button');
                    playButtons.forEach(btn => {
                        btn.style.pointerEvents = 'auto';
                        btn.style.display = 'block';
                        btn.style.visibility = 'visible';
                        btn.style.cursor = 'pointer';
                    });
                    
                    // Protect progress bar
                    const progressBar = document.querySelector('.ytp-progress-bar-container');
                    if (progressBar) {
                        progressBar.style.pointerEvents = 'auto';
                        progressBar.style.display = 'block';
                        progressBar.style.visibility = 'visible';
                    }
                    
                    // Protect volume controls
                    const volumeControls = document.querySelector('.ytp-volume-panel');
                    if (volumeControls) {
                        volumeControls.style.pointerEvents = 'auto';
                        volumeControls.style.display = 'block';
                        volumeControls.style.visibility = 'visible';
                    }
                    
                    // Protect settings menu
                    const settingsButton = document.querySelector('.ytp-settings-button');
                    if (settingsButton) {
                        settingsButton.style.pointerEvents = 'auto';
                        settingsButton.style.display = 'block';
                        settingsButton.style.visibility = 'visible';
                    }
                    
                    // Protect fullscreen button
                    const fullscreenButton = document.querySelector('.ytp-fullscreen-button');
                    if (fullscreenButton) {
                        fullscreenButton.style.pointerEvents = 'auto';
                        fullscreenButton.style.display = 'block';
                        fullscreenButton.style.visibility = 'visible';
                    }
                }
                
                // Always protect video elements first
                protectVideoElements();
                
                // Video-safe ad selectors - very specific to avoid affecting video playback
                const videoSafeAdSelectors = [
                    // Only target obvious ad containers that won't affect video
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-promoted-video-renderer:not(.ytd-video-renderer)', 
                    '.ytd-banner-promo-renderer',
                    '.ytd-in-feed-ad-layout-renderer',
                    '.ytd-ad-slot-renderer',
                    '.ytd-display-ad-renderer',
                    
                    // Sidebar ads only (never touch player area)
                    '#masthead-ad', 
                    '#watch-sidebar-ads',
                    '.ytd-companion-slot-renderer',
                    
                    // Premium upsells (but not player overlays)
                    '.ytd-mealbar-promo-renderer',
                    '.ytd-premium-upsell-renderer',
                    '.ytd-background-promo-renderer',
                    
                    // Shopping ads (but not video-related)
                    '.ytd-merch-shelf-renderer',
                    '.ytd-product-shelf-renderer',
                    
                    // Very specific ad attributes (with video exclusions)
                    '[data-is-ad=""true""]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""]):not([class*=""video""]):not([id*=""video""])',
                    '[data-promoted=""true""]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""]):not([class*=""video""]):not([id*=""video""])'
                ];
                
                // Remove ads with extreme caution
                videoSafeAdSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            // Multiple safety checks to never remove video elements
                            if (!el.closest('.html5-video-player') && 
                                !el.closest('.ytp-chrome-bottom') &&
                                !el.closest('.ytp-chrome-controls') &&
                                !el.closest('.ytp-chrome-top') &&
                                !el.closest('#player') &&
                                !el.closest('#movie_player') &&
                                !el.classList.contains('ytp-play-button') &&
                                !el.classList.contains('ytp-large-play-button') &&
                                !el.classList.contains('html5-main-video') &&
                                !el.classList.contains('html5-video-player') &&
                                !el.hasAttribute('data-video-id') &&
                                el.tagName !== 'VIDEO' &&
                                !el.id.includes('player') &&
                                !el.id.includes('video') &&
                                !el.className.includes('video') &&
                                !el.className.includes('player')) {
                                
                                el.style.display = 'none';
                                el.style.visibility = 'hidden';
                                el.style.opacity = '0';
                                el.style.height = '0';
                                el.style.width = '0';
                                el.style.overflow = 'hidden';
                                removedCount++;
                            }
                        });
                    } catch (e) {
                        console.warn('[VideoSafeAdBlocker] Safe removal error:', e);
                    }
                });
                
                // Auto-skip ads safely (only if skip button exists)
                setTimeout(() => {
                    const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                    if (skipButton && skipButton.offsetParent !== null && skipButton.style.display !== 'none') {
                        console.log('[VideoSafeAdBlocker] Auto-clicking skip button');
                        skipButton.click();
                    }
                }, 1000);
                
                // Re-protect video elements after ad removal
                protectVideoElements();
                
                // Ensure video can play
                setTimeout(() => {
                    const video = document.querySelector('video.html5-main-video');
                    if (video && video.paused && video.readyState >= 2) {
                        // Don't auto-play, just ensure it's ready
                        console.log('[VideoSafeAdBlocker] Video ready for playback');
                    }
                }, 2000);
                
                console.log('[VideoSafeAdBlocker] Video-safe ad removal completed. Removed:', removedCount, 'Video functionality preserved.');
                return removedCount;
            })();
        ";

        try
        {
            var result = await _webView.ExecuteScriptAsync(script);
            if (int.TryParse(result, out int blockedCount) && blockedCount > 0)
            {
                IncrementBlockedStat("Elements", blockedCount);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error executing video-safe ad removal script: {ex.Message}");
        }
    }

    private async Task ProtectVideoFunctionality()
    {
        if (_webView == null) return;

        var script = @"
            (function() {
                console.log('[VideoSafeAdBlocker] Protecting video functionality...');
                
                // Ensure video element is always functional
                const video = document.querySelector('video.html5-main-video');
                if (video) {
                    // Remove any interference with video events
                    video.style.pointerEvents = 'auto';
                    video.style.cursor = 'pointer';
                    
                    // Ensure video can receive clicks
                    if (!video.hasAttribute('data-video-protected')) {
                        video.setAttribute('data-video-protected', 'true');
                        
                        // Preserve click functionality
                        video.addEventListener('click', function(e) {
                            // Allow normal video behavior
                            console.log('[VideoSafeAdBlocker] Video click preserved');
                        }, true);
                        
                        // Preserve keyboard controls
                        video.addEventListener('keydown', function(e) {
                            // Allow normal keyboard controls (space, arrows, etc.)
                            console.log('[VideoSafeAdBlocker] Video keyboard controls preserved');
                        }, true);
                    }
                }
                
                // Ensure player controls are always functional
                const playerControls = document.querySelectorAll('.ytp-chrome-bottom *');
                playerControls.forEach(control => {
                    control.style.pointerEvents = 'auto';
                });
                
                // Ensure play button works
                const playButton = document.querySelector('.ytp-play-button');
                if (playButton) {
                    playButton.style.pointerEvents = 'auto';
                    playButton.style.cursor = 'pointer';
                }
                
                // Ensure large play button works
                const largePlayButton = document.querySelector('.ytp-large-play-button');
                if (largePlayButton) {
                    largePlayButton.style.pointerEvents = 'auto';
                    largePlayButton.style.cursor = 'pointer';
                    largePlayButton.style.display = 'block';
                    largePlayButton.style.visibility = 'visible';
                }
                
                console.log('[VideoSafeAdBlocker] Video functionality protection complete');
            })();
        ";

        try
        {
            await _webView.ExecuteScriptAsync(script);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error protecting video functionality: {ex.Message}");
        }
    }

    private void ProtectVideoPlayback(object? state)
    {
        if (_webView == null || _disposed) return;

        try
        {
            // Continuously ensure video functionality is protected
            _ = Task.Run(async () =>
            {
                try
                {
                    await ProtectVideoFunctionality();
                }
                catch
                {
                    // Ignore errors in background protection
                }
            });
        }
        catch
        {
            // Ignore timer errors
        }
    }

    private string GetVideoSafeCSS()
    {
        return @"
            /* Video-Safe Ad Blocking CSS - Only targets confirmed ads, never video elements */
            
            /* Promoted content (safe selectors) */
            .ytd-promoted-sparkles-web-renderer,
            .ytd-promoted-video-renderer:not(.ytd-video-renderer),
            .ytd-banner-promo-renderer,
            .ytd-in-feed-ad-layout-renderer,
            .ytd-ad-slot-renderer,
            .ytd-display-ad-renderer,
            
            /* Sidebar ads (never touch player area) */
            #masthead-ad,
            #watch-sidebar-ads,
            .ytd-companion-slot-renderer,
            
            /* Premium upsells (but not player overlays) */
            .ytd-mealbar-promo-renderer,
            .ytd-premium-upsell-renderer,
            .ytd-background-promo-renderer,
            
            /* Shopping ads (but not video-related) */
            .ytd-merch-shelf-renderer,
            .ytd-product-shelf-renderer,
            
            /* Very specific ad attributes with video exclusions */
            [data-is-ad='true']:not(video):not(.html5-video-player):not([class*='ytp-']):not([id*='ytp-']):not([class*='video']):not([id*='video']),
            [data-promoted='true']:not(video):not(.html5-video-player):not([class*='ytp-']):not([id*='ytp-']):not([class*='video']):not([id*='video'])
            {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
                overflow: hidden !important;
            }
            
            /* CRITICAL: Ensure video elements are always visible and functional */
            video.html5-main-video,
            .html5-video-player,
            .ytp-chrome-bottom,
            .ytp-chrome-controls,
            .ytp-play-button,
            .ytp-large-play-button,
            .ytp-progress-bar-container,
            .ytp-volume-panel,
            .ytp-settings-button,
            .ytp-fullscreen-button,
            #player,
            #movie_player
            {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
            }
            
            /* Ensure video controls are clickable */
            .ytp-chrome-bottom *,
            .ytp-chrome-controls *
            {
                pointer-events: auto !important;
            }
        ";
    }

    private string GetVideoSafeJavaScript()
    {
        return @"
            (function() {
                'use strict';
                console.log('[VideoSafeAdBlocker] Initializing video-safe ad blocking...');
                
                // Override YouTube ad configuration safely
                if (window.yt && window.yt.config_) {
                    const config = window.yt.config_;
                    
                    // Set experiment flags to disable ads (but preserve video functionality)
                    if (!config.EXPERIMENT_FLAGS) {
                        config.EXPERIMENT_FLAGS = {};
                    }
                    
                    // Safe ad blocking flags that don't interfere with video
                    const safeAdBlockFlags = {
                        'html5_disable_preroll_ads': true,
                        'html5_disable_midroll_ads': true,
                        'html5_disable_postroll_ads': true,
                        'html5_disable_overlay_ads': true,
                        'html5_disable_companion_ads': true,
                        'disable_promoted_content': true,
                        'disable_banner_ads': true,
                        'disable_sidebar_ads': true
                    };
                    
                    Object.assign(config.EXPERIMENT_FLAGS, safeAdBlockFlags);
                    console.log('[VideoSafeAdBlocker] Safe ad blocking flags configured');
                }
                
                // Monitor for video elements and protect them
                const videoProtectionObserver = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.addedNodes.length > 0) {
                            // Protect any new video elements
                            const videos = document.querySelectorAll('video.html5-main-video');
                            videos.forEach(video => {
                                video.style.pointerEvents = 'auto';
                                video.style.display = 'block';
                                video.style.visibility = 'visible';
                            });
                            
                            // Protect player controls
                            const controls = document.querySelectorAll('.ytp-chrome-bottom, .ytp-chrome-controls');
                            controls.forEach(control => {
                                control.style.pointerEvents = 'auto';
                                control.style.display = 'flex';
                                control.style.visibility = 'visible';
                            });
                        }
                    });
                });
                
                // Start monitoring
                videoProtectionObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                console.log('[VideoSafeAdBlocker] Video-safe ad blocking initialized successfully');
            })();
        ";
    }

    private HashSet<string> LoadVideoSafeBlockedDomains()
    {
        // Only confirmed ad domains that won't affect video content
        var domains = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            // Confirmed ad serving domains
            "googleads.g.doubleclick.net",
            "googlesyndication.com",
            "googletagservices.com",
            "doubleclick.net",
            "googleadservices.com",
            
            // Third-party ad networks
            "amazon-adsystem.com",
            "outbrain.com",
            "taboola.com",
            "criteo.com",
            "adsafeprotected.com",
            "moatads.com",
            "scorecardresearch.com",
            "quantserve.com",
            
            // Social media tracking
            "facebook.com",
            "analytics.tiktok.com",
            "bat.bing.com",
            
            // Other ad networks
            "adnxs.com",
            "adsystem.amazon.com"
        };

        return domains;
    }

    private CompiledRegex[] LoadVideoSafeUrlPatterns()
    {
        // Only patterns that are definitely ads and won't affect video
        var patterns = new List<CompiledRegex>
        {
            new CompiledRegex(@"/pagead/ads", "Google Ads"),
            new CompiledRegex(@"/pagead/interaction", "Ad Interaction"),
            new CompiledRegex(@"/pagead/conversion", "Ad Conversion"),
            new CompiledRegex(@"/ads/measurement", "Ad Measurement"),
            new CompiledRegex(@"/video_ads_serving", "Video Ad Serving"),
            new CompiledRegex(@"/companion_ad", "Companion Ads"),
            new CompiledRegex(@"/overlay_ad", "Overlay Ads"),
            new CompiledRegex(@"/bumper_ad", "Bumper Ads"),
            new CompiledRegex(@"/masthead_ad", "Masthead Ads"),
            new CompiledRegex(@"/promoted_content", "Promoted Content"),
            new CompiledRegex(@"/sponsored_content", "Sponsored Content"),
            new CompiledRegex(@"/trueview", "TrueView Ads"),
            new CompiledRegex(@"/adsbygoogle\.js", "AdSense Script"),
            new CompiledRegex(@"/show_ads\.js", "Show Ads Script"),
            new CompiledRegex(@"gir=yes", "Google Impression Reporting")
        };

        return patterns.ToArray();
    }

    // Statistics and utility methods
    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private void IncrementBlockedStat(string category, int count = 1)
    {
        _blockedStats.AddOrUpdate(category, count, (key, oldValue) => oldValue + count);
    }

    public int GetTotalBlocked()
    {
        return _blockedStats.Values.Sum();
    }

    public Dictionary<string, int> GetBlockedStats()
    {
        return new Dictionary<string, int>(_blockedStats);
    }

    public void ClearStats()
    {
        _blockedStats.Clear();
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        _videoProtectionTimer?.Dispose();
        
        if (_webView != null)
        {
            DisableAdBlocking();
        }
        
        _blockedStats.Clear();
    }
}