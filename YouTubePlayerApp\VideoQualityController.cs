using Microsoft.Web.WebView2.Core;
using System.Text.Json;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp;

/// <summary>
/// Optimized controls for video quality settings and enforces quality restrictions on YouTube
/// </summary>
public sealed class VideoQualityController : IDisposable
{
    private CoreWebView2? _webView;
    private readonly Dictionary<string, string> _qualityMappings;
    private readonly SettingsCache _settingsCache;
    private System.Timers.Timer? _qualityMonitorTimer;
    private bool _disposed;
    private DateTime _lastQualityCheck = DateTime.MinValue;
    private readonly TimeSpan _minCheckInterval = TimeSpan.FromSeconds(5); // Reduced frequency

    public VideoQualityController()
    {
        _settingsCache = SettingsCache.Instance;
        _qualityMappings = InitializeQualityMappings();
    }

    public void Initialize(CoreWebView2 webView)
    {
        if (_disposed) return;
        
        _webView = webView;
        
        PerformanceMonitor.Instance.MeasureOperation("VideoQualityController.Initialize", () =>
        {
            if (_settingsCache.ForceVideoQuality)
            {
                EnableQualityControl();
            }
        });
    }

    public void EnableQualityControl()
    {
        if (_webView == null) return;

        // Set up event handlers
        _webView.DOMContentLoaded += OnDOMContentLoaded;
        _webView.NavigationCompleted += OnNavigationCompleted;

        // Start continuous monitoring if override is enabled
        if (AppSettings.OverrideUserQualityChanges)
        {
            StartQualityMonitoring();
        }
    }

    public void DisableQualityControl()
    {
        if (_webView == null) return;

        _webView.DOMContentLoaded -= OnDOMContentLoaded;
        _webView.NavigationCompleted -= OnNavigationCompleted;
        
        StopQualityMonitoring();
    }

    private Dictionary<string, string> InitializeQualityMappings()
    {
        return new Dictionary<string, string>
        {
            ["auto"] = "auto",
            ["144p"] = "tiny",
            ["240p"] = "small", 
            ["360p"] = "medium",
            ["480p"] = "large",
            ["720p"] = "hd720",
            ["1080p"] = "hd1080",
            ["1440p"] = "hd1440",
            ["2160p"] = "hd2160"
        };
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            // Inject quality control initialization script
            await InjectQualityControlScript();
            
            // Hide quality controls if requested
            if (AppSettings.HideQualityControls)
            {
                await HideQualityControls();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in OnDOMContentLoaded: {ex.Message}");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            // Apply quality settings after navigation
            await ApplyQualitySettings();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in OnNavigationCompleted: {ex.Message}");
        }
    }

    private async Task InjectQualityControlScript()
    {
        if (_webView == null) return;

        var forcedQuality = _qualityMappings.GetValueOrDefault(AppSettings.ForcedVideoQuality, "auto");
        var lockControls = AppSettings.LockQualityControls.ToString().ToLower();
        var hideControls = AppSettings.HideQualityControls.ToString().ToLower();
        var overrideChanges = AppSettings.OverrideUserQualityChanges.ToString().ToLower();

        var script = $@"
            (function() {{
                'use strict';
                
                // Configuration
                const FORCED_QUALITY = '{forcedQuality}';
                const LOCK_CONTROLS = {lockControls};
                const HIDE_CONTROLS = {hideControls};
                const OVERRIDE_CHANGES = {overrideChanges};
                
                let currentPlayer = null;
                let qualitySetAttempts = 0;
                const MAX_ATTEMPTS = 50;
                
                console.log('YouTube Quality Controller activated');
                console.log('Forced Quality:', FORCED_QUALITY);
                console.log('Lock Controls:', LOCK_CONTROLS);
                console.log('Hide Controls:', HIDE_CONTROLS);
                
                // Wait for YouTube player to be ready
                function waitForPlayer() {{
                    const checkPlayer = () => {{
                        if (window.ytplayer && window.ytplayer.config && window.ytplayer.config.loaded) {{
                            initializeQualityControl();
                        }} else if (qualitySetAttempts < MAX_ATTEMPTS) {{
                            qualitySetAttempts++;
                            setTimeout(checkPlayer, 200);
                        }}
                    }};
                    checkPlayer();
                }}
                
                // Initialize quality control
                function initializeQualityControl() {{
                    try {{
                        // Get player instance
                        currentPlayer = document.getElementById('movie_player') || document.querySelector('.html5-video-player');
                        
                        if (currentPlayer && currentPlayer.getAvailableQualityLevels) {{
                            applyQualitySettings();
                            
                            if (LOCK_CONTROLS || HIDE_CONTROLS) {{
                                modifyQualityControls();
                            }}
                            
                            if (OVERRIDE_CHANGES) {{
                                setupQualityOverride();
                            }}
                        }} else {{
                            setTimeout(initializeQualityControl, 500);
                        }}
                    }} catch (error) {{
                        console.error('Error initializing quality control:', error);
                    }}
                }}
                
                // Apply quality settings to video
                function applyQualitySettings() {{
                    if (!currentPlayer) return;
                    
                    try {{
                        const availableQualities = currentPlayer.getAvailableQualityLevels();
                        console.log('Available qualities:', availableQualities);
                        
                        if (FORCED_QUALITY !== 'auto' && availableQualities.includes(FORCED_QUALITY)) {{
                            currentPlayer.setPlaybackQuality(FORCED_QUALITY);
                            console.log('Quality set to:', FORCED_QUALITY);
                        }}
                    }} catch (error) {{
                        console.error('Error setting quality:', error);
                    }}
                }}
                
                // Modify or hide quality controls
                function modifyQualityControls() {{
                    const settingsButton = document.querySelector('.ytp-settings-button');
                    const settingsMenu = document.querySelector('.ytp-settings-menu');
                    
                    if (HIDE_CONTROLS) {{
                        // Hide the entire settings button
                        if (settingsButton) {{
                            settingsButton.style.display = 'none';
                        }}
                    }} else if (LOCK_CONTROLS) {{
                        // Disable quality menu item
                        const observer = new MutationObserver(() => {{
                            const qualityMenuItem = document.querySelector('.ytp-menuitem[role=""menuitem""]');
                            if (qualityMenuItem && qualityMenuItem.textContent.includes('Quality')) {{
                                qualityMenuItem.style.display = 'none';
                            }}
                        }});
                        
                        if (settingsMenu) {{
                            observer.observe(settingsMenu, {{ childList: true, subtree: true }});
                        }}
                    }}
                }}
                
                // Set up continuous quality override
                function setupQualityOverride() {{
                    if (!currentPlayer) return;
                    
                    // Override quality change methods
                    const originalSetPlaybackQuality = currentPlayer.setPlaybackQuality;
                    currentPlayer.setPlaybackQuality = function(quality) {{
                        if (FORCED_QUALITY !== 'auto' && quality !== FORCED_QUALITY) {{
                            console.log('Quality change blocked. Enforcing:', FORCED_QUALITY);
                            return originalSetPlaybackQuality.call(this, FORCED_QUALITY);
                        }}
                        return originalSetPlaybackQuality.call(this, quality);
                    }};
                    
                    // Monitor for quality changes and revert them
                    setInterval(() => {{
                        if (currentPlayer && FORCED_QUALITY !== 'auto') {{
                            const currentQuality = currentPlayer.getPlaybackQuality();
                            if (currentQuality !== FORCED_QUALITY) {{
                                const availableQualities = currentPlayer.getAvailableQualityLevels();
                                if (availableQualities.includes(FORCED_QUALITY)) {{
                                    currentPlayer.setPlaybackQuality(FORCED_QUALITY);
                                    console.log('Quality restored to:', FORCED_QUALITY);
                                }}
                            }}
                        }}
                    }}, 2000);
                }}
                
                // Handle video changes (new videos loaded)
                function handleVideoChange() {{
                    setTimeout(() => {{
                        applyQualitySettings();
                        if (LOCK_CONTROLS || HIDE_CONTROLS) {{
                            modifyQualityControls();
                        }}
                    }}, 1000);
                }}
                
                // Set up observers for dynamic content
                const videoObserver = new MutationObserver((mutations) => {{
                    mutations.forEach((mutation) => {{
                        if (mutation.addedNodes.length > 0) {{
                            const hasVideoElement = Array.from(mutation.addedNodes).some(node => 
                                node.nodeType === 1 && (
                                    node.tagName === 'VIDEO' || 
                                    node.querySelector && node.querySelector('video')
                                )
                            );
                            if (hasVideoElement) {{
                                handleVideoChange();
                            }}
                        }}
                    }});
                }});
                
                // Start observing
                if (document.body) {{
                    videoObserver.observe(document.body, {{ childList: true, subtree: true }});
                }}
                
                // Initialize when page is ready
                if (document.readyState === 'loading') {{
                    document.addEventListener('DOMContentLoaded', waitForPlayer);
                }} else {{
                    waitForPlayer();
                }}
                
                // Also try when the page becomes visible (for SPA navigation)
                document.addEventListener('visibilitychange', () => {{
                    if (!document.hidden) {{
                        setTimeout(waitForPlayer, 500);
                    }}
                }});
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task HideQualityControls()
    {
        if (_webView == null) return;

        var hideControlsScript = @"
            (function() {
                const hideControls = () => {
                    // Hide settings button
                    const settingsButtons = document.querySelectorAll('.ytp-settings-button');
                    settingsButtons.forEach(btn => btn.style.display = 'none');
                    
                    // Hide quality menu items when they appear
                    const qualityMenuItems = document.querySelectorAll('.ytp-menuitem');
                    qualityMenuItems.forEach(item => {
                        if (item.textContent && item.textContent.includes('Quality')) {
                            item.style.display = 'none';
                        }
                    });
                };
                
                // Apply immediately and on interval
                hideControls();
                setInterval(hideControls, 1000);
                
                // Use MutationObserver for dynamic content
                const observer = new MutationObserver(hideControls);
                observer.observe(document.body, { childList: true, subtree: true });
            })();
        ";

        await _webView.ExecuteScriptAsync(hideControlsScript);
    }

    private async Task ApplyQualitySettings()
    {
        if (_webView == null || !AppSettings.ForceVideoQuality) return;

        var forcedQuality = _qualityMappings.GetValueOrDefault(AppSettings.ForcedVideoQuality, "auto");
        
        var applyQualityScript = $@"
            (function() {{
                const player = document.getElementById('movie_player') || document.querySelector('.html5-video-player');
                if (player && player.setPlaybackQuality && '{forcedQuality}' !== 'auto') {{
                    const availableQualities = player.getAvailableQualityLevels();
                    if (availableQualities.includes('{forcedQuality}')) {{
                        player.setPlaybackQuality('{forcedQuality}');
                        console.log('Quality forced to: {forcedQuality}');
                    }}
                }}
            }})();
        ";

        await _webView.ExecuteScriptAsync(applyQualityScript);
    }

    private void StartQualityMonitoring()
    {
        if (_disposed) return;
        
        StopQualityMonitoring();
        
        // Optimized: Reduced frequency from 3s to 8s to lower CPU usage
        _qualityMonitorTimer = new System.Timers.Timer(8000);
        _qualityMonitorTimer.Elapsed += OnQualityMonitorTick;
        _qualityMonitorTimer.AutoReset = true;
        _qualityMonitorTimer.Start();
    }

    private void StopQualityMonitoring()
    {
        _qualityMonitorTimer?.Stop();
        _qualityMonitorTimer?.Dispose();
        _qualityMonitorTimer = null;
    }

    private void OnQualityMonitorTick(object? sender, System.Timers.ElapsedEventArgs e)
    {
        if (_disposed || _webView == null) return;
        
        var now = DateTime.UtcNow;
        
        // Throttle checks to prevent excessive monitoring
        if (now - _lastQualityCheck < _minCheckInterval) return;
        
        // Use cached settings for performance
        if (!_settingsCache.ForceVideoQuality || !_settingsCache.OverrideUserQualityChanges) 
            return;

        _lastQualityCheck = now;

        try
        {
            PerformanceMonitor.Instance.MeasureOperation("VideoQualityController.MonitorTick", () =>
            {
                // Fire and forget to avoid blocking the timer
                _ = Task.Run(async () => await ApplyQualitySettings());
            });
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error in quality monitoring: {ex.Message}");
        }
    }

    public async Task RefreshQualitySettings()
    {
        if (_webView == null) return;

        try
        {
            await InjectQualityControlScript();
            await ApplyQualitySettings();
            
            if (AppSettings.HideQualityControls)
            {
                await HideQualityControls();
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error refreshing quality settings: {ex.Message}");
        }
    }

    public void UpdateSettings()
    {
        if (_disposed) return;
        
        PerformanceMonitor.Instance.MeasureOperation("VideoQualityController.UpdateSettings", () =>
        {
            if (_settingsCache.ForceVideoQuality)
            {
                EnableQualityControl();
                
                if (_settingsCache.OverrideUserQualityChanges)
                {
                    StartQualityMonitoring();
                }
                else
                {
                    StopQualityMonitoring();
                }
            }
            else
            {
                DisableQualityControl();
            }
        });
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        
        StopQualityMonitoring();
        
        if (_webView != null)
        {
            try
            {
                _webView.DOMContentLoaded -= OnDOMContentLoaded;
                _webView.NavigationCompleted -= OnNavigationCompleted;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error disposing VideoQualityController: {ex.Message}");
            }
        }
        
        _webView = null;
    }

    public static string[] GetAvailableQualities()
    {
        return new[] { "auto", "144p", "240p", "360p", "480p", "720p", "1080p", "1440p", "2160p" };
    }

    public static string GetQualityDisplayName(string quality)
    {
        return quality switch
        {
            "auto" => "Auto (YouTube Default)",
            "144p" => "144p (Lowest)",
            "240p" => "240p (Low)",
            "360p" => "360p (Medium)",
            "480p" => "480p (Standard)",
            "720p" => "720p (HD)",
            "1080p" => "1080p (Full HD)",
            "1440p" => "1440p (2K)",
            "2160p" => "2160p (4K)",
            _ => quality
        };
    }
}
