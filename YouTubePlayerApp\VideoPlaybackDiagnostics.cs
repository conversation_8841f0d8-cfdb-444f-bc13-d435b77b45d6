using Microsoft.Web.WebView2.Core;
using System.Text;

namespace YouTubePlayerApp;

/// <summary>
/// Comprehensive video playback diagnostics to ensure ad blocking doesn't interfere with video functionality
/// </summary>
public static class VideoPlaybackDiagnostics
{
    public static async Task<string> RunVideoPlaybackTestAsync(CoreWebView2 webView)
    {
        var diagnostics = new StringBuilder();
        diagnostics.AppendLine("=== Video Playback Diagnostics ===");
        diagnostics.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        diagnostics.AppendLine($"Current URL: {webView.Source}");
        diagnostics.AppendLine();

        // 1. Video Element Tests
        diagnostics.AppendLine("1. Video Element Status:");
        var videoTestScript = @"
            (function() {
                const video = document.querySelector('video.html5-main-video');
                if (!video) return 'Video element not found';
                
                const results = [];
                results.push(`Video found: Yes`);
                results.push(`Video src: ${video.src ? 'Set' : 'Not set'}`);
                results.push(`Video currentSrc: ${video.currentSrc ? 'Set' : 'Not set'}`);
                results.push(`Video readyState: ${video.readyState} (${getReadyStateText(video.readyState)})`);
                results.push(`Video networkState: ${video.networkState} (${getNetworkStateText(video.networkState)})`);
                results.push(`Video paused: ${video.paused}`);
                results.push(`Video ended: ${video.ended}`);
                results.push(`Video duration: ${video.duration || 'Unknown'}`);
                results.push(`Video currentTime: ${video.currentTime}`);
                results.push(`Video volume: ${video.volume}`);
                results.push(`Video muted: ${video.muted}`);
                results.push(`Video width: ${video.videoWidth}`);
                results.push(`Video height: ${video.videoHeight}`);
                results.push(`Video display: ${getComputedStyle(video).display}`);
                results.push(`Video visibility: ${getComputedStyle(video).visibility}`);
                results.push(`Video opacity: ${getComputedStyle(video).opacity}`);
                results.push(`Video pointer-events: ${getComputedStyle(video).pointerEvents}`);
                
                function getReadyStateText(state) {
                    switch(state) {
                        case 0: return 'HAVE_NOTHING';
                        case 1: return 'HAVE_METADATA';
                        case 2: return 'HAVE_CURRENT_DATA';
                        case 3: return 'HAVE_FUTURE_DATA';
                        case 4: return 'HAVE_ENOUGH_DATA';
                        default: return 'Unknown';
                    }
                }
                
                function getNetworkStateText(state) {
                    switch(state) {
                        case 0: return 'NETWORK_EMPTY';
                        case 1: return 'NETWORK_IDLE';
                        case 2: return 'NETWORK_LOADING';
                        case 3: return 'NETWORK_NO_SOURCE';
                        default: return 'Unknown';
                    }
                }
                
                return results.join('|');
            })();
        ";

        try
        {
            var videoResult = await webView.ExecuteScriptAsync(videoTestScript);
            var videoResults = videoResult.Replace("\"", "").Split('|');
            foreach (var result in videoResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing video element: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 2. Player Controls Tests
        diagnostics.AppendLine("2. Player Controls Status:");
        var controlsTestScript = @"
            (function() {
                const results = [];
                
                // Check player container
                const player = document.querySelector('.html5-video-player');
                if (player) {
                    results.push('Player container: Found');
                    results.push(`Player display: ${getComputedStyle(player).display}`);
                    results.push(`Player visibility: ${getComputedStyle(player).visibility}`);
                    results.push(`Player pointer-events: ${getComputedStyle(player).pointerEvents}`);
                } else {
                    results.push('Player container: Not found');
                }
                
                // Check controls bar
                const controls = document.querySelector('.ytp-chrome-bottom');
                if (controls) {
                    results.push('Controls bar: Found');
                    results.push(`Controls display: ${getComputedStyle(controls).display}`);
                    results.push(`Controls visibility: ${getComputedStyle(controls).visibility}`);
                    results.push(`Controls pointer-events: ${getComputedStyle(controls).pointerEvents}`);
                } else {
                    results.push('Controls bar: Not found');
                }
                
                // Check play button
                const playButton = document.querySelector('.ytp-play-button');
                if (playButton) {
                    results.push('Play button: Found');
                    results.push(`Play button display: ${getComputedStyle(playButton).display}`);
                    results.push(`Play button pointer-events: ${getComputedStyle(playButton).pointerEvents}`);
                } else {
                    results.push('Play button: Not found');
                }
                
                // Check large play button
                const largePlayButton = document.querySelector('.ytp-large-play-button');
                if (largePlayButton) {
                    results.push('Large play button: Found');
                    results.push(`Large play button display: ${getComputedStyle(largePlayButton).display}`);
                    results.push(`Large play button visibility: ${getComputedStyle(largePlayButton).visibility}`);
                } else {
                    results.push('Large play button: Not found');
                }
                
                // Check progress bar
                const progressBar = document.querySelector('.ytp-progress-bar-container');
                if (progressBar) {
                    results.push('Progress bar: Found');
                    results.push(`Progress bar display: ${getComputedStyle(progressBar).display}`);
                } else {
                    results.push('Progress bar: Not found');
                }
                
                // Check volume controls
                const volumePanel = document.querySelector('.ytp-volume-panel');
                if (volumePanel) {
                    results.push('Volume controls: Found');
                    results.push(`Volume controls display: ${getComputedStyle(volumePanel).display}`);
                } else {
                    results.push('Volume controls: Not found');
                }
                
                // Check settings button
                const settingsButton = document.querySelector('.ytp-settings-button');
                if (settingsButton) {
                    results.push('Settings button: Found');
                    results.push(`Settings button display: ${getComputedStyle(settingsButton).display}`);
                } else {
                    results.push('Settings button: Not found');
                }
                
                // Check fullscreen button
                const fullscreenButton = document.querySelector('.ytp-fullscreen-button');
                if (fullscreenButton) {
                    results.push('Fullscreen button: Found');
                    results.push(`Fullscreen button display: ${getComputedStyle(fullscreenButton).display}`);
                } else {
                    results.push('Fullscreen button: Not found');
                }
                
                return results.join('|');
            })();
        ";

        try
        {
            var controlsResult = await webView.ExecuteScriptAsync(controlsTestScript);
            var controlsResults = controlsResult.Replace("\"", "").Split('|');
            foreach (var result in controlsResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing player controls: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 3. Event Listeners Test
        diagnostics.AppendLine("3. Event Listeners Status:");
        var eventTestScript = @"
            (function() {
                const results = [];
                const video = document.querySelector('video.html5-main-video');
                
                if (video) {
                    // Test if video can receive events
                    let clickable = false;
                    let keyboardAccessible = false;
                    
                    try {
                        // Test click event
                        const clickEvent = new MouseEvent('click', { bubbles: true });
                        video.dispatchEvent(clickEvent);
                        clickable = true;
                    } catch (e) {
                        clickable = false;
                    }
                    
                    try {
                        // Test keyboard event
                        const keyEvent = new KeyboardEvent('keydown', { key: 'Space', bubbles: true });
                        video.dispatchEvent(keyEvent);
                        keyboardAccessible = true;
                    } catch (e) {
                        keyboardAccessible = false;
                    }
                    
                    results.push(`Video clickable: ${clickable}`);
                    results.push(`Video keyboard accessible: ${keyboardAccessible}`);
                    
                    // Check if video has event listeners
                    const hasClickListeners = video.onclick !== null || 
                                            getEventListeners && getEventListeners(video).click && 
                                            getEventListeners(video).click.length > 0;
                    results.push(`Video has click listeners: ${hasClickListeners ? 'Yes' : 'Unknown'}`);
                } else {
                    results.push('Video element not found for event testing');
                }
                
                // Test player controls
                const playButton = document.querySelector('.ytp-play-button');
                if (playButton) {
                    const playButtonClickable = playButton.style.pointerEvents !== 'none' && 
                                              getComputedStyle(playButton).pointerEvents !== 'none';
                    results.push(`Play button clickable: ${playButtonClickable}`);
                } else {
                    results.push('Play button not found for event testing');
                }
                
                return results.join('|');
            })();
        ";

        try
        {
            var eventResult = await webView.ExecuteScriptAsync(eventTestScript);
            var eventResults = eventResult.Replace("\"", "").Split('|');
            foreach (var result in eventResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing event listeners: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 4. Ad Interference Check
        diagnostics.AppendLine("4. Ad Interference Check:");
        var adInterferenceScript = @"
            (function() {
                const results = [];
                
                // Check for ad overlays that might block video
                const adOverlays = document.querySelectorAll('.ytp-ad-overlay-container, .ytp-ad-module');
                results.push(`Ad overlays found: ${adOverlays.length}`);
                
                if (adOverlays.length > 0) {
                    adOverlays.forEach((overlay, index) => {
                        const style = getComputedStyle(overlay);
                        results.push(`Ad overlay ${index + 1} display: ${style.display}`);
                        results.push(`Ad overlay ${index + 1} visibility: ${style.visibility}`);
                        results.push(`Ad overlay ${index + 1} z-index: ${style.zIndex}`);
                    });
                }
                
                // Check for elements that might be blocking video
                const video = document.querySelector('video.html5-main-video');
                if (video) {
                    const videoRect = video.getBoundingClientRect();
                    const centerX = videoRect.left + videoRect.width / 2;
                    const centerY = videoRect.top + videoRect.height / 2;
                    
                    const elementAtCenter = document.elementFromPoint(centerX, centerY);
                    if (elementAtCenter) {
                        results.push(`Element at video center: ${elementAtCenter.tagName}.${elementAtCenter.className}`);
                        results.push(`Is video element: ${elementAtCenter === video || elementAtCenter.closest('video')}`);
                    }
                }
                
                // Check for skip buttons (indicates ads are present)
                const skipButtons = document.querySelectorAll('.ytp-ad-skip-button, .ytp-skip-ad-button');
                results.push(`Skip buttons found: ${skipButtons.length}`);
                
                return results.join('|');
            })();
        ";

        try
        {
            var adResult = await webView.ExecuteScriptAsync(adInterferenceScript);
            var adResults = adResult.Replace("\"", "").Split('|');
            foreach (var result in adResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error checking ad interference: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 5. Recommendations
        diagnostics.AppendLine("5. Recommendations:");
        
        // Add specific recommendations based on findings
        diagnostics.AppendLine("   💡 If video playback issues are found:");
        diagnostics.AppendLine("   • Ensure VideoSafeAdBlocker is being used instead of aggressive AdBlocker");
        diagnostics.AppendLine("   • Check that video elements have proper CSS properties (display, visibility, pointer-events)");
        diagnostics.AppendLine("   • Verify that ad blocking CSS doesn't target video player elements");
        diagnostics.AppendLine("   • Ensure JavaScript ad blocking preserves video functionality");
        diagnostics.AppendLine("   • Test with different video types (regular, live, shorts)");
        
        diagnostics.AppendLine();
        diagnostics.AppendLine("=== End of Video Playback Diagnostics ===");

        return diagnostics.ToString();
    }

    public static async Task<string> FixVideoPlaybackIssuesAsync(CoreWebView2 webView)
    {
        var fixScript = @"
            (function() {
                console.log('[VideoPlaybackDiagnostics] Fixing video playback issues...');
                let fixesApplied = 0;
                
                // Fix 1: Ensure video element is visible and functional
                const video = document.querySelector('video.html5-main-video');
                if (video) {
                    video.style.display = 'block';
                    video.style.visibility = 'visible';
                    video.style.opacity = '1';
                    video.style.pointerEvents = 'auto';
                    video.style.cursor = 'pointer';
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Fixed video element visibility');
                }
                
                // Fix 2: Ensure player container is functional
                const player = document.querySelector('.html5-video-player');
                if (player) {
                    player.style.display = 'block';
                    player.style.visibility = 'visible';
                    player.style.pointerEvents = 'auto';
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Fixed player container');
                }
                
                // Fix 3: Ensure controls are visible and functional
                const controls = document.querySelector('.ytp-chrome-bottom');
                if (controls) {
                    controls.style.display = 'flex';
                    controls.style.visibility = 'visible';
                    controls.style.pointerEvents = 'auto';
                    controls.style.zIndex = '9999';
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Fixed player controls');
                }
                
                // Fix 4: Ensure all control buttons are functional
                const controlButtons = document.querySelectorAll('.ytp-chrome-bottom button, .ytp-chrome-controls button');
                controlButtons.forEach(button => {
                    button.style.pointerEvents = 'auto';
                    button.style.cursor = 'pointer';
                    button.style.display = 'block';
                    button.style.visibility = 'visible';
                });
                if (controlButtons.length > 0) {
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Fixed control buttons');
                }
                
                // Fix 5: Ensure play buttons are functional
                const playButtons = document.querySelectorAll('.ytp-play-button, .ytp-large-play-button');
                playButtons.forEach(button => {
                    button.style.display = 'block';
                    button.style.visibility = 'visible';
                    button.style.pointerEvents = 'auto';
                    button.style.cursor = 'pointer';
                    button.style.opacity = '1';
                });
                if (playButtons.length > 0) {
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Fixed play buttons');
                }
                
                // Fix 6: Remove any overlays that might block video interaction
                const blockingOverlays = document.querySelectorAll('[style*=""pointer-events: none""], [style*=""display: none""]');
                blockingOverlays.forEach(overlay => {
                    if (overlay.closest('.html5-video-player') && !overlay.classList.contains('ad')) {
                        overlay.style.pointerEvents = 'auto';
                        overlay.style.display = 'block';
                        overlay.style.visibility = 'visible';
                    }
                });
                
                // Fix 7: Ensure progress bar is functional
                const progressBar = document.querySelector('.ytp-progress-bar-container');
                if (progressBar) {
                    progressBar.style.pointerEvents = 'auto';
                    progressBar.style.display = 'block';
                    progressBar.style.visibility = 'visible';
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Fixed progress bar');
                }
                
                // Fix 8: Re-enable any disabled event listeners
                if (video && !video.hasAttribute('data-playback-fixed')) {
                    video.setAttribute('data-playback-fixed', 'true');
                    
                    // Ensure click events work
                    video.addEventListener('click', function(e) {
                        console.log('[VideoPlaybackDiagnostics] Video click event preserved');
                    }, false);
                    
                    // Ensure keyboard events work
                    video.addEventListener('keydown', function(e) {
                        console.log('[VideoPlaybackDiagnostics] Video keyboard event preserved');
                    }, false);
                    
                    fixesApplied++;
                    console.log('[VideoPlaybackDiagnostics] Re-enabled video event listeners');
                }
                
                console.log('[VideoPlaybackDiagnostics] Video playback fixes completed. Applied:', fixesApplied);
                return fixesApplied;
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(fixScript);
            if (int.TryParse(result, out int fixesApplied))
            {
                return $"Video playback fixes completed. Applied {fixesApplied} fixes to ensure proper video functionality.";
            }
        }
        catch (Exception ex)
        {
            return $"Error applying video playback fixes: {ex.Message}";
        }

        return "Video playback fixes completed with unknown results.";
    }

    public static async Task<bool> TestVideoPlaybackFunctionalityAsync(CoreWebView2 webView)
    {
        var testScript = @"
            (function() {
                // Test if video playback functionality is working
                const video = document.querySelector('video.html5-main-video');
                if (!video) return false;
                
                // Check if video is visible and interactive
                const style = getComputedStyle(video);
                const isVisible = style.display !== 'none' && 
                                style.visibility !== 'hidden' && 
                                style.opacity !== '0';
                
                const isInteractive = style.pointerEvents !== 'none';
                
                // Check if controls are functional
                const controls = document.querySelector('.ytp-chrome-bottom');
                const controlsWorking = controls && 
                                      getComputedStyle(controls).display !== 'none' &&
                                      getComputedStyle(controls).pointerEvents !== 'none';
                
                // Check if play button exists and is functional
                const playButton = document.querySelector('.ytp-play-button, .ytp-large-play-button');
                const playButtonWorking = playButton && 
                                        getComputedStyle(playButton).display !== 'none' &&
                                        getComputedStyle(playButton).pointerEvents !== 'none';
                
                return isVisible && isInteractive && controlsWorking && playButtonWorking;
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(testScript);
            return result.Trim().ToLower() == "true";
        }
        catch
        {
            return false;
        }
    }
}