using System.Configuration;
using System.IO;
using System.Text.Json;

namespace YouTubePlayerApp;

/// <summary>
/// Application settings management
/// </summary>
public static class AppSettings
{
    private static readonly string SettingsFilePath = 
        Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), 
                     "YouTubePlayerApp", "settings.json");

    // General Settings
    public static bool StartWithWindows { get; set; } = false;
    public static bool StartFullScreen { get; set; } = false;
    public static bool RememberLastPosition { get; set; } = true;
    public static string HomePage { get; set; } = "https://www.youtube.com";

    // Video Settings
    public static int DefaultVideoQuality { get; set; } = 0; // Auto
    public static bool EnableAutoplay { get; set; } = true;
    public static bool MuteOnStartup { get; set; } = false;
    public static int DefaultVolume { get; set; } = 50;

    // Privacy Settings
    public static bool ClearCookiesOnExit { get; set; } = false;
    public static bool BlockThirdPartyCookies { get; set; } = false;
    public static bool DoNotTrack { get; set; } = false;
    public static bool BlockAds { get; set; } = false;
    
    // Advanced Ad Blocking Settings
    public static bool BlockVideoAds { get; set; } = true;
    public static bool BlockBannerAds { get; set; } = true;
    public static bool BlockTrackingScripts { get; set; } = true;
    public static bool AutoSkipAds { get; set; } = true;
    public static bool ShowAdBlockStats { get; set; } = false;
    public static bool ShowPerformanceIndicator { get; set; } = false;
    
    // YouTube Enhancement Settings
    public static bool DisableAutoPreview { get; set; } = false;
    
    // Video Quality Control Settings
    public static bool ForceVideoQuality { get; set; } = false;
    public static string ForcedVideoQuality { get; set; } = "auto"; // auto, 144p, 240p, 360p, 480p, 720p, 1080p, 1440p, 2160p
    public static bool LockQualityControls { get; set; } = false;
    public static bool HideQualityControls { get; set; } = false;
    public static bool OverrideUserQualityChanges { get; set; } = true;

    // Window Settings
    public static double WindowWidth { get; set; } = 1200;
    public static double WindowHeight { get; set; } = 800;
    public static double WindowLeft { get; set; } = -1;
    public static double WindowTop { get; set; } = -1;
    public static bool WindowMaximized { get; set; } = true;

    static AppSettings()
    {
        Load();
    }

    public static void Load()
    {
        try
        {
            if (File.Exists(SettingsFilePath))
            {
                var json = File.ReadAllText(SettingsFilePath);
                var settings = JsonSerializer.Deserialize<SettingsData>(json);
                if (settings != null)
                {
                    ApplySettings(settings);
                }
            }
        }
        catch (Exception ex)
        {
            // Log error in production
            System.Diagnostics.Debug.WriteLine($"Error loading settings: {ex.Message}");
        }
    }

    public static void Save()
    {
        try
        {
            var settings = new SettingsData
            {
                StartWithWindows = StartWithWindows,
                StartFullScreen = StartFullScreen,
                RememberLastPosition = RememberLastPosition,
                HomePage = HomePage,
                DefaultVideoQuality = DefaultVideoQuality,
                EnableAutoplay = EnableAutoplay,
                MuteOnStartup = MuteOnStartup,
                DefaultVolume = DefaultVolume,
                ClearCookiesOnExit = ClearCookiesOnExit,
                BlockThirdPartyCookies = BlockThirdPartyCookies,
                DoNotTrack = DoNotTrack,
                BlockAds = BlockAds,
                BlockVideoAds = BlockVideoAds,
                BlockBannerAds = BlockBannerAds,
                BlockTrackingScripts = BlockTrackingScripts,
                AutoSkipAds = AutoSkipAds,
                ShowAdBlockStats = ShowAdBlockStats,
                DisableAutoPreview = DisableAutoPreview,
                ForceVideoQuality = ForceVideoQuality,
                ForcedVideoQuality = ForcedVideoQuality,
                LockQualityControls = LockQualityControls,
                HideQualityControls = HideQualityControls,
                OverrideUserQualityChanges = OverrideUserQualityChanges,
                WindowWidth = WindowWidth,
                WindowHeight = WindowHeight,
                WindowLeft = WindowLeft,
                WindowTop = WindowTop,
                WindowMaximized = WindowMaximized
            };

            var directory = Path.GetDirectoryName(SettingsFilePath);
            if (!Directory.Exists(directory))
                Directory.CreateDirectory(directory!);

            var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions { WriteIndented = true });
            File.WriteAllText(SettingsFilePath, json);
        }
        catch (Exception ex)
        {
            // Log error in production
            System.Diagnostics.Debug.WriteLine($"Error saving settings: {ex.Message}");
        }
    }

    private static void ApplySettings(SettingsData settings)
    {
        StartWithWindows = settings.StartWithWindows;
        StartFullScreen = settings.StartFullScreen;
        RememberLastPosition = settings.RememberLastPosition;
        HomePage = settings.HomePage;
        DefaultVideoQuality = settings.DefaultVideoQuality;
        EnableAutoplay = settings.EnableAutoplay;
        MuteOnStartup = settings.MuteOnStartup;
        DefaultVolume = settings.DefaultVolume;
        ClearCookiesOnExit = settings.ClearCookiesOnExit;
        BlockThirdPartyCookies = settings.BlockThirdPartyCookies;
        DoNotTrack = settings.DoNotTrack;
        BlockAds = settings.BlockAds;
        BlockVideoAds = settings.BlockVideoAds;
        BlockBannerAds = settings.BlockBannerAds;
        BlockTrackingScripts = settings.BlockTrackingScripts;
        AutoSkipAds = settings.AutoSkipAds;
        ShowAdBlockStats = settings.ShowAdBlockStats;
        DisableAutoPreview = settings.DisableAutoPreview;
        ForceVideoQuality = settings.ForceVideoQuality;
        ForcedVideoQuality = settings.ForcedVideoQuality;
        LockQualityControls = settings.LockQualityControls;
        HideQualityControls = settings.HideQualityControls;
        OverrideUserQualityChanges = settings.OverrideUserQualityChanges;
        WindowWidth = settings.WindowWidth;
        WindowHeight = settings.WindowHeight;
        WindowLeft = settings.WindowLeft;
        WindowTop = settings.WindowTop;
        WindowMaximized = settings.WindowMaximized;
    }

    private class SettingsData
    {
        public bool StartWithWindows { get; set; }
        public bool StartFullScreen { get; set; }
        public bool RememberLastPosition { get; set; }
        public string HomePage { get; set; } = "https://www.youtube.com";
        public int DefaultVideoQuality { get; set; }
        public bool EnableAutoplay { get; set; }
        public bool MuteOnStartup { get; set; }
        public int DefaultVolume { get; set; }
        public bool ClearCookiesOnExit { get; set; }
        public bool BlockThirdPartyCookies { get; set; }
        public bool DoNotTrack { get; set; }
        public bool BlockAds { get; set; }
        public bool BlockVideoAds { get; set; }
        public bool BlockBannerAds { get; set; }
        public bool BlockTrackingScripts { get; set; }
        public bool AutoSkipAds { get; set; }
        public bool ShowAdBlockStats { get; set; }
        public bool DisableAutoPreview { get; set; }
        public bool ForceVideoQuality { get; set; }
        public string ForcedVideoQuality { get; set; } = "auto";
        public bool LockQualityControls { get; set; }
        public bool HideQualityControls { get; set; }
        public bool OverrideUserQualityChanges { get; set; }
        public double WindowWidth { get; set; }
        public double WindowHeight { get; set; }
        public double WindowLeft { get; set; }
        public double WindowTop { get; set; }
        public bool WindowMaximized { get; set; }
    }
}
