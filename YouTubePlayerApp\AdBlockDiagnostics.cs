using Microsoft.Web.WebView2.Core;
using System.Text;

namespace YouTubePlayerApp;

/// <summary>
/// Comprehensive ad blocking diagnostics and testing
/// </summary>
public static class AdBlockDiagnostics
{
    public static async Task<string> RunComprehensiveDiagnosticsAsync(CoreWebView2 webView)
    {
        var diagnostics = new StringBuilder();
        diagnostics.AppendLine("=== YouTube Ad Blocking Diagnostics ===");
        diagnostics.AppendLine($"Timestamp: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        diagnostics.AppendLine($"Current URL: {webView.Source}");
        diagnostics.AppendLine();

        // 1. Check settings
        diagnostics.AppendLine("1. Ad Blocking Settings:");
        diagnostics.AppendLine($"   BlockAds: {AppSettings.BlockAds}");
        diagnostics.AppendLine($"   BlockVideoAds: {AppSettings.BlockVideoAds}");
        diagnostics.AppendLine($"   BlockBannerAds: {AppSettings.BlockBannerAds}");
        diagnostics.AppendLine($"   BlockTrackingScripts: {AppSettings.BlockTrackingScripts}");
        diagnostics.AppendLine($"   AutoSkipAds: {AppSettings.AutoSkipAds}");
        diagnostics.AppendLine($"   ShowAdBlockStats: {AppSettings.ShowAdBlockStats}");
        diagnostics.AppendLine($"   DoNotTrack: {AppSettings.DoNotTrack}");
        diagnostics.AppendLine();

        // 2. Test DOM for ad elements
        diagnostics.AppendLine("2. DOM Ad Element Detection:");
        var domTestScript = @"
            (function() {
                const adSelectors = [
                    '.video-ads', '.ytp-ad-module', '.ytp-ad-overlay-container',
                    '.ytd-promoted-video-renderer', '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-banner-promo-renderer', '.ytd-in-feed-ad-layout-renderer',
                    '.ytd-display-ad-renderer', '.ytd-companion-slot-renderer',
                    '#player-ads', '#masthead-ad', '#watch-sidebar-ads',
                    '.ytd-popup-container[dialog]', '.ytd-mealbar-promo-renderer'
                ];
                
                let results = [];
                adSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    results.push(`${selector}: ${elements.length} elements`);
                });
                
                return results.join('|');
            })();
        ";

        try
        {
            var domResult = await webView.ExecuteScriptAsync(domTestScript);
            var domResults = domResult.Replace("\"", "").Split('|');
            foreach (var result in domResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing DOM: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 3. Test YouTube API overrides
        diagnostics.AppendLine("3. YouTube API Override Status:");
        var apiTestScript = @"
            (function() {
                let results = [];
                
                // Check window.yt config
                if (window.yt && window.yt.config_) {
                    results.push('yt.config: Available');
                    if (window.yt.config_.EXPERIMENT_FLAGS) {
                        const flags = window.yt.config_.EXPERIMENT_FLAGS;
                        const adFlags = [
                            'html5_disable_preroll_ads',
                            'html5_disable_midroll_ads', 
                            'html5_disable_postroll_ads',
                            'html5_disable_overlay_ads'
                        ];
                        let flagsSet = 0;
                        adFlags.forEach(flag => {
                            if (flags[flag] === true) flagsSet++;
                        });
                        results.push(`Ad blocking flags: ${flagsSet}/${adFlags.length} set`);
                    } else {
                        results.push('EXPERIMENT_FLAGS: Not available');
                    }
                } else {
                    results.push('yt.config: Not available');
                }
                
                // Check player
                const player = document.getElementById('movie_player') || 
                              document.querySelector('.html5-video-player');
                if (player) {
                    results.push('Player: Found');
                    const hasAdClasses = player.classList.contains('ad-showing') ||
                                       player.classList.contains('ad-interrupting');
                    results.push(`Player ad classes: ${hasAdClasses ? 'Present' : 'None'}`);
                } else {
                    results.push('Player: Not found');
                }
                
                // Check video element
                const video = document.querySelector('video');
                if (video) {
                    results.push('Video element: Found');
                    results.push(`Video duration: ${video.duration || 'Unknown'}`);
                    const hasAdAttrs = video.hasAttribute('data-ad') ||
                                     video.hasAttribute('data-ad-status');
                    results.push(`Video ad attributes: ${hasAdAttrs ? 'Present' : 'None'}`);
                } else {
                    results.push('Video element: Not found');
                }
                
                return results.join('|');
            })();
        ";

        try
        {
            var apiResult = await webView.ExecuteScriptAsync(apiTestScript);
            var apiResults = apiResult.Replace("\"", "").Split('|');
            foreach (var result in apiResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing API: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 4. Test network blocking
        diagnostics.AppendLine("4. Network Blocking Test:");
        var networkTestScript = @"
            (function() {
                const testUrls = [
                    'https://googleads.g.doubleclick.net/pagead/ads',
                    'https://www.youtube.com/pagead/interaction',
                    'https://www.youtube.com/ptracking',
                    'https://googlesyndication.com/safeframe/'
                ];
                
                let results = [];
                testUrls.forEach(url => {
                    const isAdUrl = /pagead|doubleclick|googlesyndication|ptracking/.test(url);
                    results.push(`${url.split('/')[2]}: ${isAdUrl ? 'Would block' : 'Would allow'}`);
                });
                
                return results.join('|');
            })();
        ";

        try
        {
            var networkResult = await webView.ExecuteScriptAsync(networkTestScript);
            var networkResults = networkResult.Replace("\"", "").Split('|');
            foreach (var result in networkResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing network: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 5. Video playback protection test
        diagnostics.AppendLine("5. Video Playback Protection:");
        var videoProtectionScript = @"
            (function() {
                let results = [];
                
                // Check video element
                const video = document.querySelector('video.html5-main-video');
                if (video) {
                    results.push('Video element: Found');
                    results.push(`Video display: ${video.style.display || 'default'}`);
                    results.push(`Video visibility: ${video.style.visibility || 'default'}`);
                    results.push(`Video pointer-events: ${video.style.pointerEvents || 'default'}`);
                    results.push(`Video ready state: ${video.readyState}`);
                } else {
                    results.push('Video element: Not found');
                }
                
                // Check player controls
                const controls = document.querySelector('.ytp-chrome-bottom');
                if (controls) {
                    results.push('Player controls: Found');
                    results.push(`Controls display: ${controls.style.display || 'default'}`);
                    results.push(`Controls pointer-events: ${controls.style.pointerEvents || 'default'}`);
                } else {
                    results.push('Player controls: Not found');
                }
                
                // Check play button
                const playButton = document.querySelector('.ytp-play-button');
                if (playButton) {
                    results.push('Play button: Found and functional');
                } else {
                    results.push('Play button: Not found');
                }
                
                return results.join('|');
            })();
        ";

        try
        {
            var videoResult = await webView.ExecuteScriptAsync(videoProtectionScript);
            var videoResults = videoResult.Replace("\"", "").Split('|');
            foreach (var result in videoResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error testing video protection: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 6. Performance impact
        diagnostics.AppendLine("6. Performance Metrics:");
        var performanceScript = @"
            (function() {
                const perf = performance.getEntriesByType('navigation')[0];
                if (perf) {
                    const loadTime = perf.loadEventEnd - perf.loadEventStart;
                    const domTime = perf.domContentLoadedEventEnd - perf.domContentLoadedEventStart;
                    return `Load time: ${Math.round(loadTime)}ms|DOM time: ${Math.round(domTime)}ms`;
                }
                return 'Performance data not available';
            })();
        ";

        try
        {
            var perfResult = await webView.ExecuteScriptAsync(performanceScript);
            var perfResults = perfResult.Replace("\"", "").Split('|');
            foreach (var result in perfResults)
            {
                diagnostics.AppendLine($"   {result}");
            }
        }
        catch (Exception ex)
        {
            diagnostics.AppendLine($"   Error getting performance: {ex.Message}");
        }
        diagnostics.AppendLine();

        // 7. Recommendations
        diagnostics.AppendLine("7. Recommendations:");
        if (!AppSettings.BlockAds)
        {
            diagnostics.AppendLine("   ⚠️  Enable ad blocking in settings");
        }
        if (!AppSettings.ShowAdBlockStats)
        {
            diagnostics.AppendLine("   💡 Enable ad block statistics to monitor effectiveness");
        }
        if (!AppSettings.DoNotTrack)
        {
            diagnostics.AppendLine("   🔒 Enable Do Not Track for better privacy");
        }
        
        diagnostics.AppendLine();
        diagnostics.AppendLine("=== End of Diagnostics ===");

        return diagnostics.ToString();
    }

    public static async Task<bool> TestAdBlockingEffectivenessAsync(CoreWebView2 webView)
    {
        var testScript = @"
            (function() {
                // Test for common ad elements
                const adSelectors = [
                    '.video-ads', '.ytp-ad-module', '.ytp-ad-overlay-container',
                    '.ytd-promoted-video-renderer', '.ytd-banner-promo-renderer',
                    '#player-ads', '#masthead-ad'
                ];
                
                let adElementsFound = 0;
                adSelectors.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    adElementsFound += elements.length;
                });
                
                // Test for ad-related attributes
                const adAttributes = document.querySelectorAll('[data-is-ad=""true""], [data-promoted=""true""]');
                adElementsFound += adAttributes.length;
                
                // Test for skip buttons (indicates ads are present)
                const skipButtons = document.querySelectorAll('.ytp-ad-skip-button, .ytp-skip-ad-button');
                adElementsFound += skipButtons.length;
                
                return adElementsFound;
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(testScript);
            if (int.TryParse(result, out int adCount))
            {
                return adCount == 0; // Effective if no ads found
            }
        }
        catch
        {
            // If script fails, assume ads might be present
        }

        return false;
    }

    public static async Task<string> ForceAdRemovalAsync(CoreWebView2 webView)
    {
        var forceRemovalScript = @"
            (function() {
                console.log('[AdBlockDiagnostics] Force ad removal started...');
                let removedCount = 0;
                
                // Comprehensive ad selectors
                const adSelectors = [
                    // Video ads
                    '.video-ads', '.ytp-ad-module', '.ytp-ad-overlay-container',
                    '.ytp-ad-image-overlay', '.ytp-ad-text-overlay', '.ytp-ad-player-overlay',
                    
                    // Banner and display ads
                    '.ytd-promoted-video-renderer', '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-banner-promo-renderer', '.ytd-in-feed-ad-layout-renderer',
                    '.ytd-display-ad-renderer', '.ytd-companion-slot-renderer',
                    '.ytd-action-companion-ad-renderer',
                    
                    // Sidebar and companion ads
                    '#player-ads', '#masthead-ad', '#watch-sidebar-ads',
                    
                    // Premium upsells and popups
                    '.ytd-popup-container[dialog][role=""dialog""]',
                    '.ytd-mealbar-promo-renderer', '.ytd-premium-upsell-renderer',
                    '.ytd-upsell-dialog-renderer', '.ytd-background-promo-renderer',
                    
                    // Shopping ads
                    '.ytd-product-details-renderer', '.ytd-merch-shelf-renderer',
                    '.ytd-shopping-shelf-renderer', '.ytd-product-shelf-renderer',
                    
                    // Generic patterns
                    '[data-is-ad=""true""]', '[data-promoted=""true""]', '[data-ad-status]',
                    '[class*=""promoted""]', '[class*=""sponsor""]', '[class*=""advertisement""]'
                ];
                
                // Remove ad elements
                adSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            // Ensure we don't remove video player elements
                            if (!el.closest('.html5-video-player') && 
                                !el.classList.contains('html5-main-video') &&
                                el.tagName !== 'VIDEO') {
                                el.remove();
                                removedCount++;
                            }
                        });
                    } catch (e) {
                        console.warn('[AdBlockDiagnostics] Error removing ads with selector:', selector, e);
                    }
                });
                
                // Auto-click skip buttons
                const skipButtons = document.querySelectorAll('.ytp-ad-skip-button, .ytp-skip-ad-button');
                skipButtons.forEach(btn => {
                    if (btn.offsetParent !== null) {
                        btn.click();
                        removedCount++;
                    }
                });
                
                // Hide ad containers with CSS
                const hideCSS = `
                    .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
                    .ytd-promoted-video-renderer, .ytd-banner-promo-renderer,
                    #player-ads, #masthead-ad, [data-is-ad=""true""],
                    [data-promoted=""true""], [class*=""promoted""],
                    [class*=""sponsor""], [class*=""advertisement""]
                    { display: none !important; visibility: hidden !important; }
                `;
                
                const style = document.createElement('style');
                style.textContent = hideCSS;
                document.head.appendChild(style);
                
                console.log('[AdBlockDiagnostics] Force ad removal completed. Removed:', removedCount);
                return removedCount;
            })();
        ";

        try
        {
            var result = await webView.ExecuteScriptAsync(forceRemovalScript);
            if (int.TryParse(result, out int removedCount))
            {
                return $"Force ad removal completed. Removed {removedCount} ad elements.";
            }
        }
        catch (Exception ex)
        {
            return $"Error during force ad removal: {ex.Message}";
        }

        return "Force ad removal completed with unknown results.";
    }
}