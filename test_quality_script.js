// Test script to verify YouTube quality control functionality
// Paste this into browser console on a YouTube video page to test

(function() {
    'use strict';
    
    console.log('=== YouTube Quality Control Test Script ===');
    
    // Test 1: Check for YouTube player
    console.log('\n1. Testing Player Detection:');
    const player = document.getElementById('movie_player') || 
                  document.querySelector('.html5-video-player') ||
                  document.querySelector('#player-container .html5-video-player');
    
    console.log('Player found:', !!player);
    if (player) {
        console.log('Player element:', player.tagName, player.className);
        console.log('Has setPlaybackQuality:', typeof player.setPlaybackQuality === 'function');
        console.log('Has getAvailableQualityLevels:', typeof player.getAvailableQualityLevels === 'function');
        
        if (typeof player.getAvailableQualityLevels === 'function') {
            try {
                const qualities = player.getAvailableQualityLevels();
                console.log('Available qualities:', qualities);
            } catch (e) {
                console.log('Error getting qualities:', e.message);
            }
        }
    }
    
    // Test 2: Check for video element
    console.log('\n2. Testing Video Element:');
    const video = document.querySelector('video');
    console.log('Video found:', !!video);
    if (video) {
        console.log('Video ready state:', video.readyState);
        console.log('Video dimensions:', video.videoWidth + 'x' + video.videoHeight);
        console.log('Video source:', video.currentSrc ? 'Available' : 'Not loaded');
    }
    
    // Test 3: Check for YouTube API
    console.log('\n3. Testing YouTube API:');
    console.log('window.yt exists:', !!window.yt);
    console.log('window.ytplayer exists:', !!window.ytplayer);
    
    if (window.yt) {
        console.log('yt.player exists:', !!window.yt.player);
        console.log('yt.config_ exists:', !!window.yt.config_);
        if (window.yt.player && window.yt.player.getPlayerByElement) {
            console.log('getPlayerByElement available:', true);
        }
    }
    
    // Test 4: Check for settings button
    console.log('\n4. Testing Settings Button:');
    const settingsButton = document.querySelector('.ytp-settings-button') ||
                          document.querySelector('[data-tooltip-target-id="ytp-settings-button"]') ||
                          document.querySelector('.ytp-chrome-controls .ytp-button[aria-label*="Settings"]');
    
    console.log('Settings button found:', !!settingsButton);
    if (settingsButton) {
        console.log('Settings button visible:', settingsButton.offsetParent !== null);
        console.log('Settings button enabled:', !settingsButton.disabled);
    }
    
    // Test 5: Test quality setting (if player available)
    if (player && typeof player.setPlaybackQuality === 'function') {
        console.log('\n5. Testing Quality Setting:');
        try {
            const originalQuality = player.getPlaybackQuality ? player.getPlaybackQuality() : 'unknown';
            console.log('Current quality:', originalQuality);
            
            // Try setting to 720p
            player.setPlaybackQuality('hd720');
            console.log('Attempted to set quality to hd720');
            
            setTimeout(() => {
                const newQuality = player.getPlaybackQuality ? player.getPlaybackQuality() : 'unknown';
                console.log('Quality after setting:', newQuality);
                console.log('Quality change successful:', newQuality === 'hd720');
            }, 2000);
            
        } catch (e) {
            console.log('Error testing quality setting:', e.message);
        }
    }
    
    // Test 6: DOM Manipulation Test
    console.log('\n6. Testing DOM Manipulation Capability:');
    if (settingsButton) {
        console.log('Can simulate settings click: Yes');
        
        // Test menu detection after simulated click
        setTimeout(() => {
            try {
                settingsButton.click();
                console.log('Settings button clicked');
                
                setTimeout(() => {
                    const menuItems = document.querySelectorAll('.ytp-menuitem');
                    console.log('Menu items found:', menuItems.length);
                    
                    const qualityItem = Array.from(menuItems).find(item => 
                        item.textContent && item.textContent.toLowerCase().includes('quality')
                    );
                    console.log('Quality menu item found:', !!qualityItem);
                    
                    // Close menu
                    document.body.click();
                }, 500);
            } catch (e) {
                console.log('Error testing DOM manipulation:', e.message);
            }
        }, 1000);
    } else {
        console.log('Cannot test DOM manipulation - settings button not found');
    }
    
    console.log('\n=== Test Complete ===');
    console.log('Check the results above to verify YouTube compatibility');
    
})();