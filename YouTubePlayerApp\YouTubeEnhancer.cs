using System;
using System.Threading.Tasks;
using Microsoft.Web.WebView2.Core;

namespace YouTubePlayerApp;

/// <summary>
/// Manages YouTube-specific enhancements and optimizations
/// </summary>
public class YouTubeEnhancer : IDisposable
{
    private CoreWebView2? webView;
    private bool disposed = false;
    private bool autoPreviewDisabled = false;

    public void Initialize(CoreWebView2 coreWebView2)
    {
        webView = coreWebView2;
        
        // Subscribe to navigation events to reapply settings
        webView.NavigationCompleted += OnNavigationCompleted;
        webView.DOMContentLoaded += OnDOMContentLoaded;
        
        AppLogger.Instance.LogInfo("YouTubeEnhancer", "YouTube enhancer initialized successfully");
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (e.IsSuccess && webView != null)
        {
            await ApplyEnhancements();
        }
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (webView != null)
        {
            await ApplyEnhancements();
        }
    }

    public async Task ApplyEnhancements()
    {
        if (webView == null) return;

        try
        {
            // Apply auto preview disabling if enabled
            if (AppSettings.DisableAutoPreview)
            {
                await DisableAutoPreview();
            }
            else
            {
                await EnableAutoPreview();
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("YouTubeEnhancer", ex, "Error applying YouTube enhancements");
        }
    }

    private async Task DisableAutoPreview()
    {
        if (webView == null || autoPreviewDisabled) return;

        try
        {
            string script = @"
                (function() {
                    if (window.youtubeEnhancerApplied) return;
                    window.youtubeEnhancerApplied = true;
                    
                    console.log('[YouTubeEnhancer] Disabling auto preview...');
                    
                    // Function to disable preview on thumbnails
                    function disableAutoPreview() {
                        // Remove existing event listeners and preview elements
                        const thumbnails = document.querySelectorAll('ytd-thumbnail, #thumbnail, .ytd-thumbnail, a#thumbnail, ytd-rich-item-renderer .ytd-thumbnail');
                        
                        thumbnails.forEach(function(thumbnail) {
                            if (thumbnail.youtubeEnhancerProcessed) return;
                            thumbnail.youtubeEnhancerProcessed = true;
                            
                            // Remove mouseover and mouseenter events
                            const newThumbnail = thumbnail.cloneNode(true);
                            if (thumbnail.parentNode) {
                                thumbnail.parentNode.replaceChild(newThumbnail, thumbnail);
                            }
                            
                            // Disable hover preview for this thumbnail
                            newThumbnail.addEventListener('mouseover', function(e) {
                                e.stopImmediatePropagation();
                            }, true);
                            
                            newThumbnail.addEventListener('mouseenter', function(e) {
                                e.stopImmediatePropagation();
                            }, true);
                        });
                        
                        // Remove preview videos that might already be playing
                        const previewVideos = document.querySelectorAll('video[id*=""preview""], video[class*=""preview""], .preview-video');
                        previewVideos.forEach(function(video) {
                            if (video.parentNode) {
                                video.parentNode.removeChild(video);
                            }
                        });
                        
                        // Disable preview functionality in YouTube's internal code
                        if (window.yt && window.yt.config_) {
                            if (window.yt.config_.EXPERIMENT_FLAGS) {
                                window.yt.config_.EXPERIMENT_FLAGS.html5_disable_video_preview_on_hover = true;
                                window.yt.config_.EXPERIMENT_FLAGS.kevlar_hover_previews = false;
                                window.yt.config_.EXPERIMENT_FLAGS.web_player_preview_on_hover = false;
                            }
                        }
                    }
                    
                    // Apply immediately
                    disableAutoPreview();
                    
                    // Monitor for new content (dynamic loading)
                    const observer = new MutationObserver(function(mutations) {
                        let shouldProcess = false;
                        mutations.forEach(function(mutation) {
                            if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                                shouldProcess = true;
                            }
                        });
                        
                        if (shouldProcess) {
                            setTimeout(disableAutoPreview, 100);
                        }
                    });
                    
                    observer.observe(document.body, {
                        childList: true,
                        subtree: true
                    });
                    
                    // Store observer reference for cleanup
                    window.youtubeEnhancerObserver = observer;
                    
                    console.log('[YouTubeEnhancer] Auto preview disabled successfully');
                })();
            ";

            await webView.ExecuteScriptAsync(script);
            autoPreviewDisabled = true;
            AppLogger.Instance.LogInfo("YouTubeEnhancer", "Auto preview disabled successfully");
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("YouTubeEnhancer", ex, "Error disabling auto preview");
        }
    }

    private async Task EnableAutoPreview()
    {
        if (webView == null || !autoPreviewDisabled) return;

        try
        {
            string script = @"
                (function() {
                    console.log('[YouTubeEnhancer] Re-enabling auto preview...');
                    
                    // Clean up observer
                    if (window.youtubeEnhancerObserver) {
                        window.youtubeEnhancerObserver.disconnect();
                        delete window.youtubeEnhancerObserver;
                    }
                    
                    // Reset flag
                    window.youtubeEnhancerApplied = false;
                    
                    // Reload the page to restore original functionality
                    window.location.reload();
                })();
            ";

            await webView.ExecuteScriptAsync(script);
            autoPreviewDisabled = false;
            AppLogger.Instance.LogInfo("YouTubeEnhancer", "Auto preview re-enabled - page will reload");
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("YouTubeEnhancer", ex, "Error enabling auto preview");
        }
    }

    public async Task UpdateSettings()
    {
        await ApplyEnhancements();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!disposed)
        {
            if (disposing)
            {
                try
                {
                    if (webView != null)
                    {
                        webView.NavigationCompleted -= OnNavigationCompleted;
                        webView.DOMContentLoaded -= OnDOMContentLoaded;
                    }
                }
                catch (Exception ex)
                {
                    AppLogger.Instance.LogError("YouTubeEnhancer", ex, "Error disposing YouTubeEnhancer");
                }
            }
            disposed = true;
        }
    }
}
