using Microsoft.Web.WebView2.Core;
using System.Text.Json;
using System.Text.RegularExpressions;
using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp;

/// <summary>
/// Safe ad blocker that preserves video player functionality
/// </summary>
public sealed class SafeAdBlocker : IDisposable
{
    private readonly HashSet<string> _blockedDomains;
    private readonly CompiledRegex[] _urlPatterns;
    private readonly ConcurrentDictionary<string, int> _blockedStats;
    private readonly SettingsCache _settingsCache;
    private CoreWebView2? _webView;
    private bool _disposed;

    // Compiled regex patterns for better performance
    private readonly struct CompiledRegex
    {
        public readonly Regex Pattern;
        public readonly string Description;

        public CompiledRegex(string pattern, string description)
        {
            Pattern = new Regex(pattern, RegexOptions.Compiled | RegexOptions.IgnoreCase | RegexOptions.CultureInvariant);
            Description = description;
        }
    }

    public SafeAdBlocker()
    {
        _settingsCache = SettingsCache.Instance;
        _blockedDomains = LoadBlockedDomains();
        _urlPatterns = LoadUrlPatterns();
        _blockedStats = new ConcurrentDictionary<string, int>();
    }

    public void Initialize(CoreWebView2 webView)
    {
        _webView = webView;
        
        if (AppSettings.BlockAds)
        {
            EnableAdBlocking();
        }
    }

    public void EnableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("SafeAdBlocker.EnableAdBlocking", () =>
        {
            // 1. Request filtering - block ad domains
            _webView.WebResourceRequested += OnWebResourceRequested;
            _webView.AddWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);

            // 2. DOM content loaded - inject safe scripts
            _webView.DOMContentLoaded += OnDOMContentLoaded;

            // 3. Navigation completed - apply safe blocking
            _webView.NavigationCompleted += OnNavigationCompleted;
        });
    }

    public void DisableAdBlocking()
    {
        if (_webView == null || _disposed) return;

        PerformanceMonitor.Instance.MeasureOperation("SafeAdBlocker.DisableAdBlocking", () =>
        {
            _webView.WebResourceRequested -= OnWebResourceRequested;
            _webView.DOMContentLoaded -= OnDOMContentLoaded;
            _webView.NavigationCompleted -= OnNavigationCompleted;
            _webView.RemoveWebResourceRequestedFilter("*", CoreWebView2WebResourceContext.All);
        });
    }

    private void OnWebResourceRequested(object? sender, CoreWebView2WebResourceRequestedEventArgs e)
    {
        if (_disposed) return;
        
        var uri = e.Request.Uri;
        
        // Block known ad domains and patterns - use cached settings for performance
        if (ShouldBlockRequest(uri))
        {
            e.Response = _webView?.Environment.CreateWebResourceResponse(
                null, 200, "OK", "");
            
            IncrementBlockedStat("Requests");
            
            // Log blocked request
            AppLogger.Instance.LogNetworkRequest(uri, true, "Ad domain/pattern blocked");
            
            // Use cached setting to avoid property access overhead
            if (_settingsCache.ShowAdBlockStats)
            {
                AppLogger.Instance.LogAdBlockingActivity("Request blocked", 1, uri);
            }
        }
    }

    private async void OnDOMContentLoaded(object? sender, CoreWebView2DOMContentLoadedEventArgs e)
    {
        if (_webView == null) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("SafeAdBlocker.OnDOMContentLoaded");
            try
            {
                // Inject safe CSS first for immediate visual effect
                await InjectSafeAdBlockingCSS();
                
                // Small delay to let CSS take effect before JS
                await Task.Delay(50);
                
                // Inject safe JavaScript for dynamic ad removal
                await InjectSafeAdBlockingJavaScript();
                
                AppLogger.Instance.LogInfo("SafeAdBlocker", "DOM Content Loaded - Safe ad blocking scripts injected");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("SafeAdBlocker.OnDOMContentLoaded");
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("SafeAdBlocker", ex, "Error injecting safe ad blocking code");
        }
    }

    private async void OnNavigationCompleted(object? sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (_webView == null || !e.IsSuccess) return;

        try
        {
            PerformanceMonitor.Instance.StartOperation("SafeAdBlocker.OnNavigationCompleted");
            try
            {
                AppLogger.Instance.LogInfo("SafeAdBlocker", $"Navigation completed to: {_webView.Source}");
                
                // Wait a bit for the page to stabilize before cleanup
                await Task.Delay(1000);
                
                // Safe cleanup after page load
                await SafeRemoveAdsAfterLoad();
                
                System.Diagnostics.Debug.WriteLine("[SafeAdBlocker] Post-navigation safe ad cleanup completed");
            }
            finally
            {
                PerformanceMonitor.Instance.EndOperation("SafeAdBlocker.OnNavigationCompleted");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"[SafeAdBlocker] Error in post-load ad removal: {ex.Message}");
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private bool ShouldBlockRequest(string uri)
    {
        try
        {
            // Fast string-based checks first (avoid URI parsing overhead)
            if (IsYouTubeAdRequest(uri))
                return true;

            // Check against URL patterns using compiled regex
            foreach (var pattern in _urlPatterns)
            {
                if (pattern.Pattern.IsMatch(uri))
                    return true;
            }

            // Domain check last (requires URI parsing)
            var uriObj = new Uri(uri);
            var host = uriObj.Host;

            // Use HashSet.Contains for O(1) lookup instead of LINQ
            return _blockedDomains.Contains(host) || 
                   _blockedDomains.Any(domain => host.Contains(domain, StringComparison.OrdinalIgnoreCase));
        }
        catch
        {
            return false;
        }
    }

    private bool IsYouTubeAdRequest(string uri)
    {
        var youtubeAdPatterns = new[]
        {
            // Core ad serving
            "/pagead/",
            "/ptracking",
            "googleads",
            "googlesyndication",
            "googletagservices",
            "/ads?",
            "/ad?",
            "doubleclick.net",
            "/gen_204?",
            "/youtubei/v1/log_event",
            "/api/stats/",
            "video_ads",
            "/adview",
            "/ad_companion",
            "/get_midroll_info"
        };

        return youtubeAdPatterns.Any(pattern => 
            Regex.IsMatch(uri, pattern, RegexOptions.IgnoreCase));
    }

    private async Task InjectSafeAdBlockingCSS()
    {
        if (_webView == null) return;

        var css = GetSafeAdBlockingCSS();
        var script = $@"
            (function() {{
                var style = document.createElement('style');
                style.textContent = `{css}`;
                document.head.appendChild(style);
            }})();
        ";

        await _webView.AddScriptToExecuteOnDocumentCreatedAsync(script);
    }

    private async Task InjectSafeAdBlockingJavaScript()
    {
        if (_webView == null) return;

        var script = GetSafeAdBlockingJavaScript();
        await _webView.ExecuteScriptAsync(script);
    }

    private async Task SafeRemoveAdsAfterLoad()
    {
        if (_webView == null) return;

        var script = @"
            (function() {
                console.log('[SafeAdBlocker] Starting SAFE ad removal...');
                let removedCount = 0;
                
                // CRITICAL: Preserve video player functionality first
                function ensurePlayerFunctionality() {
                    // Ensure video element is clickable
                    const video = document.querySelector('video.html5-main-video');
                    if (video) {
                        video.style.pointerEvents = 'auto';
                        video.style.cursor = 'pointer';
                    }
                    
                    // Ensure player controls are functional
                    const controls = document.querySelector('.ytp-chrome-bottom');
                    if (controls) {
                        controls.style.pointerEvents = 'auto';
                        controls.style.zIndex = '9999';
                    }
                    
                    // Ensure play buttons work
                    const playButtons = document.querySelectorAll('.ytp-play-button, .ytp-large-play-button');
                    playButtons.forEach(btn => {
                        btn.style.pointerEvents = 'auto';
                        btn.style.cursor = 'pointer';
                    });
                }
                
                // Always ensure player functionality first
                ensurePlayerFunctionality();
                
                // Safe ad removal - only target specific ad elements
                const safeAdSelectors = [
                    // Only target obvious ad containers (not player elements)
                    '.ytd-promoted-sparkles-web-renderer',
                    '.ytd-promoted-video-renderer', 
                    '.ytd-banner-promo-renderer',
                    '.ytd-in-feed-ad-layout-renderer',
                    '.ytd-ad-slot-renderer',
                    '.ytd-display-ad-renderer',
                    
                    // Sidebar ads only
                    '#player-ads',
                    '#masthead-ad', 
                    '#watch-sidebar-ads',
                    '.ytd-companion-slot-renderer',
                    
                    // Data attributes for ads (but exclude video and player elements)
                    '[data-ad-status]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""])',
                    '[data-is-ad=""true""]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""])',
                    '[data-promoted=""true""]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""])'
                ];
                
                // Remove ads safely
                safeAdSelectors.forEach(selector => {
                    try {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(el => {
                            // Triple check we're not removing player elements
                            if (!el.closest('.html5-video-player') && 
                                !el.closest('.ytp-chrome-bottom') &&
                                !el.closest('.ytp-chrome-controls') &&
                                !el.classList.contains('ytp-play-button') &&
                                !el.classList.contains('ytp-large-play-button') &&
                                !el.classList.contains('html5-main-video') &&
                                el.tagName !== 'VIDEO') {
                                el.remove();
                                removedCount++;
                            }
                        });
                    } catch (e) {
                        console.warn('[SafeAdBlocker] Safe removal error:', e);
                    }
                });
                
                // Auto-skip ads safely
                setTimeout(() => {
                    const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                    if (skipButton && skipButton.offsetParent !== null && skipButton.style.display !== 'none') {
                        console.log('[SafeAdBlocker] Auto-clicking skip button');
                        skipButton.click();
                    }
                }, 1000);
                
                // Re-ensure player functionality after ad removal
                ensurePlayerFunctionality();
                
                console.log('[SafeAdBlocker] Safe ad removal completed. Removed:', removedCount, 'Player functionality preserved.');
                return removedCount;
            })();
        ";

        try
        {
            var result = await _webView.ExecuteScriptAsync(script);
            if (int.TryParse(result, out int blockedCount) && blockedCount > 0)
            {
                IncrementBlockedStat("Elements", blockedCount);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error executing safe ad removal script: {ex.Message}");
        }
    }

    private string GetSafeAdBlockingCSS()
    {
        return @"
            /* SAFE Ad Blocking CSS - Only targets obvious ads, preserves player functionality */
            
            /* Banner and Display Ads - Safe targeting */
            .ytd-promoted-sparkles-web-renderer,
            .ytd-promoted-video-renderer,
            .ytd-banner-promo-renderer,
            .ytd-in-feed-ad-layout-renderer,
            .ytd-ad-slot-renderer,
            .ytd-display-ad-renderer,
            .ytd-promoted-sparkles-text-search-renderer,
            .ytd-search-promoted-sparkles-web-renderer,
            .ytd-compact-promoted-video-renderer,
            .ytd-search-promoted-item-renderer,
            .ytd-carousel-ad-renderer,
            .ytd-video-masthead-ad-renderer {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Sidebar and Companion Ads - Safe targeting */
            #player-ads,
            #masthead-ad,
            #watch-sidebar-ads,
            #watch-channel-brand-div,
            .ytd-companion-slot-renderer,
            .ytd-action-companion-ad-renderer {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
                height: 0 !important;
                width: 0 !important;
            }
            
            /* Data attributes for ads - but EXCLUDE video and player elements */
            [data-ad-status]:not(video):not(.html5-video-player):not([class*='ytp-']):not([id*='ytp-']),
            [data-is-ad='true']:not(video):not(.html5-video-player):not([class*='ytp-']):not([id*='ytp-']),
            [data-promoted='true']:not(video):not(.html5-video-player):not([class*='ytp-']):not([id*='ytp-']) {
                display: none !important;
                visibility: hidden !important;
                opacity: 0 !important;
            }
            
            /* CRITICAL: Ensure video player elements remain functional */
            video.html5-main-video,
            .html5-video-player,
            .ytp-chrome-bottom,
            .ytp-chrome-controls,
            .ytp-play-button,
            .ytp-large-play-button,
            .ytp-progress-bar,
            .ytp-volume-slider,
            .ytp-time-display,
            .ytp-fullscreen-button,
            .ytp-settings-button {
                display: block !important;
                visibility: visible !important;
                opacity: 1 !important;
                pointer-events: auto !important;
                cursor: pointer !important;
                z-index: 9999 !important;
            }
        ";
    }

    private string GetSafeAdBlockingJavaScript()
    {
        return @"
            (function() {
                'use strict';
                console.log('[SafeAdBlocker] Initializing SAFE ad blocking JavaScript...');
                
                // CRITICAL: Preserve video player functionality
                function ensurePlayerFunctionality() {
                    // Ensure video element is clickable
                    const video = document.querySelector('video.html5-main-video');
                    if (video) {
                        video.style.pointerEvents = 'auto';
                        video.style.cursor = 'pointer';
                        
                        // Ensure video can receive clicks
                        if (!video.hasAttribute('data-safe-adblock-processed')) {
                            video.setAttribute('data-safe-adblock-processed', 'true');
                            
                            // Preserve click functionality
                            video.addEventListener('click', function(e) {
                                console.log('[SafeAdBlocker] Video click preserved');
                                // Allow normal video behavior
                            }, false);
                        }
                    }
                    
                    // Ensure player controls are functional
                    const controls = document.querySelector('.ytp-chrome-bottom');
                    if (controls) {
                        controls.style.pointerEvents = 'auto';
                        controls.style.zIndex = '9999';
                    }
                    
                    // Ensure play button works
                    const playButton = document.querySelector('.ytp-play-button');
                    if (playButton) {
                        playButton.style.pointerEvents = 'auto';
                        playButton.style.cursor = 'pointer';
                    }
                    
                    // Ensure large play button works
                    const largePlayButton = document.querySelector('.ytp-large-play-button');
                    if (largePlayButton) {
                        largePlayButton.style.pointerEvents = 'auto';
                        largePlayButton.style.cursor = 'pointer';
                    }
                }
                
                // Safe ad removal - only target specific ad elements
                function safeAdRemoval() {
                    const safeAdSelectors = [
                        // Only target obvious ad containers
                        '.ytd-promoted-sparkles-web-renderer',
                        '.ytd-promoted-video-renderer', 
                        '.ytd-banner-promo-renderer',
                        '.ytd-in-feed-ad-layout-renderer',
                        '.ytd-ad-slot-renderer',
                        '.ytd-display-ad-renderer',
                        
                        // Sidebar ads only
                        '#player-ads',
                        '#masthead-ad', 
                        '#watch-sidebar-ads',
                        '.ytd-companion-slot-renderer',
                        
                        // Data attributes for ads (but not video elements)
                        '[data-ad-status]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""])',
                        '[data-is-ad=""true""]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""])',
                        '[data-promoted=""true""]:not(video):not(.html5-video-player):not([class*=""ytp-""]):not([id*=""ytp-""])'
                    ];
                    
                    let removedCount = 0;
                    safeAdSelectors.forEach(selector => {
                        try {
                            const elements = document.querySelectorAll(selector);
                            elements.forEach(el => {
                                // Triple check we're not removing player elements
                                if (!el.closest('.html5-video-player') && 
                                    !el.closest('.ytp-chrome-bottom') &&
                                    !el.closest('.ytp-chrome-controls') &&
                                    !el.classList.contains('ytp-play-button') &&
                                    !el.classList.contains('ytp-large-play-button') &&
                                    !el.classList.contains('html5-main-video') &&
                                    el.tagName !== 'VIDEO') {
                                    el.remove();
                                    removedCount++;
                                }
                            });
                        } catch (e) {
                            console.warn('[SafeAdBlocker] Safe removal error:', e);
                        }
                    });
                    
                    if (removedCount > 0) {
                        console.log('[SafeAdBlocker] Safely removed', removedCount, 'ad elements');
                    }
                    
                    return removedCount;
                }
                
                // Auto-skip ads safely
                function safeAdSkip() {
                    const skipButton = document.querySelector('.ytp-ad-skip-button, .ytp-skip-ad-button');
                    if (skipButton && skipButton.offsetParent !== null && skipButton.style.display !== 'none') {
                        console.log('[SafeAdBlocker] Auto-clicking skip button');
                        skipButton.click();
                        return true;
                    }
                    return false;
                }
                
                // Minimal YouTube API override - only disable ads, preserve functionality
                function minimalAPIOverride() {
                    try {
                        if (window.yt?.config_?.EXPERIMENT_FLAGS) {
                            const flags = window.yt.config_.EXPERIMENT_FLAGS;
                            // Only set essential ad-blocking flags
                            flags.html5_disable_preroll_ads = true;
                            flags.html5_disable_midroll_ads = true;
                            flags.html5_disable_postroll_ads = true;
                            flags.html5_disable_overlay_ads = true;
                            flags.html5_disable_companion_ads = true;
                        }
                    } catch (error) {
                        console.warn('[SafeAdBlocker] Minimal API override error:', error);
                    }
                }
                
                // Throttled execution to avoid performance issues
                let lastExecution = 0;
                const THROTTLE_DELAY = 1000; // 1 second
                
                function throttledExecution() {
                    const now = Date.now();
                    if (now - lastExecution < THROTTLE_DELAY) return;
                    lastExecution = now;
                    
                    // Always ensure player functionality first
                    ensurePlayerFunctionality();
                    
                    // Then safely remove ads
                    safeAdRemoval();
                    
                    // Try to skip ads
                    safeAdSkip();
                }
                
                // Minimal mutation observer - only watch for new content
                const observer = new MutationObserver(function(mutations) {
                    let shouldProcess = false;
                    for (const mutation of mutations) {
                        if (mutation.addedNodes.length > 0) {
                            shouldProcess = true;
                            break;
                        }
                    }
                    
                    if (shouldProcess) {
                        setTimeout(throttledExecution, 100);
                    }
                });
                
                // Start observing with minimal config
                observer.observe(document.body, {
                    childList: true,
                    subtree: true,
                    attributes: false,
                    characterData: false
                });
                
                // Initialize
                minimalAPIOverride();
                
                // Initial execution
                setTimeout(() => {
                    ensurePlayerFunctionality();
                    safeAdRemoval();
                }, 500);
                
                // Periodic skip check (less frequent)
                setInterval(safeAdSkip, 2000);
                
                // Re-ensure player functionality periodically
                setInterval(ensurePlayerFunctionality, 5000);
                
                console.log('[SafeAdBlocker] SAFE ad blocker initialized - player functionality preserved');
            })();
        ";
    }

    private HashSet<string> LoadBlockedDomains()
    {
        return new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "googleads.g.doubleclick.net",
            "googlesyndication.com",
            "googletagservices.com",
            "doubleclick.net",
            "googleadservices.com",
            "google-analytics.com",
            "googletagmanager.com"
        };
    }

    private CompiledRegex[] LoadUrlPatterns()
    {
        return new[]
        {
            new CompiledRegex(@"/pagead/", "Google PageAd"),
            new CompiledRegex(@"/ads\?", "Generic ads parameter"),
            new CompiledRegex(@"googleads", "Google Ads"),
            new CompiledRegex(@"googlesyndication", "Google Syndication"),
            new CompiledRegex(@"doubleclick", "DoubleClick"),
            new CompiledRegex(@"/ptracking", "YouTube tracking"),
            new CompiledRegex(@"/api/stats/", "YouTube stats")
        };
    }

    private void IncrementBlockedStat(string category, int count = 1)
    {
        _blockedStats.AddOrUpdate(category, count, (key, oldValue) => oldValue + count);
    }

    public int GetTotalBlocked()
    {
        return _blockedStats.Values.Sum();
    }

    public Dictionary<string, int> GetBlockedStats()
    {
        return new Dictionary<string, int>(_blockedStats);
    }

    public void ClearStats()
    {
        _blockedStats.Clear();
    }

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    private void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                try
                {
                    if (_webView != null)
                    {
                        _webView.WebResourceRequested -= OnWebResourceRequested;
                        _webView.DOMContentLoaded -= OnDOMContentLoaded;
                        _webView.NavigationCompleted -= OnNavigationCompleted;
                    }
                }
                catch (Exception ex)
                {
                    AppLogger.Instance.LogError("SafeAdBlocker", ex, "Error disposing SafeAdBlocker");
                }
            }
            _disposed = true;
        }
    }
}