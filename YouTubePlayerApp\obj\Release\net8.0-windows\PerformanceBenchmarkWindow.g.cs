﻿#pragma checksum "..\..\..\PerformanceBenchmarkWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "24D91A2A54820001F3A3FC6399FBD3E84D28E1DF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;
using YouTubePlayerApp;


namespace YouTubePlayerApp {
    
    
    /// <summary>
    /// PerformanceBenchmarkWindow
    /// </summary>
    public partial class PerformanceBenchmarkWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 47 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentMemoryText;
        
        #line default
        #line hidden
        
        
        #line 51 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PeakMemoryText;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock WorkingSetText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PrivateBytesText;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ForceGCButton;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CpuUsageText;
        
        #line default
        #line hidden
        
        
        #line 89 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UptimeText;
        
        #line default
        #line hidden
        
        
        #line 93 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Gen0GCText;
        
        #line default
        #line hidden
        
        
        #line 97 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Gen1GCText;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock Gen2GCText;
        
        #line default
        #line hidden
        
        
        #line 114 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BenchmarkAdBlockerButton;
        
        #line default
        #line hidden
        
        
        #line 117 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AdBlockerBenchmarkResult;
        
        #line default
        #line hidden
        
        
        #line 124 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BenchmarkQualityControllerButton;
        
        #line default
        #line hidden
        
        
        #line 127 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock QualityControllerBenchmarkResult;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BenchmarkSettingsCacheButton;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SettingsCacheBenchmarkResult;
        
        #line default
        #line hidden
        
        
        #line 147 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 149 "..\..\..\PerformanceBenchmarkWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CloseButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/YouTubePlayerApp;component/performancebenchmarkwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\PerformanceBenchmarkWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "8.0.18.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CurrentMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.PeakMemoryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.WorkingSetText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.PrivateBytesText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.ForceGCButton = ((System.Windows.Controls.Button)(target));
            
            #line 64 "..\..\..\PerformanceBenchmarkWindow.xaml"
            this.ForceGCButton.Click += new System.Windows.RoutedEventHandler(this.ForceGCButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CpuUsageText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.UptimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.Gen0GCText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.Gen1GCText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 10:
            this.Gen2GCText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.BenchmarkAdBlockerButton = ((System.Windows.Controls.Button)(target));
            
            #line 116 "..\..\..\PerformanceBenchmarkWindow.xaml"
            this.BenchmarkAdBlockerButton.Click += new System.Windows.RoutedEventHandler(this.BenchmarkAdBlockerButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.AdBlockerBenchmarkResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.BenchmarkQualityControllerButton = ((System.Windows.Controls.Button)(target));
            
            #line 126 "..\..\..\PerformanceBenchmarkWindow.xaml"
            this.BenchmarkQualityControllerButton.Click += new System.Windows.RoutedEventHandler(this.BenchmarkQualityControllerButton_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.QualityControllerBenchmarkResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.BenchmarkSettingsCacheButton = ((System.Windows.Controls.Button)(target));
            
            #line 136 "..\..\..\PerformanceBenchmarkWindow.xaml"
            this.BenchmarkSettingsCacheButton.Click += new System.Windows.RoutedEventHandler(this.BenchmarkSettingsCacheButton_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SettingsCacheBenchmarkResult = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 17:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\PerformanceBenchmarkWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.RefreshButton_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.CloseButton = ((System.Windows.Controls.Button)(target));
            
            #line 150 "..\..\..\PerformanceBenchmarkWindow.xaml"
            this.CloseButton.Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

