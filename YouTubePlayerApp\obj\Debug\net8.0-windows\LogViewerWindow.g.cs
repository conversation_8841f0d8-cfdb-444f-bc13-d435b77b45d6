﻿#pragma checksum "..\..\..\LogViewerWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "4920EA37A2E841E1D78592049A8D921CABCF9EAF"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace YouTubePlayerApp {
    
    
    /// <summary>
    /// LogViewerWindow
    /// </summary>
    public partial class LogViewerWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 30 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox LogLevelComboBox;
        
        #line default
        #line hidden
        
        
        #line 41 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ComponentComboBox;
        
        #line default
        #line hidden
        
        
        #line 47 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoRefreshCheckBox;
        
        #line default
        #line hidden
        
        
        #line 52 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshButton;
        
        #line default
        #line hidden
        
        
        #line 53 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearButton;
        
        #line default
        #line hidden
        
        
        #line 54 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ExportButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogCountText;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox ScrollToBottomCheckBox;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid LogsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel LogDetailsPanel;
        
        #line default
        #line hidden
        
        
        #line 132 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorSummaryText;
        
        #line default
        #line hidden
        
        
        #line 133 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ErrorDetailsText;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid ErrorLogsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 151 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.DataGrid PerformanceLogsDataGrid;
        
        #line default
        #line hidden
        
        
        #line 170 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CopyAllButton;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveToFileButton;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox SearchTextBox;
        
        #line default
        #line hidden
        
        
        #line 176 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox RawLogTextBox;
        
        #line default
        #line hidden
        
        
        #line 192 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse AutoRefreshIndicator;
        
        #line default
        #line hidden
        
        
        #line 196 "..\..\..\LogViewerWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LastUpdateText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/YouTubePlayerApp;component/logviewerwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\LogViewerWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "********")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LogLevelComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 30 "..\..\..\LogViewerWindow.xaml"
            this.LogLevelComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LogLevel_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ComponentComboBox = ((System.Windows.Controls.ComboBox)(target));
            
            #line 41 "..\..\..\LogViewerWindow.xaml"
            this.ComponentComboBox.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.Component_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 3:
            this.AutoRefreshCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 48 "..\..\..\LogViewerWindow.xaml"
            this.AutoRefreshCheckBox.Checked += new System.Windows.RoutedEventHandler(this.AutoRefresh_Changed);
            
            #line default
            #line hidden
            
            #line 48 "..\..\..\LogViewerWindow.xaml"
            this.AutoRefreshCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.AutoRefresh_Changed);
            
            #line default
            #line hidden
            return;
            case 4:
            this.RefreshButton = ((System.Windows.Controls.Button)(target));
            
            #line 52 "..\..\..\LogViewerWindow.xaml"
            this.RefreshButton.Click += new System.Windows.RoutedEventHandler(this.Refresh_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.ClearButton = ((System.Windows.Controls.Button)(target));
            
            #line 53 "..\..\..\LogViewerWindow.xaml"
            this.ClearButton.Click += new System.Windows.RoutedEventHandler(this.Clear_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.ExportButton = ((System.Windows.Controls.Button)(target));
            
            #line 54 "..\..\..\LogViewerWindow.xaml"
            this.ExportButton.Click += new System.Windows.RoutedEventHandler(this.Export_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.LogCountText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.ScrollToBottomCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 9:
            this.LogsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            
            #line 78 "..\..\..\LogViewerWindow.xaml"
            this.LogsDataGrid.SelectionChanged += new System.Windows.Controls.SelectionChangedEventHandler(this.LogsDataGrid_SelectionChanged);
            
            #line default
            #line hidden
            return;
            case 10:
            this.LogDetailsPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 11:
            this.ErrorSummaryText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.ErrorDetailsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 13:
            this.ErrorLogsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 14:
            this.PerformanceLogsDataGrid = ((System.Windows.Controls.DataGrid)(target));
            return;
            case 15:
            this.CopyAllButton = ((System.Windows.Controls.Button)(target));
            
            #line 170 "..\..\..\LogViewerWindow.xaml"
            this.CopyAllButton.Click += new System.Windows.RoutedEventHandler(this.CopyAll_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.SaveToFileButton = ((System.Windows.Controls.Button)(target));
            
            #line 171 "..\..\..\LogViewerWindow.xaml"
            this.SaveToFileButton.Click += new System.Windows.RoutedEventHandler(this.SaveToFile_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.SearchTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 173 "..\..\..\LogViewerWindow.xaml"
            this.SearchTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.Search_TextChanged);
            
            #line default
            #line hidden
            return;
            case 18:
            this.RawLogTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 19:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.AutoRefreshIndicator = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 21:
            this.LastUpdateText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

