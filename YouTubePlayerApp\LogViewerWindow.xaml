<Window x:Class="YouTubePlayerApp.LogViewerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Log Viewer - YouTube Player" 
        Height="600" Width="1000"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Control Panel -->
        <Border Grid.Row="0" Background="#F0F0F0" Padding="10" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" Margin="0,0,20,0">
                    <TextBlock Text="Log Level:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox Name="LogLevelComboBox" Width="100" SelectionChanged="LogLevel_SelectionChanged">
                        <ComboBoxItem Content="Trace" Tag="0"/>
                        <ComboBoxItem Content="Debug" Tag="1"/>
                        <ComboBoxItem Content="Info" Tag="2" IsSelected="True"/>
                        <ComboBoxItem Content="Warning" Tag="3"/>
                        <ComboBoxItem Content="Error" Tag="4"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" Margin="0,0,20,0">
                    <TextBlock Text="Component:" VerticalAlignment="Center" Margin="0,0,5,0"/>
                    <ComboBox Name="ComponentComboBox" Width="120" SelectionChanged="Component_SelectionChanged">
                        <ComboBoxItem Content="All" IsSelected="True"/>
                    </ComboBox>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal" Margin="0,0,20,0">
                    <CheckBox Name="AutoRefreshCheckBox" Content="Auto Refresh" IsChecked="True" 
                              VerticalAlignment="Center" Checked="AutoRefresh_Changed" Unchecked="AutoRefresh_Changed"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" Orientation="Horizontal" Margin="0,0,20,0">
                    <Button Name="RefreshButton" Content="Refresh" Width="80" Height="25" Click="Refresh_Click"/>
                    <Button Name="ClearButton" Content="Clear" Width="80" Height="25" Margin="5,0,0,0" Click="Clear_Click"/>
                    <Button Name="ExportButton" Content="Export" Width="80" Height="25" Margin="5,0,0,0" Click="Export_Click"/>
                </StackPanel>
                
                <StackPanel Grid.Column="5" Orientation="Horizontal">
                    <TextBlock Name="LogCountText" Text="0 logs" VerticalAlignment="Center" Margin="0,0,10,0"/>
                    <CheckBox Name="ScrollToBottomCheckBox" Content="Auto Scroll" IsChecked="True" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Log Display -->
        <TabControl Grid.Row="1">
            <TabItem Header="Live Logs">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="5"/>
                        <RowDefinition Height="150"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Main Log List -->
                    <DataGrid Grid.Row="0" Name="LogsDataGrid" AutoGenerateColumns="False" 
                              IsReadOnly="True" AlternatingRowBackground="#F8F8F8"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column"
                              SelectionChanged="LogsDataGrid_SelectionChanged">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Time" Binding="{Binding Timestamp, StringFormat='HH:mm:ss.fff'}" Width="80"/>
                            <DataGridTextColumn Header="Level" Binding="{Binding Level}" Width="70"/>
                            <DataGridTextColumn Header="Component" Binding="{Binding Component}" Width="120"/>
                            <DataGridTextColumn Header="Thread" Binding="{Binding ThreadId}" Width="50"/>
                            <DataGridTextColumn Header="Message" Binding="{Binding Message}" Width="*"/>
                        </DataGrid.Columns>
                        <DataGrid.RowStyle>
                            <Style TargetType="DataGridRow">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Level}" Value="Error">
                                        <Setter Property="Background" Value="#FFE5E5"/>
                                        <Setter Property="Foreground" Value="#800000"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Level}" Value="Warning">
                                        <Setter Property="Background" Value="#FFF8E1"/>
                                        <Setter Property="Foreground" Value="#B8860B"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Level}" Value="Debug">
                                        <Setter Property="Foreground" Value="#808080"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Level}" Value="Trace">
                                        <Setter Property="Foreground" Value="#A0A0A0"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </DataGrid.RowStyle>
                    </DataGrid>
                    
                    <GridSplitter Grid.Row="1" HorizontalAlignment="Stretch" Background="#CCCCCC"/>
                    
                    <!-- Log Details -->
                    <GroupBox Grid.Row="2" Header="Log Details" Padding="5">
                        <ScrollViewer VerticalScrollBarVisibility="Auto">
                            <StackPanel Name="LogDetailsPanel">
                                <TextBlock Text="Select a log entry to view details" 
                                         HorizontalAlignment="Center" VerticalAlignment="Center" 
                                         Foreground="Gray" FontStyle="Italic"/>
                            </StackPanel>
                        </ScrollViewer>
                    </GroupBox>
                </Grid>
            </TabItem>
            
            <TabItem Header="Error Summary">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <Border Grid.Row="0" Background="#FFE5E5" Padding="10" Margin="5">
                        <StackPanel>
                            <TextBlock Name="ErrorSummaryText" Text="No errors recorded" FontWeight="Bold"/>
                            <TextBlock Name="ErrorDetailsText" Text="" Margin="0,5,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <DataGrid Grid.Row="1" Name="ErrorLogsDataGrid" AutoGenerateColumns="False" 
                              IsReadOnly="True" Margin="5" AlternatingRowBackground="#FFF8F8"
                              GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                        <DataGrid.Columns>
                            <DataGridTextColumn Header="Time" Binding="{Binding Timestamp, StringFormat='MM-dd HH:mm:ss'}" Width="120"/>
                            <DataGridTextColumn Header="Level" Binding="{Binding Level}" Width="70"/>
                            <DataGridTextColumn Header="Component" Binding="{Binding Component}" Width="120"/>
                            <DataGridTextColumn Header="Message" Binding="{Binding Message}" Width="*"/>
                        </DataGrid.Columns>
                    </DataGrid>
                </Grid>
            </TabItem>
            
            <TabItem Header="Performance Logs">
                <DataGrid Name="PerformanceLogsDataGrid" AutoGenerateColumns="False" 
                          IsReadOnly="True" AlternatingRowBackground="#F0F8FF"
                          GridLinesVisibility="Horizontal" HeadersVisibility="Column">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Time" Binding="{Binding Timestamp, StringFormat='HH:mm:ss'}" Width="80"/>
                        <DataGridTextColumn Header="Component" Binding="{Binding Component}" Width="150"/>
                        <DataGridTextColumn Header="Operation" Binding="{Binding Message}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
            </TabItem>
            
            <TabItem Header="Raw Log Text">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="5">
                        <Button Name="CopyAllButton" Content="Copy All" Width="80" Height="25" Click="CopyAll_Click"/>
                        <Button Name="SaveToFileButton" Content="Save to File" Width="100" Height="25" Margin="5,0,0,0" Click="SaveToFile_Click"/>
                        <TextBlock Text="Search:" VerticalAlignment="Center" Margin="20,0,5,0"/>
                        <TextBox Name="SearchTextBox" Width="200" Height="25" TextChanged="Search_TextChanged"/>
                    </StackPanel>
                    
                    <TextBox Grid.Row="1" Name="RawLogTextBox" IsReadOnly="True" 
                             VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto"
                             FontFamily="Consolas" FontSize="10" Margin="5"
                             Background="#F8F8F8"/>
                </Grid>
            </TabItem>
        </TabControl>
        
        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="5" BorderBrush="#CCCCCC" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Auto Refresh: " VerticalAlignment="Center"/>
                    <Ellipse Name="AutoRefreshIndicator" Width="8" Height="8" Fill="Green" Margin="2,0,10,0"/>
                    <TextBlock Name="LastUpdateText" Text="Never" VerticalAlignment="Center" FontSize="10"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
