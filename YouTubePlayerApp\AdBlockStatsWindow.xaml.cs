using System.Windows;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for AdBlockStatsWindow.xaml
/// </summary>
public partial class AdBlockStatsWindow : Window
{
    public AdBlockStatsWindow()
    {
        InitializeComponent();
        LoadStatistics();
    }

    private void LoadStatistics()
    {
        try
        {
            // Get the main window to access the video-safe ad blocker
            var mainWindow = Application.Current.MainWindow as MainWindow;
            var adBlocker = mainWindow?.GetVideoSafeAdBlocker();
            
            if (adBlocker != null)
            {
                var stats = adBlocker.GetBlockedStats();
                
                TotalBlockedText.Text = adBlocker.GetTotalBlocked().ToString();
                VideoAdsBlockedText.Text = stats.GetValueOrDefault("VideoAds", 0).ToString();
                ElementsBlockedText.Text = stats.GetValueOrDefault("Elements", 0).ToString();
                TrackingBlockedText.Text = stats.GetValueOrDefault("Requests", 0).ToString();
                
                // Estimate data saved (rough calculation: 50KB per blocked request)
                var totalBlocked = adBlocker.GetTotalBlocked();
                var estimatedKB = totalBlocked * 50;
                if (estimatedKB > 1024)
                {
                    DataSavedText.Text = $"~{estimatedKB / 1024:F1} MB";
                }
                else
                {
                    DataSavedText.Text = $"~{estimatedKB} KB";
                }
            }
            else
            {
                TotalBlockedText.Text = "N/A";
                VideoAdsBlockedText.Text = "N/A";
                ElementsBlockedText.Text = "N/A";
                TrackingBlockedText.Text = "N/A";
                DataSavedText.Text = "N/A";
            }
            
            // Set session start time
            SessionStartText.Text = DateTime.Now.ToString("HH:mm:ss");
            
            // Set ad blocker status
            if (AppSettings.BlockAds)
            {
                AdBlockerStatusText.Text = "Active";
                AdBlockerStatusText.Foreground = System.Windows.Media.Brushes.Green;
            }
            else
            {
                AdBlockerStatusText.Text = "Disabled";
                AdBlockerStatusText.Foreground = System.Windows.Media.Brushes.Red;
            }
            
            // Load recent blocked URLs (placeholder)
            LoadRecentBlockedUrls();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error loading ad block statistics: {ex.Message}");
        }
    }

    private void LoadRecentBlockedUrls()
    {
        // Placeholder for recently blocked URLs
        var sampleBlockedUrls = new List<string>
        {
            "googleads.g.doubleclick.net/pagead/ads",
            "googlesyndication.com/safeframe/",
            "youtube.com/pagead/conversion/",
            "googletagservices.com/tag/js/gpt.js",
            "doubleclick.net/instream/ad_status.js"
        };
        
        BlockedUrlsList.ItemsSource = sampleBlockedUrls;
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        LoadStatistics();
    }

    private void ClearStatsButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show(
            "Clear all ad blocking statistics?",
            "Clear Statistics",
            MessageBoxButton.YesNo,
            MessageBoxImage.Question);
            
        if (result == MessageBoxResult.Yes)
        {
            var mainWindow = Application.Current.MainWindow as MainWindow;
            var adBlocker = mainWindow?.GetVideoSafeAdBlocker();
            adBlocker?.ClearStats();
            
            LoadStatistics();
            
            MessageBox.Show("Statistics cleared successfully.", "Clear Complete", 
                          MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }
}
