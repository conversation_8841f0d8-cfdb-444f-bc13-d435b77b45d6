using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;

namespace YouTubePlayerApp.Core;

/// <summary>
/// مساعد مشترك لإنشاء وإدارة النوافذ لتجنب التكرار في الكود
/// </summary>
public static class WindowHelper
{
    /// <summary>
    /// إنشاء نافذة تشخيصية موحدة مع تخصيص كامل
    /// </summary>
    /// <param name="title">عنوان النافذة</param>
    /// <param name="content">المحتوى النصي</param>
    /// <param name="owner">النافذة الأب</param>
    /// <param name="width">عرض النافذة</param>
    /// <param name="height">ارتفاع النافذة</param>
    /// <param name="fontSize">حجم الخط</param>
    /// <param name="fontFamily">عائلة الخط</param>
    /// <param name="isResizable">قابلية تغيير الحجم</param>
    /// <returns>النافذة المُنشأة</returns>
    public static Window CreateDiagnosticsWindow(
        string title, 
        string content, 
        Window? owner = null,
        double width = 900, 
        double height = 700,
        double fontSize = 11,
        string fontFamily = "Consolas",
        bool isResizable = true)
    {
        var window = new Window
        {
            Title = title,
            Width = width,
            Height = height,
            Owner = owner,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            ResizeMode = isResizable ? ResizeMode.CanResize : ResizeMode.NoResize,
            ShowInTaskbar = false,
            Icon = owner?.Icon // استخدام أيقونة النافذة الأب
        };

        // إنشاء ScrollViewer للمحتوى
        var scrollViewer = new ScrollViewer
        {
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
            Padding = new Thickness(15),
            Background = new SolidColorBrush(Color.FromRgb(248, 249, 250)) // خلفية فاتحة
        };

        // إنشاء TextBlock للنص
        var textBlock = new TextBlock
        {
            Text = content,
            FontFamily = new FontFamily(fontFamily),
            FontSize = fontSize,
            TextWrapping = TextWrapping.Wrap,
            Foreground = new SolidColorBrush(Color.FromRgb(33, 37, 41)), // نص داكن
            LineHeight = fontSize * 1.4, // تباعد أسطر مريح
            Margin = new Thickness(0)
        };

        scrollViewer.Content = textBlock;
        window.Content = scrollViewer;

        return window;
    }

    /// <summary>
    /// إنشاء نافذة تشخيصية مع أزرار إضافية
    /// </summary>
    /// <param name="title">عنوان النافذة</param>
    /// <param name="content">المحتوى النصي</param>
    /// <param name="buttons">قائمة الأزرار مع أحداثها</param>
    /// <param name="owner">النافذة الأب</param>
    /// <param name="width">عرض النافذة</param>
    /// <param name="height">ارتفاع النافذة</param>
    /// <returns>النافذة المُنشأة</returns>
    public static Window CreateDiagnosticsWindowWithButtons(
        string title,
        string content,
        Dictionary<string, Action> buttons,
        Window? owner = null,
        double width = 900,
        double height = 700)
    {
        var window = CreateDiagnosticsWindow(title, content, owner, width, height);

        // إنشاء Grid رئيسي
        var mainGrid = new Grid();
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // نقل المحتوى الموجود إلى الصف الأول
        var existingContent = (ScrollViewer)window.Content;
        Grid.SetRow(existingContent, 0);
        mainGrid.Children.Add(existingContent);

        // إنشاء شريط الأزرار
        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Right,
            Margin = new Thickness(15, 10, 15, 15)
        };

        // إضافة الأزرار
        foreach (var buttonInfo in buttons)
        {
            var button = new Button
            {
                Content = buttonInfo.Key,
                Margin = new Thickness(5, 0, 0, 0),
                Padding = new Thickness(15, 5, 15, 5),
                MinWidth = 80
            };

            button.Click += (s, e) => buttonInfo.Value?.Invoke();
            buttonPanel.Children.Add(button);
        }

        // إضافة زر إغلاق افتراضي
        var closeButton = new Button
        {
            Content = "إغلاق",
            Margin = new Thickness(10, 0, 0, 0),
            Padding = new Thickness(15, 5, 15, 5),
            MinWidth = 80,
            IsCancel = true
        };
        closeButton.Click += (s, e) => window.Close();
        buttonPanel.Children.Add(closeButton);

        Grid.SetRow(buttonPanel, 1);
        mainGrid.Children.Add(buttonPanel);

        window.Content = mainGrid;
        return window;
    }

    /// <summary>
    /// إنشاء نافذة إعدادات موحدة
    /// </summary>
    /// <param name="title">عنوان النافذة</param>
    /// <param name="content">محتوى النافذة</param>
    /// <param name="owner">النافذة الأب</param>
    /// <param name="width">عرض النافذة</param>
    /// <param name="height">ارتفاع النافذة</param>
    /// <returns>النافذة المُنشأة</returns>
    public static Window CreateSettingsWindow(
        string title,
        UIElement content,
        Window? owner = null,
        double width = 500,
        double height = 400)
    {
        var window = new Window
        {
            Title = title,
            Width = width,
            Height = height,
            Owner = owner,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            ResizeMode = ResizeMode.NoResize,
            ShowInTaskbar = false,
            Icon = owner?.Icon
        };

        // إنشاء Grid رئيسي مع هوامش
        var mainGrid = new Grid
        {
            Margin = new Thickness(20)
        };

        mainGrid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
        mainGrid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // إضافة المحتوى
        Grid.SetRow(content, 0);
        mainGrid.Children.Add(content);

        // إنشاء شريط الأزرار
        var buttonPanel = new StackPanel
        {
            Orientation = Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Right,
            Margin = new Thickness(0, 20, 0, 0)
        };

        // زر موافق
        var okButton = new Button
        {
            Content = "موافق",
            Margin = new Thickness(0, 0, 10, 0),
            Padding = new Thickness(20, 5, 20, 5),
            MinWidth = 80,
            IsDefault = true
        };
        okButton.Click += (s, e) => { window.DialogResult = true; window.Close(); };
        buttonPanel.Children.Add(okButton);

        // زر إلغاء
        var cancelButton = new Button
        {
            Content = "إلغاء",
            Padding = new Thickness(20, 5, 20, 5),
            MinWidth = 80,
            IsCancel = true
        };
        cancelButton.Click += (s, e) => { window.DialogResult = false; window.Close(); };
        buttonPanel.Children.Add(cancelButton);

        Grid.SetRow(buttonPanel, 1);
        mainGrid.Children.Add(buttonPanel);

        window.Content = mainGrid;
        return window;
    }

    /// <summary>
    /// عرض رسالة تأكيد موحدة
    /// </summary>
    /// <param name="message">نص الرسالة</param>
    /// <param name="title">عنوان الرسالة</param>
    /// <param name="owner">النافذة الأب</param>
    /// <returns>نتيجة اختيار المستخدم</returns>
    public static MessageBoxResult ShowConfirmationDialog(
        string message, 
        string title = "تأكيد", 
        Window? owner = null)
    {
        return MessageBox.Show(
            owner,
            message,
            title,
            MessageBoxButton.YesNo,
            MessageBoxImage.Question,
            MessageBoxResult.No);
    }

    /// <summary>
    /// عرض رسالة معلومات مع تسجيل
    /// </summary>
    /// <param name="message">نص الرسالة</param>
    /// <param name="title">عنوان الرسالة</param>
    /// <param name="owner">النافذة الأب</param>
    public static void ShowInfoDialog(
        string message, 
        string title = "معلومات", 
        Window? owner = null)
    {
        MessageBox.Show(
            owner,
            message,
            title,
            MessageBoxButton.OK,
            MessageBoxImage.Information);

        AppLogger.LogInfo($"{title}: {message}");
    }

    /// <summary>
    /// عرض رسالة خطأ مع تسجيل
    /// </summary>
    /// <param name="message">نص الرسالة</param>
    /// <param name="title">عنوان الرسالة</param>
    /// <param name="owner">النافذة الأب</param>
    /// <param name="exception">الاستثناء إن وجد</param>
    public static void ShowErrorDialog(
        string message, 
        string title = "خطأ", 
        Window? owner = null,
        Exception? exception = null)
    {
        var fullMessage = exception != null 
            ? $"{message}\n\nتفاصيل الخطأ: {exception.Message}"
            : message;

        MessageBox.Show(
            owner,
            fullMessage,
            title,
            MessageBoxButton.OK,
            MessageBoxImage.Error);

        AppLogger.LogError($"{title}: {fullMessage}");
    }

    /// <summary>
    /// عرض رسالة تحذير مع تسجيل
    /// </summary>
    /// <param name="message">نص الرسالة</param>
    /// <param name="title">عنوان الرسالة</param>
    /// <param name="owner">النافذة الأب</param>
    public static void ShowWarningDialog(
        string message, 
        string title = "تحذير", 
        Window? owner = null)
    {
        MessageBox.Show(
            owner,
            message,
            title,
            MessageBoxButton.OK,
            MessageBoxImage.Warning);

        AppLogger.LogWarning($"{title}: {message}");
    }

    /// <summary>
    /// إنشاء نافذة تقدم العمل
    /// </summary>
    /// <param name="title">عنوان النافذة</param>
    /// <param name="message">رسالة التقدم</param>
    /// <param name="owner">النافذة الأب</param>
    /// <returns>النافذة مع شريط التقدم</returns>
    public static (Window window, ProgressBar progressBar, TextBlock statusText) CreateProgressWindow(
        string title,
        string message,
        Window? owner = null)
    {
        var window = new Window
        {
            Title = title,
            Width = 400,
            Height = 150,
            Owner = owner,
            WindowStartupLocation = WindowStartupLocation.CenterOwner,
            ResizeMode = ResizeMode.NoResize,
            ShowInTaskbar = false,
            WindowStyle = WindowStyle.ToolWindow
        };

        var grid = new Grid
        {
            Margin = new Thickness(20)
        };

        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(20) });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        // رسالة الحالة
        var statusText = new TextBlock
        {
            Text = message,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(0, 0, 0, 10)
        };
        Grid.SetRow(statusText, 0);
        grid.Children.Add(statusText);

        // شريط التقدم
        var progressBar = new ProgressBar
        {
            Height = 20,
            Margin = new Thickness(0, 10, 0, 10)
        };
        Grid.SetRow(progressBar, 1);
        grid.Children.Add(progressBar);

        window.Content = grid;

        return (window, progressBar, statusText);
    }

    /// <summary>
    /// تطبيق تنسيق موحد على النافذة
    /// </summary>
    /// <param name="window">النافذة المراد تنسيقها</param>
    /// <param name="isDarkTheme">استخدام المظهر الداكن</param>
    public static void ApplyUnifiedStyling(Window window, bool isDarkTheme = false)
    {
        if (window == null) return;

        if (isDarkTheme)
        {
            window.Background = new SolidColorBrush(Color.FromRgb(32, 32, 32));
            // يمكن إضافة المزيد من تنسيقات المظهر الداكن هنا
        }
        else
        {
            window.Background = new SolidColorBrush(Color.FromRgb(248, 249, 250));
        }

        // تطبيق خط موحد
        window.FontFamily = new FontFamily("Segoe UI");
        window.FontSize = 12;
    }

    /// <summary>
    /// حفظ موضع وحجم النافذة
    /// </summary>
    /// <param name="window">النافذة</param>
    /// <param name="settingsKey">مفتاح الإعدادات</param>
    public static void SaveWindowState(Window window, string settingsKey)
    {
        if (window == null || string.IsNullOrEmpty(settingsKey)) return;

        try
        {
            var settings = SettingsCache.Instance;
            settings.SetSetting($"{settingsKey}.Left", window.Left.ToString());
            settings.SetSetting($"{settingsKey}.Top", window.Top.ToString());
            settings.SetSetting($"{settingsKey}.Width", window.Width.ToString());
            settings.SetSetting($"{settingsKey}.Height", window.Height.ToString());
            settings.SetSetting($"{settingsKey}.WindowState", window.WindowState.ToString());
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في حفظ حالة النافذة {settingsKey}: {ex.Message}");
        }
    }

    /// <summary>
    /// استعادة موضع وحجم النافذة
    /// </summary>
    /// <param name="window">النافذة</param>
    /// <param name="settingsKey">مفتاح الإعدادات</param>
    public static void RestoreWindowState(Window window, string settingsKey)
    {
        if (window == null || string.IsNullOrEmpty(settingsKey)) return;

        try
        {
            var settings = SettingsCache.Instance;
            
            if (double.TryParse(settings.GetSetting($"{settingsKey}.Left"), out var left))
                window.Left = left;
            
            if (double.TryParse(settings.GetSetting($"{settingsKey}.Top"), out var top))
                window.Top = top;
            
            if (double.TryParse(settings.GetSetting($"{settingsKey}.Width"), out var width))
                window.Width = width;
            
            if (double.TryParse(settings.GetSetting($"{settingsKey}.Height"), out var height))
                window.Height = height;
            
            if (Enum.TryParse<WindowState>(settings.GetSetting($"{settingsKey}.WindowState"), out var windowState))
                window.WindowState = windowState;
        }
        catch (Exception ex)
        {
            AppLogger.LogError($"خطأ في استعادة حالة النافذة {settingsKey}: {ex.Message}");
        }
    }
}