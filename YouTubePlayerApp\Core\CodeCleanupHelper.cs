using System.Text;
using System.Text.RegularExpressions;

namespace YouTubePlayerApp.Core;

/// <summary>
/// مساعد تنظيف الكود لإزالة using statements غير المستخدمة وتحسين تنسيق الكود
/// </summary>
public static class CodeCleanupHelper
{
    /// <summary>
    /// قائمة using statements الأساسية المطلوبة لمعظم ملفات المشروع
    /// </summary>
    private static readonly HashSet<string> EssentialUsings = new()
    {
        "System",
        "System.Collections.Generic",
        "System.Linq",
        "System.Text",
        "System.Threading.Tasks",
        "System.Windows",
        "Microsoft.Web.WebView2.Core"
    };

    /// <summary>
    /// using statements خاصة بـ WPF
    /// </summary>
    private static readonly HashSet<string> WpfUsings = new()
    {
        "System.Windows.Controls",
        "System.Windows.Data",
        "System.Windows.Documents",
        "System.Windows.Input",
        "System.Windows.Media",
        "System.Windows.Media.Imaging",
        "System.Windows.Navigation",
        "System.Windows.Shapes",
        "System.Windows.Threading"
    };

    /// <summary>
    /// using statements خاصة بالتشخيص والأداء
    /// </summary>
    private static readonly HashSet<string> DiagnosticsUsings = new()
    {
        "System.Diagnostics",
        "System.Runtime.CompilerServices",
        "System.Collections.Concurrent",
        "System.Text.Json",
        "System.Text.RegularExpressions"
    };

    /// <summary>
    /// تنظيف using statements في ملف الكود
    /// </summary>
    /// <param name="sourceCode">كود المصدر</param>
    /// <param name="fileType">نوع الملف (WPF, Core, Diagnostics)</param>
    /// <returns>الكود المنظف</returns>
    public static string CleanupUsingStatements(string sourceCode, FileType fileType = FileType.Core)
    {
        if (string.IsNullOrEmpty(sourceCode)) return sourceCode;

        var lines = sourceCode.Split('\n');
        var cleanedLines = new List<string>();
        var usingStatements = new HashSet<string>();
        var nonUsingLines = new List<string>();
        
        bool inUsingSection = true;

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            
            if (trimmedLine.StartsWith("using ") && trimmedLine.EndsWith(";"))
            {
                if (inUsingSection)
                {
                    var usingStatement = trimmedLine.Substring(6, trimmedLine.Length - 7).Trim();
                    if (IsUsingNeeded(usingStatement, fileType))
                    {
                        usingStatements.Add(trimmedLine);
                    }
                }
                else
                {
                    nonUsingLines.Add(line);
                }
            }
            else if (string.IsNullOrWhiteSpace(trimmedLine))
            {
                if (!inUsingSection)
                {
                    nonUsingLines.Add(line);
                }
            }
            else
            {
                inUsingSection = false;
                nonUsingLines.Add(line);
            }
        }

        // ترتيب using statements
        var sortedUsings = usingStatements.OrderBy(u => u).ToList();
        
        // بناء الكود المنظف
        cleanedLines.AddRange(sortedUsings);
        if (sortedUsings.Any())
        {
            cleanedLines.Add("");
        }
        cleanedLines.AddRange(nonUsingLines);

        return string.Join("\n", cleanedLines);
    }

    /// <summary>
    /// تحديد ما إذا كان using statement مطلوباً
    /// </summary>
    /// <param name="usingStatement">using statement</param>
    /// <param name="fileType">نوع الملف</param>
    /// <returns>true إذا كان مطلوباً</returns>
    private static bool IsUsingNeeded(string usingStatement, FileType fileType)
    {
        // using statements أساسية دائماً مطلوبة
        if (EssentialUsings.Contains(usingStatement))
            return true;

        // فحص حسب نوع الملف
        return fileType switch
        {
            FileType.WPF => WpfUsings.Contains(usingStatement) || EssentialUsings.Contains(usingStatement),
            FileType.Diagnostics => DiagnosticsUsings.Contains(usingStatement) || EssentialUsings.Contains(usingStatement),
            FileType.Core => EssentialUsings.Contains(usingStatement),
            _ => EssentialUsings.Contains(usingStatement)
        };
    }

    /// <summary>
    /// تنسيق الكود ليتبع معايير C# الموحدة
    /// </summary>
    /// <param name="sourceCode">كود المصدر</param>
    /// <returns>الكود المنسق</returns>
    public static string FormatCode(string sourceCode)
    {
        if (string.IsNullOrEmpty(sourceCode)) return sourceCode;

        var lines = sourceCode.Split('\n');
        var formattedLines = new List<string>();
        int indentLevel = 0;
        const string indentString = "    "; // 4 مسافات

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            
            if (string.IsNullOrEmpty(trimmedLine))
            {
                formattedLines.Add("");
                continue;
            }

            // تقليل المستوى للأقواس المغلقة
            if (trimmedLine.StartsWith("}"))
            {
                indentLevel = Math.Max(0, indentLevel - 1);
            }

            // إضافة المسافات البادئة
            var indentedLine = new string(' ', indentLevel * 4) + trimmedLine;
            formattedLines.Add(indentedLine);

            // زيادة المستوى للأقواس المفتوحة
            if (trimmedLine.EndsWith("{"))
            {
                indentLevel++;
            }
        }

        return string.Join("\n", formattedLines);
    }

    /// <summary>
    /// إضافة تعليقات توضيحية للوظائف المعقدة
    /// </summary>
    /// <param name="sourceCode">كود المصدر</param>
    /// <returns>الكود مع التعليقات</returns>
    public static string AddDocumentationComments(string sourceCode)
    {
        if (string.IsNullOrEmpty(sourceCode)) return sourceCode;

        var methodPattern = @"(public|private|protected|internal)\s+(static\s+)?(async\s+)?([\w<>]+)\s+(\w+)\s*\([^)]*\)";
        var regex = new Regex(methodPattern, RegexOptions.Multiline);

        return regex.Replace(sourceCode, match =>
        {
            var visibility = match.Groups[1].Value;
            var isStatic = !string.IsNullOrEmpty(match.Groups[2].Value);
            var isAsync = !string.IsNullOrEmpty(match.Groups[3].Value);
            var returnType = match.Groups[4].Value;
            var methodName = match.Groups[5].Value;

            // تجنب إضافة تعليقات للطرق التي تحتوي بالفعل على تعليقات
            var beforeMatch = sourceCode.Substring(0, match.Index);
            if (beforeMatch.TrimEnd().EndsWith("*/"))
            {
                return match.Value;
            }

            var comment = GenerateMethodComment(methodName, returnType, isAsync);
            return $"{comment}\n{match.Value}";
        });
    }

    /// <summary>
    /// توليد تعليق للطريقة
    /// </summary>
    /// <param name="methodName">اسم الطريقة</param>
    /// <param name="returnType">نوع الإرجاع</param>
    /// <param name="isAsync">هل الطريقة غير متزامنة</param>
    /// <returns>التعليق المولد</returns>
    private static string GenerateMethodComment(string methodName, string returnType, bool isAsync)
    {
        var comment = new StringBuilder();
        comment.AppendLine("    /// <summary>");
        
        // توليد وصف بناءً على اسم الطريقة
        var description = GenerateMethodDescription(methodName, isAsync);
        comment.AppendLine($"    /// {description}");
        
        comment.AppendLine("    /// </summary>");

        if (returnType != "void" && !returnType.StartsWith("Task"))
        {
            comment.AppendLine($"    /// <returns>{GetReturnDescription(returnType)}</returns>");
        }

        return comment.ToString().TrimEnd();
    }

    /// <summary>
    /// توليد وصف للطريقة بناءً على اسمها
    /// </summary>
    /// <param name="methodName">اسم الطريقة</param>
    /// <param name="isAsync">هل الطريقة غير متزامنة</param>
    /// <returns>الوصف المولد</returns>
    private static string GenerateMethodDescription(string methodName, bool isAsync)
    {
        var asyncPrefix = isAsync ? "تنفيذ غير متزامن لـ" : "";
        
        return methodName.ToLower() switch
        {
            var name when name.Contains("initialize") => $"{asyncPrefix}تهيئة المكون",
            var name when name.Contains("dispose") => "التخلص من الموارد وتنظيف الذاكرة",
            var name when name.Contains("update") => $"{asyncPrefix}تحديث البيانات أو الحالة",
            var name when name.Contains("load") => $"{asyncPrefix}تحميل البيانات أو الموارد",
            var name when name.Contains("save") => $"{asyncPrefix}حفظ البيانات أو الإعدادات",
            var name when name.Contains("validate") => "التحقق من صحة البيانات",
            var name when name.Contains("process") => $"{asyncPrefix}معالجة البيانات",
            var name when name.Contains("handle") => "معالجة الحدث أو الطلب",
            var name when name.Contains("execute") => $"{asyncPrefix}تنفيذ العملية",
            var name when name.Contains("create") => "إنشاء مثيل أو كائن جديد",
            var name when name.Contains("delete") => "حذف البيانات أو الموارد",
            var name when name.Contains("get") => "الحصول على البيانات أو القيمة",
            var name when name.Contains("set") => "تعيين البيانات أو القيمة",
            var name when name.Contains("configure") => "تكوين الإعدادات أو المكون",
            var name when name.Contains("apply") => $"{asyncPrefix}تطبيق التغييرات أو الإعدادات",
            _ => $"{asyncPrefix}تنفيذ العملية المطلوبة"
        };
    }

    /// <summary>
    /// الحصول على وصف نوع الإرجاع
    /// </summary>
    /// <param name="returnType">نوع الإرجاع</param>
    /// <returns>وصف نوع الإرجاع</returns>
    private static string GetReturnDescription(string returnType)
    {
        return returnType.ToLower() switch
        {
            "bool" => "true في حالة النجاح، false في حالة الفشل",
            "string" => "النص المطلوب أو null في حالة عدم وجود بيانات",
            "int" => "القيمة العددية المطلوبة",
            "double" => "القيمة العشرية المطلوبة",
            var type when type.Contains("list") => "قائمة بالعناصر المطلوبة",
            var type when type.Contains("dictionary") => "قاموس بالبيانات المطلوبة",
            var type when type.Contains("task") => "مهمة غير متزامنة",
            _ => "النتيجة المطلوبة"
        };
    }

    /// <summary>
    /// إزالة الكود المكرر والمتشابه
    /// </summary>
    /// <param name="sourceCode">كود المصدر</param>
    /// <returns>الكود بعد إزالة التكرار</returns>
    public static string RemoveDuplicateCode(string sourceCode)
    {
        if (string.IsNullOrEmpty(sourceCode)) return sourceCode;

        // إزالة الأسطر المكررة المتتالية
        var lines = sourceCode.Split('\n');
        var uniqueLines = new List<string>();
        string? previousLine = null;

        foreach (var line in lines)
        {
            var trimmedLine = line.Trim();
            
            // تجنب إزالة الأسطر الفارغة المتعددة إذا كانت ضرورية للتنسيق
            if (string.IsNullOrWhiteSpace(trimmedLine))
            {
                if (previousLine != null && !string.IsNullOrWhiteSpace(previousLine))
                {
                    uniqueLines.Add(line);
                }
            }
            else if (trimmedLine != previousLine?.Trim())
            {
                uniqueLines.Add(line);
            }

            previousLine = line;
        }

        return string.Join("\n", uniqueLines);
    }

    /// <summary>
    /// تحسين أداء الكود بإزالة العمليات غير الضرورية
    /// </summary>
    /// <param name="sourceCode">كود المصدر</param>
    /// <returns>الكود المحسن</returns>
    public static string OptimizePerformance(string sourceCode)
    {
        if (string.IsNullOrEmpty(sourceCode)) return sourceCode;

        // إزالة استدعاءات ToString() غير الضرورية
        sourceCode = Regex.Replace(sourceCode, @"\.ToString\(\)\.ToString\(\)", ".ToString()");
        
        // تحسين استخدام string.IsNullOrEmpty
        sourceCode = Regex.Replace(sourceCode, @"(\w+)\s*==\s*null\s*\|\|\s*\1\s*==\s*""""", 
            "string.IsNullOrEmpty($1)");
        
        // تحسين استخدام string.IsNullOrWhiteSpace
        sourceCode = Regex.Replace(sourceCode, @"string\.IsNullOrEmpty\((\w+)\)\s*\|\|\s*\1\.Trim\(\)\.Length\s*==\s*0", 
            "string.IsNullOrWhiteSpace($1)");

        return sourceCode;
    }

    /// <summary>
    /// تطبيق جميع تحسينات التنظيف على الكود
    /// </summary>
    /// <param name="sourceCode">كود المصدر</param>
    /// <param name="fileType">نوع الملف</param>
    /// <returns>الكود المنظف والمحسن</returns>
    public static string ApplyAllCleanups(string sourceCode, FileType fileType = FileType.Core)
    {
        if (string.IsNullOrEmpty(sourceCode)) return sourceCode;

        // تطبيق جميع التحسينات بالترتيب
        sourceCode = CleanupUsingStatements(sourceCode, fileType);
        sourceCode = RemoveDuplicateCode(sourceCode);
        sourceCode = OptimizePerformance(sourceCode);
        sourceCode = FormatCode(sourceCode);
        sourceCode = AddDocumentationComments(sourceCode);

        return sourceCode;
    }
}

/// <summary>
/// أنواع الملفات المختلفة في المشروع
/// </summary>
public enum FileType
{
    /// <summary>
    /// ملف أساسي عام
    /// </summary>
    Core,
    
    /// <summary>
    /// ملف WPF (نوافذ وواجهات)
    /// </summary>
    WPF,
    
    /// <summary>
    /// ملف تشخيصي أو أداء
    /// </summary>
    Diagnostics
}