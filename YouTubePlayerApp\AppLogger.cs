using System.Collections.Concurrent;
using System.IO;
using System.Text;

namespace YouTubePlayerApp;

/// <summary>
/// Comprehensive logging system for the YouTube Desktop Player
/// </summary>
public sealed class AppLogger : IDisposable
{
    private static readonly Lazy<AppLogger> _instance = new(() => new AppLogger());
    public static AppLogger Instance => _instance.Value;

    private readonly ConcurrentQueue<LogEntry> _logQueue;
    private readonly Timer _flushTimer;
    private readonly string _logFilePath;
    private readonly object _lockObject = new();
    private bool _disposed;
    private readonly StringBuilder _memoryBuffer;

    // Configuration
    public LogLevel MinimumLogLevel { get; set; } = LogLevel.Info;
    public bool EnableFileLogging { get; set; } = true;
    public bool EnableConsoleLogging { get; set; } = true;
    public bool EnableMemoryBuffer { get; set; } = true;
    public int MaxMemoryBufferEntries { get; set; } = 1000;
    public int FlushIntervalMs { get; set; } = 5000; // 5 seconds

    private AppLogger()
    {
        _logQueue = new ConcurrentQueue<LogEntry>();
        _memoryBuffer = new StringBuilder();
        
        // Create logs directory
        var logsDir = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "YouTubePlayerApp", "Logs");
        Directory.CreateDirectory(logsDir);
        
        // Set log file path with timestamp
        _logFilePath = Path.Combine(logsDir, $"app_{DateTime.Now:yyyyMMdd_HHmmss}.log");
        
        // Timer for periodic log flushing
        _flushTimer = new Timer(FlushLogs, null, TimeSpan.FromMilliseconds(FlushIntervalMs), TimeSpan.FromMilliseconds(FlushIntervalMs));
        
        Log(LogLevel.Info, "AppLogger", "Logger initialized", new { LogFile = _logFilePath });
    }

    public void Log(LogLevel level, string component, string message, object? context = null)
    {
        if (level < MinimumLogLevel || _disposed) return;

        var entry = new LogEntry
        {
            Timestamp = DateTime.UtcNow,
            Level = level,
            Component = component,
            Message = message,
            Context = context,
            ThreadId = Thread.CurrentThread.ManagedThreadId
        };

        _logQueue.Enqueue(entry);

        // Immediate console output for errors and warnings
        if ((level >= LogLevel.Warning && EnableConsoleLogging) || level >= LogLevel.Error)
        {
            WriteToConsole(entry);
        }
    }

    public void LogDebug(string component, string message, object? context = null)
        => Log(LogLevel.Debug, component, message, context);

    public void LogInfo(string component, string message, object? context = null)
        => Log(LogLevel.Info, component, message, context);

    public void LogWarning(string component, string message, object? context = null)
        => Log(LogLevel.Warning, component, message, context);

    public void LogError(string component, string message, object? context = null)
        => Log(LogLevel.Error, component, message, context);

    public void LogError(string component, Exception exception, string? additionalMessage = null)
    {
        var message = additionalMessage != null ? $"{additionalMessage}: {exception.Message}" : exception.Message;
        Log(LogLevel.Error, component, message, new
        {
            ExceptionType = exception.GetType().Name,
            StackTrace = exception.StackTrace,
            InnerException = exception.InnerException?.Message
        });
    }

    public void LogPerformance(string component, string operation, long durationMs, object? metrics = null)
    {
        Log(LogLevel.Info, $"{component}.Performance", $"{operation} completed in {durationMs}ms", metrics);
    }

    public void LogMemoryUsage(string component, long memoryBytes, string? operation = null)
    {
        var memoryMB = memoryBytes / 1024.0 / 1024.0;
        var message = operation != null ? $"{operation}: {memoryMB:F2} MB" : $"Memory usage: {memoryMB:F2} MB";
        Log(LogLevel.Debug, $"{component}.Memory", message, new { MemoryBytes = memoryBytes });
    }

    public void LogAdBlockingActivity(string action, int count, string? details = null)
    {
        var message = details != null ? $"{action}: {count} ({details})" : $"{action}: {count}";
        Log(LogLevel.Debug, "AdBlocker", message, new { Action = action, Count = count, Details = details });
    }

    public void LogVideoQualityChange(string from, string to, string reason)
    {
        Log(LogLevel.Info, "VideoQuality", $"Quality changed: {from} → {to}", new { From = from, To = to, Reason = reason });
    }

    public void LogNetworkRequest(string url, bool blocked, string? reason = null)
    {
        var level = blocked ? LogLevel.Debug : LogLevel.Trace;
        var message = blocked ? $"Blocked: {url}" : $"Allowed: {url}";
        Log(level, "Network", message, new { Url = url, Blocked = blocked, Reason = reason });
    }

    public List<LogEntry> GetRecentLogs(int count = 100)
    {
        var logs = new List<LogEntry>();
        var tempQueue = new List<LogEntry>();
        
        // Drain queue to get all logs
        while (_logQueue.TryDequeue(out var entry))
        {
            tempQueue.Add(entry);
        }
        
        // Re-queue logs and return recent ones
        foreach (var entry in tempQueue)
        {
            _logQueue.Enqueue(entry);
        }
        
        return tempQueue.TakeLast(count).ToList();
    }

    public List<LogEntry> GetLogsByComponent(string component, int maxCount = 50)
    {
        return GetRecentLogs(500)
            .Where(log => log.Component.Equals(component, StringComparison.OrdinalIgnoreCase))
            .TakeLast(maxCount)
            .ToList();
    }

    public List<LogEntry> GetErrorLogs(int maxCount = 20)
    {
        return GetRecentLogs(1000)
            .Where(log => log.Level >= LogLevel.Warning)
            .TakeLast(maxCount)
            .ToList();
    }

    public string GetMemoryBuffer()
    {
        lock (_lockObject)
        {
            return _memoryBuffer.ToString();
        }
    }

    public void ClearLogs()
    {
        while (_logQueue.TryDequeue(out _)) { }
        
        lock (_lockObject)
        {
            _memoryBuffer.Clear();
        }
    }

    public void FlushNow()
    {
        FlushLogs(null);
    }

    private void FlushLogs(object? state)
    {
        if (_disposed) return;

        var logsToFlush = new List<LogEntry>();
        
        // Drain the queue
        while (_logQueue.TryDequeue(out var entry))
        {
            logsToFlush.Add(entry);
        }

        if (logsToFlush.Count == 0) return;

        try
        {
            // Write to file
            if (EnableFileLogging)
            {
                WriteToFile(logsToFlush);
            }

            // Write to memory buffer
            if (EnableMemoryBuffer)
            {
                WriteToMemoryBuffer(logsToFlush);
            }

            // Write to console (for levels that weren't already written)
            if (EnableConsoleLogging)
            {
                foreach (var entry in logsToFlush.Where(e => e.Level < LogLevel.Warning))
                {
                    WriteToConsole(entry);
                }
            }
        }
        catch (Exception ex)
        {
            // Fallback: write critical error to console
            Console.WriteLine($"[LOGGER ERROR] Failed to flush logs: {ex.Message}");
        }
    }

    private void WriteToFile(List<LogEntry> logs)
    {
        try
        {
            var logLines = logs.Select(FormatLogEntry);
            File.AppendAllLines(_logFilePath, logLines, Encoding.UTF8);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"[LOGGER] Failed to write to file: {ex.Message}");
        }
    }

    private void WriteToMemoryBuffer(List<LogEntry> logs)
    {
        lock (_lockObject)
        {
            foreach (var log in logs)
            {
                _memoryBuffer.AppendLine(FormatLogEntry(log));
            }

            // Trim buffer if too large
            var lines = _memoryBuffer.ToString().Split('\n');
            if (lines.Length > MaxMemoryBufferEntries)
            {
                var trimmedLines = lines.TakeLast(MaxMemoryBufferEntries / 2);
                _memoryBuffer.Clear();
                _memoryBuffer.AppendJoin('\n', trimmedLines);
            }
        }
    }

    private void WriteToConsole(LogEntry entry)
    {
        var color = entry.Level switch
        {
            LogLevel.Error => ConsoleColor.Red,
            LogLevel.Warning => ConsoleColor.Yellow,
            LogLevel.Info => ConsoleColor.White,
            LogLevel.Debug => ConsoleColor.Gray,
            LogLevel.Trace => ConsoleColor.DarkGray,
            _ => ConsoleColor.White
        };

        var originalColor = Console.ForegroundColor;
        try
        {
            Console.ForegroundColor = color;
            Console.WriteLine(FormatLogEntry(entry));
        }
        finally
        {
            Console.ForegroundColor = originalColor;
        }
    }

    private string FormatLogEntry(LogEntry entry)
    {
        var contextStr = entry.Context != null ? $" | {System.Text.Json.JsonSerializer.Serialize(entry.Context)}" : "";
        return $"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{entry.Level}] [{entry.Component}] T{entry.ThreadId} | {entry.Message}{contextStr}";
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        
        Log(LogLevel.Info, "AppLogger", "Logger shutting down");
        
        _flushTimer?.Dispose();
        FlushLogs(null); // Final flush
        
        ClearLogs();
    }
}

public enum LogLevel
{
    Trace = 0,
    Debug = 1,
    Info = 2,
    Warning = 3,
    Error = 4
}

public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public LogLevel Level { get; set; }
    public string Component { get; set; } = "";
    public string Message { get; set; } = "";
    public object? Context { get; set; }
    public int ThreadId { get; set; }
}
