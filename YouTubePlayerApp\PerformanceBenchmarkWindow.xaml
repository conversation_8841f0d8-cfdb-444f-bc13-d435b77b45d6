<Window x:Class="YouTubePlayerApp.PerformanceBenchmarkWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:YouTubePlayerApp"
        mc:Ignorable="d"
        Title="Performance Monitor" Height="500" Width="700"
        WindowStartupLocation="CenterOwner" ResizeMode="CanResize">
    
    <Grid Margin="15">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Performance Monitor &amp; Benchmarks" 
                   FontSize="18" FontWeight="Bold" Margin="0,0,0,15"/>
        
        <!-- Content -->
        <TabControl Grid.Row="1">
            <TabItem Header="Current Metrics">
                <Grid Margin="10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Memory Metrics -->
                    <GroupBox Header="Memory Usage" Grid.Column="0" Margin="0,0,10,0">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Current Memory:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CurrentMemoryText" Text="0 MB" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Peak Memory:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="PeakMemoryText" Text="0 MB" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Working Set:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="WorkingSetText" Text="0 MB" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Private Bytes:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" x:Name="PrivateBytesText" Text="0 MB" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <Button Grid.Row="4" Grid.ColumnSpan="2" x:Name="ForceGCButton" 
                                    Content="Force Garbage Collection" Margin="0,10,0,0" 
                                    Click="ForceGCButton_Click"/>
                        </Grid>
                    </GroupBox>
                    
                    <!-- Performance Metrics -->
                    <GroupBox Header="Performance" Grid.Column="1" Margin="10,0,0,0">
                        <Grid Margin="10">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="100"/>
                            </Grid.ColumnDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="CPU Usage:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="CpuUsageText" Text="0%" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Uptime:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="UptimeText" Text="0 min" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Gen 0 GC:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="Gen0GCText" Text="0" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="3" Grid.Column="0" Text="Gen 1 GC:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="3" Grid.Column="1" x:Name="Gen1GCText" Text="0" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                            
                            <TextBlock Grid.Row="4" Grid.Column="0" Text="Gen 2 GC:" VerticalAlignment="Center"/>
                            <TextBlock Grid.Row="4" Grid.Column="1" x:Name="Gen2GCText" Text="0" 
                                       FontWeight="Bold" HorizontalAlignment="Right" VerticalAlignment="Center"/>
                        </Grid>
                    </GroupBox>
                </Grid>
            </TabItem>
            
            <TabItem Header="Benchmarks">
                <StackPanel Margin="15">
                    <TextBlock Text="Performance Benchmarks" FontSize="14" FontWeight="Bold" Margin="0,0,0,15"/>
                    
                    <GroupBox Header="Ad Blocker Performance" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <Button x:Name="BenchmarkAdBlockerButton" Content="Benchmark Ad Blocker" 
                                    Width="200" HorizontalAlignment="Left" Margin="0,5"
                                    Click="BenchmarkAdBlockerButton_Click"/>
                            <TextBlock x:Name="AdBlockerBenchmarkResult" Text="Click to run benchmark" 
                                       Margin="0,5" FontFamily="Consolas"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Video Quality Controller Performance" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <Button x:Name="BenchmarkQualityControllerButton" Content="Benchmark Quality Controller" 
                                    Width="200" HorizontalAlignment="Left" Margin="0,5"
                                    Click="BenchmarkQualityControllerButton_Click"/>
                            <TextBlock x:Name="QualityControllerBenchmarkResult" Text="Click to run benchmark" 
                                       Margin="0,5" FontFamily="Consolas"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Settings Cache Performance" Margin="0,0,0,15">
                        <StackPanel Margin="10">
                            <Button x:Name="BenchmarkSettingsCacheButton" Content="Benchmark Settings Cache" 
                                    Width="200" HorizontalAlignment="Left" Margin="0,5"
                                    Click="BenchmarkSettingsCacheButton_Click"/>
                            <TextBlock x:Name="SettingsCacheBenchmarkResult" Text="Click to run benchmark" 
                                       Margin="0,5" FontFamily="Consolas"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </TabItem>
        </TabControl>
        
        <!-- Bottom buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,15,0,0">
            <Button x:Name="RefreshButton" Content="Refresh" Width="80" Height="30" 
                    Margin="0,0,10,0" Click="RefreshButton_Click"/>
            <Button x:Name="CloseButton" Content="Close" Width="80" Height="30" 
                    IsCancel="True" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
