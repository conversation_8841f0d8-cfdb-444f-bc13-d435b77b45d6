using System.Collections.ObjectModel;
using System.IO;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Threading;
using Microsoft.Win32;

namespace YouTubePlayerApp;

public partial class LogViewerWindow : Window
{
    private readonly DispatcherTimer _refreshTimer;
    private readonly AppLogger _logger;
    private readonly ObservableCollection<LogEntry> _logEntries;
    private readonly ObservableCollection<LogEntry> _errorLogEntries;
    private readonly ObservableCollection<LogEntry> _performanceLogEntries;
    private LogLevel _currentLogLevel = LogLevel.Info;
    private string _selectedComponent = "All";

    public LogViewerWindow()
    {
        InitializeComponent();
        
        _logger = AppLogger.Instance;
        _logEntries = new ObservableCollection<LogEntry>();
        _errorLogEntries = new ObservableCollection<LogEntry>();
        _performanceLogEntries = new ObservableCollection<LogEntry>();
        
        // Set up data binding
        LogsDataGrid.ItemsSource = _logEntries;
        ErrorLogsDataGrid.ItemsSource = _errorLogEntries;
        PerformanceLogsDataGrid.ItemsSource = _performanceLogEntries;
        
        // Set up auto-refresh timer
        _refreshTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(2)
        };
        _refreshTimer.Tick += RefreshTimer_Tick;
        _refreshTimer.Start();
        
        LoadInitialData();
        PopulateComponentFilter();
    }

    private void LoadInitialData()
    {
        RefreshLogs();
        UpdateErrorSummary();
        UpdateRawLogText();
    }

    private void PopulateComponentFilter()
    {
        var components = _logger.GetRecentLogs(1000)
            .Select(log => log.Component)
            .Distinct()
            .OrderBy(c => c)
            .ToList();

        ComponentComboBox.Items.Clear();
        ComponentComboBox.Items.Add(new ComboBoxItem { Content = "All", IsSelected = true });
        
        foreach (var component in components)
        {
            ComponentComboBox.Items.Add(new ComboBoxItem { Content = component });
        }
    }

    private void RefreshTimer_Tick(object? sender, EventArgs e)
    {
        if (AutoRefreshCheckBox.IsChecked == true)
        {
            RefreshLogs();
            UpdateErrorSummary();
            UpdateRawLogText();
            LastUpdateText.Text = DateTime.Now.ToString("HH:mm:ss");
        }
    }

    private void RefreshLogs()
    {
        try
        {
            var logs = _logger.GetRecentLogs(1000);
            
            // Filter by level and component
            var filteredLogs = logs.Where(log => log.Level >= _currentLogLevel);
            
            if (_selectedComponent != "All")
            {
                filteredLogs = filteredLogs.Where(log => 
                    log.Component.Equals(_selectedComponent, StringComparison.OrdinalIgnoreCase));
            }
            
            // Update main logs
            _logEntries.Clear();
            foreach (var log in filteredLogs.TakeLast(500))
            {
                _logEntries.Add(log);
            }
            
            // Update error logs
            _errorLogEntries.Clear();
            foreach (var log in logs.Where(l => l.Level >= LogLevel.Warning).TakeLast(100))
            {
                _errorLogEntries.Add(log);
            }
            
            // Update performance logs
            _performanceLogEntries.Clear();
            foreach (var log in logs.Where(l => l.Component.Contains("Performance")).TakeLast(100))
            {
                _performanceLogEntries.Add(log);
            }
            
            LogCountText.Text = $"{_logEntries.Count} logs";
            
            // Auto-scroll to bottom if enabled
            if (ScrollToBottomCheckBox.IsChecked == true && _logEntries.Count > 0)
            {
                LogsDataGrid.ScrollIntoView(_logEntries.Last());
            }
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Error refreshing logs: {ex.Message}";
        }
    }

    private void UpdateErrorSummary()
    {
        try
        {
            var errorLogs = _logger.GetErrorLogs(50);
            
            if (errorLogs.Count == 0)
            {
                ErrorSummaryText.Text = "No errors recorded";
                ErrorDetailsText.Text = "Application is running without errors.";
            }
            else
            {
                var recentErrors = errorLogs.Count(e => e.Timestamp > DateTime.UtcNow.AddMinutes(-30));
                var totalErrors = errorLogs.Count(e => e.Level == LogLevel.Error);
                var totalWarnings = errorLogs.Count(e => e.Level == LogLevel.Warning);
                
                ErrorSummaryText.Text = $"Errors: {totalErrors}, Warnings: {totalWarnings}";
                ErrorDetailsText.Text = $"{recentErrors} errors/warnings in the last 30 minutes";
            }
        }
        catch (Exception ex)
        {
            ErrorSummaryText.Text = $"Error loading summary: {ex.Message}";
        }
    }

    private void UpdateRawLogText()
    {
        try
        {
            var rawText = _logger.GetMemoryBuffer();
            
            // Apply search filter if any
            if (!string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                var searchTerm = SearchTextBox.Text.ToLowerInvariant();
                var lines = rawText.Split('\n');
                var filteredLines = lines.Where(line => 
                    line.ToLowerInvariant().Contains(searchTerm));
                rawText = string.Join('\n', filteredLines);
            }
            
            RawLogTextBox.Text = rawText;
            
            // Auto-scroll to bottom
            if (ScrollToBottomCheckBox.IsChecked == true)
            {
                RawLogTextBox.ScrollToEnd();
            }
        }
        catch (Exception ex)
        {
            RawLogTextBox.Text = $"Error loading raw logs: {ex.Message}";
        }
    }

    private void LogLevel_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (LogLevelComboBox.SelectedItem is ComboBoxItem item && 
            int.TryParse(item.Tag?.ToString(), out int level))
        {
            _currentLogLevel = (LogLevel)level;
            RefreshLogs();
        }
    }

    private void Component_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (ComponentComboBox.SelectedItem is ComboBoxItem item)
        {
            _selectedComponent = item.Content?.ToString() ?? "All";
            RefreshLogs();
        }
    }

    private void AutoRefresh_Changed(object sender, RoutedEventArgs e)
    {
        if (AutoRefreshCheckBox.IsChecked == true)
        {
            _refreshTimer.Start();
            AutoRefreshIndicator.Fill = System.Windows.Media.Brushes.Green;
        }
        else
        {
            _refreshTimer.Stop();
            AutoRefreshIndicator.Fill = System.Windows.Media.Brushes.Red;
        }
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        RefreshLogs();
        UpdateErrorSummary();
        UpdateRawLogText();
        PopulateComponentFilter();
        StatusText.Text = "Logs refreshed";
    }

    private void Clear_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("Clear all logs? This action cannot be undone.", 
            "Confirm Clear", MessageBoxButton.YesNo, MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            _logger.ClearLogs();
            RefreshLogs();
            UpdateErrorSummary();
            UpdateRawLogText();
            StatusText.Text = "Logs cleared";
        }
    }

    private void Export_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Title = "Export Logs",
                Filter = "Text Files (*.txt)|*.txt|Log Files (*.log)|*.log|All Files (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"youtube_player_logs_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveDialog.ShowDialog() == true)
            {
                var logs = _logger.GetRecentLogs(5000);
                var logText = string.Join(Environment.NewLine, 
                    logs.Select(log => $"{log.Timestamp:yyyy-MM-dd HH:mm:ss.fff} [{log.Level}] [{log.Component}] T{log.ThreadId} | {log.Message}"));
                
                File.WriteAllText(saveDialog.FileName, logText);
                StatusText.Text = $"Logs exported to {Path.GetFileName(saveDialog.FileName)}";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error exporting logs: {ex.Message}", "Export Error", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void LogsDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
    {
        if (LogsDataGrid.SelectedItem is LogEntry selectedLog)
        {
            DisplayLogDetails(selectedLog);
        }
    }

    private void DisplayLogDetails(LogEntry log)
    {
        LogDetailsPanel.Children.Clear();

        // Basic info
        LogDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Timestamp: {log.Timestamp:yyyy-MM-dd HH:mm:ss.fff} UTC",
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 0, 0, 5)
        });

        LogDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Level: {log.Level}",
            Margin = new Thickness(0, 0, 0, 5)
        });

        LogDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Component: {log.Component}",
            Margin = new Thickness(0, 0, 0, 5)
        });

        LogDetailsPanel.Children.Add(new TextBlock
        {
            Text = $"Thread ID: {log.ThreadId}",
            Margin = new Thickness(0, 0, 0, 5)
        });

        LogDetailsPanel.Children.Add(new TextBlock
        {
            Text = "Message:",
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(0, 10, 0, 5)
        });

        LogDetailsPanel.Children.Add(new TextBox
        {
            Text = log.Message,
            IsReadOnly = true,
            TextWrapping = TextWrapping.Wrap,
            BorderThickness = new Thickness(0),
            Background = System.Windows.Media.Brushes.Transparent,
            Margin = new Thickness(0, 0, 0, 10)
        });

        // Context data
        if (log.Context != null)
        {
            LogDetailsPanel.Children.Add(new TextBlock
            {
                Text = "Context Data:",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 10, 0, 5)
            });

            try
            {
                var contextJson = System.Text.Json.JsonSerializer.Serialize(log.Context, 
                    new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
                
                LogDetailsPanel.Children.Add(new TextBox
                {
                    Text = contextJson,
                    IsReadOnly = true,
                    FontFamily = new System.Windows.Media.FontFamily("Consolas"),
                    FontSize = 10,
                    TextWrapping = TextWrapping.NoWrap,
                    VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                    HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                    Background = new System.Windows.Media.SolidColorBrush(System.Windows.Media.Color.FromRgb(248, 248, 248)),
                    Padding = new Thickness(5),
                    MaxHeight = 200
                });
            }
            catch
            {
                LogDetailsPanel.Children.Add(new TextBlock
                {
                    Text = log.Context.ToString(),
                    Margin = new Thickness(0, 0, 0, 5)
                });
            }
        }
    }

    private void CopyAll_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            Clipboard.SetText(RawLogTextBox.Text);
            StatusText.Text = "Raw log text copied to clipboard";
        }
        catch (Exception ex)
        {
            StatusText.Text = $"Error copying: {ex.Message}";
        }
    }

    private void SaveToFile_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var saveDialog = new SaveFileDialog
            {
                Title = "Save Raw Logs",
                Filter = "Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"raw_logs_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveDialog.ShowDialog() == true)
            {
                File.WriteAllText(saveDialog.FileName, RawLogTextBox.Text);
                StatusText.Text = $"Raw logs saved to {Path.GetFileName(saveDialog.FileName)}";
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error saving file: {ex.Message}", "Save Error", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void Search_TextChanged(object sender, TextChangedEventArgs e)
    {
        UpdateRawLogText();
    }

    protected override void OnClosed(EventArgs e)
    {
        _refreshTimer?.Stop();
        base.OnClosed(e);
    }
}
