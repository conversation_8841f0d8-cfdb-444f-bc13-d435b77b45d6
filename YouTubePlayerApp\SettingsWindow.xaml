<Window x:Class="YouTubePlayerApp.SettingsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:YouTubePlayerApp"
        mc:Ignorable="d"
        Title="Settings - YouTube Desktop Player" Height="500" Width="600"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Application Settings" 
                   FontSize="20" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Settings Content -->
        <TabControl Grid.Row="1">
            <TabItem Header="General">
                <StackPanel Margin="20">
                    <GroupBox Header="Startup" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="StartWithWindowsCheckBox" 
                                      Content="Start with Windows" Margin="0,5"/>
                            <CheckBox x:Name="StartFullScreenCheckBox" 
                                      Content="Start in full screen mode" Margin="0,5"/>
                            <CheckBox x:Name="RememberLastPositionCheckBox" 
                                      Content="Remember window position and size" Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Default Home Page" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <RadioButton x:Name="YouTubeHomeRadio" Content="YouTube Home" 
                                         IsChecked="True" Margin="0,5"/>
                            <RadioButton x:Name="YouTubeTrendingRadio" Content="YouTube Trending" 
                                         Margin="0,5"/>
                            <RadioButton x:Name="CustomUrlRadio" Content="Custom URL:" 
                                         Margin="0,5"/>
                            <TextBox x:Name="CustomUrlTextBox" 
                                     IsEnabled="{Binding IsChecked, ElementName=CustomUrlRadio}"
                                     Margin="20,5,0,5"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </TabItem>
            
            <TabItem Header="Video">
                <StackPanel Margin="20">
                    <GroupBox Header="Video Quality" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <TextBlock Text="Default video quality preference:" Margin="0,0,0,10"/>
                            <ComboBox x:Name="VideoQualityComboBox" SelectedIndex="0">
                                <ComboBoxItem Content="Auto"/>
                                <ComboBoxItem Content="144p"/>
                                <ComboBoxItem Content="240p"/>
                                <ComboBoxItem Content="360p"/>
                                <ComboBoxItem Content="480p"/>
                                <ComboBoxItem Content="720p"/>
                                <ComboBoxItem Content="1080p"/>
                                <ComboBoxItem Content="1440p"/>
                                <ComboBoxItem Content="2160p (4K)"/>
                            </ComboBox>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Playback" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="AutoplayCheckBox" 
                                      Content="Enable autoplay" IsChecked="True" Margin="0,5"/>
                            <CheckBox x:Name="MuteOnStartupCheckBox" 
                                      Content="Mute on startup" Margin="0,5"/>
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,5">
                                <TextBlock Text="Default volume: " VerticalAlignment="Center"/>
                                <Slider x:Name="VolumeSlider" Width="200" Minimum="0" Maximum="100" 
                                        Value="50" Margin="10,0,0,0"/>
                                <TextBlock Text="{Binding Value, ElementName=VolumeSlider, StringFormat={}{0:F0}%}" 
                                           VerticalAlignment="Center" Margin="10,0,0,0"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Quality Control" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="ForceVideoQualityCheckBox" 
                                      Content="Force specific video quality" Margin="0,5"
                                      Checked="ForceVideoQualityCheckBox_Changed" Unchecked="ForceVideoQualityCheckBox_Changed"/>
                            
                            <StackPanel x:Name="QualityControlPanel" Margin="20,5,0,5"
                                        IsEnabled="{Binding IsChecked, ElementName=ForceVideoQualityCheckBox}">
                                <StackPanel Orientation="Horizontal" Margin="0,5">
                                    <TextBlock Text="Video Quality:" Width="100" VerticalAlignment="Center"/>
                                    <ComboBox x:Name="ForcedVideoQualityComboBox" Width="200" SelectedIndex="0">
                                        <ComboBoxItem Content="Auto (YouTube Default)" Tag="auto"/>
                                        <ComboBoxItem Content="144p (Lowest)" Tag="144p"/>
                                        <ComboBoxItem Content="240p (Low)" Tag="240p"/>
                                        <ComboBoxItem Content="360p (Medium)" Tag="360p"/>
                                        <ComboBoxItem Content="480p (Standard)" Tag="480p"/>
                                        <ComboBoxItem Content="720p (HD)" Tag="720p"/>
                                        <ComboBoxItem Content="1080p (Full HD)" Tag="1080p"/>
                                        <ComboBoxItem Content="1440p (2K)" Tag="1440p"/>
                                        <ComboBoxItem Content="2160p (4K)" Tag="2160p"/>
                                    </ComboBox>
                                </StackPanel>
                                
                                <CheckBox x:Name="LockQualityControlsCheckBox" 
                                          Content="Lock quality controls (prevent manual changes)" Margin="0,8,0,3"/>
                                <CheckBox x:Name="HideQualityControlsCheckBox" 
                                          Content="Hide quality controls completely" Margin="0,3"/>
                                <CheckBox x:Name="OverrideUserQualityChangesCheckBox" 
                                          Content="Override any user quality changes" Margin="0,3"/>
                                
                                <TextBlock Text="⚠️ Quality enforcement is intended for bandwidth control and institutional use." 
                                           TextWrapping="Wrap" Margin="0,10,0,0" FontSize="10" 
                                           Foreground="Orange"/>
                            </StackPanel>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button x:Name="TestQualityButton" Content="Test Quality" 
                                        Width="100" Height="25" Margin="0,0,10,0"
                                        Click="TestQualityButton_Click"/>
                                <Button x:Name="ResetQualityButton" Content="Reset Quality" 
                                        Width="100" Height="25"
                                        Click="ResetQualityButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </TabItem>
            
            <TabItem Header="Privacy">
                <StackPanel Margin="20">
                    <GroupBox Header="Cookies and Data" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="ClearCookiesOnExitCheckBox" 
                                      Content="Clear cookies on exit" Margin="0,5"/>
                            <CheckBox x:Name="BlockThirdPartyCookiesCheckBox" 
                                      Content="Block third-party cookies" Margin="0,5"/>
                            <Button x:Name="ClearDataButton" Content="Clear All Data Now" 
                                    Width="150" HorizontalAlignment="Left" Margin="0,10,0,5"
                                    Click="ClearDataButton_Click"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Ad Blocking" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="BlockAdsCheckBox" 
                                      Content="Enable ad blocker" Margin="0,5" 
                                      Checked="BlockAdsCheckBox_Changed" Unchecked="BlockAdsCheckBox_Changed"/>
                            
                            <StackPanel x:Name="AdBlockingOptionsPanel" Margin="20,5,0,5"
                                        IsEnabled="{Binding IsChecked, ElementName=BlockAdsCheckBox}">
                                <CheckBox x:Name="BlockVideoAdsCheckBox" 
                                          Content="Block video ads (pre-roll, mid-roll, post-roll)" Margin="0,3"/>
                                <CheckBox x:Name="BlockBannerAdsCheckBox" 
                                          Content="Block banner and display ads" Margin="0,3"/>
                                <CheckBox x:Name="BlockTrackingScriptsCheckBox" 
                                          Content="Block tracking scripts and analytics" Margin="0,3"/>
                                <CheckBox x:Name="AutoSkipAdsCheckBox" 
                                          Content="Auto-skip ads when possible" Margin="0,3"/>
                                <CheckBox x:Name="ShowAdBlockStatsCheckBox" 
                                          Content="Show blocked content statistics" Margin="0,3"/>
                            </StackPanel>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button x:Name="ViewBlockedStatsButton" Content="View Statistics" 
                                        Width="120" Height="25" Margin="0,0,10,0"
                                        Click="ViewBlockedStatsButton_Click"/>
                                <Button x:Name="ResetAdBlockerButton" Content="Reset Ad Blocker" 
                                        Width="120" Height="25"
                                        Click="ResetAdBlockerButton_Click"/>
                            </StackPanel>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="Tracking" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="DoNotTrackCheckBox" 
                                      Content="Send 'Do Not Track' header" Margin="0,5"/>
                        </StackPanel>
                    </GroupBox>
                    
                    <GroupBox Header="YouTube Enhancements" Margin="0,0,0,20">
                        <StackPanel Margin="10">
                            <CheckBox x:Name="DisableAutoPreviewCheckBox" 
                                      Content="Disable auto preview on video thumbnails" Margin="0,5"
                                      Checked="DisableAutoPreviewCheckBox_Changed" Unchecked="DisableAutoPreviewCheckBox_Changed"/>
                            <TextBlock Text="📱 Reduces data usage by preventing automatic video previews when hovering over thumbnails" 
                                       TextWrapping="Wrap" Margin="20,2,0,0" FontSize="10" 
                                       Foreground="Gray"/>
                        </StackPanel>
                    </GroupBox>
                </StackPanel>
            </TabItem>
        </TabControl>
        
        <!-- Bottom Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="OkButton" Content="OK" Width="80" Height="30" 
                    IsDefault="True" Margin="0,0,10,0" Click="OkButton_Click"/>
            <Button x:Name="CancelButton" Content="Cancel" Width="80" Height="30" 
                    IsCancel="True" Click="CancelButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
