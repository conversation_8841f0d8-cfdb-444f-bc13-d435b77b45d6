using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Web.WebView2.Core;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private bool isFullScreen = false;
    private WindowState previousWindowState;
    private WindowStyle previousWindowStyle;
    private AdBlocker? adBlocker;
    private VideoQualityController? qualityController;

    public MainWindow()
    {
        InitializeComponent();
        InitializeWebView();
    }

    private async void InitializeWebView()
    {
        await YouTubeWebView.EnsureCoreWebView2Async();
        
        // Configure user agent for better YouTube compatibility
        YouTubeWebView.CoreWebView2.Settings.UserAgent = 
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
        
        // Enable features for better web compatibility
        YouTubeWebView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
        YouTubeWebView.CoreWebView2.Settings.IsWebMessageEnabled = true;
        YouTubeWebView.CoreWebView2.Settings.AreDevToolsEnabled = false;
        
        // Initialize ad blocker
        adBlocker = new AdBlocker();
        adBlocker.Initialize(YouTubeWebView.CoreWebView2);
        
        // Initialize video quality controller
        qualityController = new VideoQualityController();
        qualityController.Initialize(YouTubeWebView.CoreWebView2);
        
        // Apply additional YouTube-specific optimizations
        ApplyYouTubeOptimizations();
    }
    
    private async void ApplyYouTubeOptimizations()
    {
        try
        {
            // Disable notifications
            await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                Object.defineProperty(Notification, 'permission', {
                    get() { return 'denied'; }
                });
            ");
            
            // Apply ad blocker settings
            if (AppSettings.DoNotTrack)
            {
                await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                    Object.defineProperty(Navigator.prototype, 'doNotTrack', {
                        get() { return '1'; }
                    });
                ");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying YouTube optimizations: {ex.Message}");
        }
    }

    private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (e.IsSuccess)
        {
            Title = $"YouTube Desktop Player - {YouTubeWebView.CoreWebView2.DocumentTitle}";
            UpdateAdBlockStatus();
        }
    }

    private void UpdateAdBlockStatus()
    {
        if (AppSettings.ShowAdBlockStats && AppSettings.BlockAds && adBlocker != null)
        {
            var totalBlocked = adBlocker.GetTotalBlocked();
            AdBlockStatusText.Text = $"Ad Blocker: {totalBlocked} blocked";
            AdBlockStatusIndicator.Visibility = Visibility.Visible;
        }
        else
        {
            AdBlockStatusIndicator.Visibility = Visibility.Collapsed;
        }
    }

    // Navigation Menu Handlers
    private void Back_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoBack)
            YouTubeWebView.CoreWebView2.GoBack();
    }

    private void Forward_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoForward)
            YouTubeWebView.CoreWebView2.GoForward();
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Reload();
    }

    private void Home_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Navigate("https://www.youtube.com");
    }

    // View Menu Handlers
    private void FullScreen_Click(object sender, RoutedEventArgs e)
    {
        ToggleFullScreen();
    }

    private void ZoomIn_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor += 0.1;
    }

    private void ZoomOut_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = Math.Max(0.1, YouTubeWebView.ZoomFactor - 0.1);
    }

    private void ResetZoom_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = 1.0;
    }

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        var settingsWindow = new SettingsWindow();
        settingsWindow.Owner = this;
        settingsWindow.ShowDialog();
    }

    private void ToggleFullScreen()
    {
        if (!isFullScreen)
        {
            previousWindowState = WindowState;
            previousWindowStyle = WindowStyle;
            
            WindowStyle = WindowStyle.None;
            WindowState = WindowState.Maximized;
            isFullScreen = true;
        }
        else
        {
            WindowStyle = previousWindowStyle;
            WindowState = previousWindowState;
            isFullScreen = false;
        }
    }

    // Keyboard shortcuts
    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.F11)
        {
            ToggleFullScreen();
            e.Handled = true;
        }
        else if (e.Key == Key.F5)
        {
            YouTubeWebView.CoreWebView2.Reload();
            e.Handled = true;
        }
        
        base.OnKeyDown(e);
    }

    // Public method to access the ad blocker for statistics
    public AdBlocker? GetAdBlocker()
    {
        return adBlocker;
    }

    // Toggle ad blocker from settings
    public void ToggleAdBlocker(bool enable)
    {
        if (adBlocker != null)
        {
            if (enable)
            {
                adBlocker.EnableAdBlocking();
            }
            else
            {
                adBlocker.DisableAdBlocking();
            }
        }
    }

    // Public method to access the video quality controller
    public VideoQualityController? GetQualityController()
    {
        return qualityController;
    }

    // Update quality settings from settings window
    public async Task UpdateQualitySettings()
    {
        if (qualityController != null)
        {
            qualityController.UpdateSettings();
            await qualityController.RefreshQualitySettings();
        }
    }
}
