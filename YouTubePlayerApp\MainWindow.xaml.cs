using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Microsoft.Web.WebView2.Core;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private bool isFullScreen = false;
    private WindowState previousWindowState;
    private WindowStyle previousWindowStyle;
    private VideoSafeAdBlocker? videoSafeAdBlocker;
    private VideoQualityController? qualityController;
    private YouTubeEnhancer? youtubeEnhancer;
    private readonly DispatcherTimer _statsUpdateTimer;
    private readonly DateTime _applicationStartTime;

    public MainWindow()
    {
        _applicationStartTime = DateTime.Now;
        InitializeComponent();
        InitializeWebView();
        
        // Set up stats update timer
        _statsUpdateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(3)
        };
        _statsUpdateTimer.Tick += UpdateStatsDisplay;
        _statsUpdateTimer.Start();
        
        // Initialize performance monitoring
        PerformanceMonitor.Instance.LogCurrentState("Application Start");
    }

    protected override void OnClosed(EventArgs e)
    {
        try
        {
            // Dispose resources to prevent memory leaks
            videoSafeAdBlocker?.Dispose();
            qualityController?.Dispose();
            youtubeEnhancer?.Dispose();
            PerformanceMonitor.Instance.Dispose();
            SettingsCache.Instance.Dispose();
            
            PerformanceMonitor.Instance.LogCurrentState("Application Exit");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
        }
        
        // Stop timers and clean up resources
        _statsUpdateTimer?.Stop();
        
        base.OnClosed(e);
    }

    private async void InitializeWebView()
    {
        PerformanceMonitor.Instance.StartOperation("WebView.Initialize");
        try
        {
            await YouTubeWebView.EnsureCoreWebView2Async();
            
            // Configure user agent for better YouTube compatibility
            YouTubeWebView.CoreWebView2.Settings.UserAgent = 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
            
            // Enable features for better web compatibility and interaction
            YouTubeWebView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
            YouTubeWebView.CoreWebView2.Settings.IsWebMessageEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.AreDevToolsEnabled = false;
            
            // Critical settings for proper interaction
            YouTubeWebView.CoreWebView2.Settings.IsScriptEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.AreDefaultScriptDialogsEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.IsGeneralAutofillEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.IsPasswordAutosaveEnabled = false;
            
            // Ensure proper event handling and interaction
            YouTubeWebView.CoreWebView2.Settings.AreBrowserAcceleratorKeysEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.IsSwipeNavigationEnabled = false;
            
            // CRITICAL: Ensure all interaction features are enabled
            YouTubeWebView.CoreWebView2.Settings.AreDefaultContextMenusEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.AreDefaultScriptDialogsEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.IsGeneralAutofillEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.IsScriptEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.IsWebMessageEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
            
            // Enable media features
            YouTubeWebView.CoreWebView2.Settings.IsPasswordAutosaveEnabled = false;
            
            // Ensure proper permissions for media playback
            YouTubeWebView.CoreWebView2.PermissionRequested += OnPermissionRequested;
            
            // Initialize video-safe ad blocker
            videoSafeAdBlocker = new VideoSafeAdBlocker();
            videoSafeAdBlocker.Initialize(YouTubeWebView.CoreWebView2);
            
            // Initialize video quality controller
            qualityController = new VideoQualityController();
            qualityController.Initialize(YouTubeWebView.CoreWebView2);
            
            // Initialize YouTube enhancer
            youtubeEnhancer = new YouTubeEnhancer();
            youtubeEnhancer.Initialize(YouTubeWebView.CoreWebView2);
            
            // Add script to ensure video interaction works
            await InjectVideoInteractionScript();
            
            // Apply additional YouTube-specific optimizations
            ApplyYouTubeOptimizations();
        }
        finally
        {
            PerformanceMonitor.Instance.EndOperation("WebView.Initialize");
        }
    }
    
    private async void ApplyYouTubeOptimizations()
    {
        try
        {
            // Disable notifications
            await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                Object.defineProperty(Notification, 'permission', {
                    get() { return 'denied'; }
                });
            ");
            
            // Apply ad blocker settings
            if (AppSettings.DoNotTrack)
            {
                await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                    Object.defineProperty(Navigator.prototype, 'doNotTrack', {
                        get() { return '1'; }
                    });
                ");
            }

            // Force ad blocking CSS injection
            if (AppSettings.BlockAds)
            {
                await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                    // Immediate ad blocking CSS
                    const adBlockCSS = `
                        /* Video ads */
                        .video-ads, .ytp-ad-module, .ytp-ad-overlay-container,
                        .ytp-ad-image-overlay, .ytp-ad-text-overlay,
                        .ytp-ad-player-overlay, .ytp-ad-skip-button-container,
                        
                        /* Banner and display ads */
                        .ytd-promoted-video-renderer, .ytd-promoted-sparkles-web-renderer,
                        .ytd-banner-promo-renderer, .ytd-in-feed-ad-layout-renderer,
                        .ytd-display-ad-renderer, .ytd-companion-slot-renderer,
                        
                        /* Sidebar and companion ads */
                        #player-ads, #masthead-ad, #watch-sidebar-ads,
                        .ytd-action-companion-ad-renderer,
                        
                        /* Premium upsells */
                        .ytd-popup-container[dialog][role='dialog'],
                        .ytd-mealbar-promo-renderer, .ytd-premium-upsell-renderer,
                        .ytd-upsell-dialog-renderer, .ytd-background-promo-renderer,
                        
                        /* Shopping ads */
                        .ytd-product-details-renderer, .ytd-merch-shelf-renderer,
                        .ytd-shopping-shelf-renderer, .ytd-product-shelf-renderer,
                        
                        /* Generic ad patterns */
                        [class*='promoted'], [class*='sponsor'], [class*='advertisement'],
                        [id*='promoted'], [id*='sponsor'], [id*='advertisement'],
                        [data-is-ad='true'], [data-promoted='true'], [data-ad-status]
                        { display: none !important; visibility: hidden !important; }
                    `;
                    
                    const style = document.createElement('style');
                    style.textContent = adBlockCSS;
                    (document.head || document.documentElement).appendChild(style);
                ");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying YouTube optimizations: {ex.Message}");
        }
    }

    private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (e.IsSuccess)
        {
            Title = $"YouTube Desktop Player - {YouTubeWebView.CoreWebView2.DocumentTitle}";
            UpdateAdBlockStatus();
        }
    }

    private void UpdateAdBlockStatus()
    {
        // This method is now handled by UpdateStatsDisplay
        // Keep for backward compatibility if needed
        if (AppSettings.ShowAdBlockStats && AppSettings.BlockAds && videoSafeAdBlocker != null)
        {
            var totalBlocked = videoSafeAdBlocker.GetTotalBlocked();
            var stats = videoSafeAdBlocker.GetBlockedStats();
            
            SessionTotalText.Text = $"Session: {totalBlocked}";
            BlockedRequestsText.Text = $"Requests: {stats.GetValueOrDefault("Requests", 0)}";
            AdBlockStatusIndicator.Visibility = Visibility.Visible;
        }
        else
        {
            AdBlockStatusIndicator.Visibility = Visibility.Collapsed;
        }
    }

    private void OnPermissionRequested(object? sender, CoreWebView2PermissionRequestedEventArgs e)
    {
        // Allow necessary permissions for video playback but block tracking
        switch (e.PermissionKind)
        {
            case CoreWebView2PermissionKind.Microphone:
            case CoreWebView2PermissionKind.Camera:
                e.State = CoreWebView2PermissionState.Allow;
                break;
            case CoreWebView2PermissionKind.Geolocation:
                e.State = AppSettings.DoNotTrack ? CoreWebView2PermissionState.Deny : CoreWebView2PermissionState.Allow;
                break;
            case CoreWebView2PermissionKind.Notifications:
                e.State = CoreWebView2PermissionState.Deny; // Block notifications
                break;
            default:
                e.State = CoreWebView2PermissionState.Allow;
                break;
        }
    }

    // Navigation Menu Handlers
    private void Back_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoBack)
            YouTubeWebView.CoreWebView2.GoBack();
    }

    private void Forward_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoForward)
            YouTubeWebView.CoreWebView2.GoForward();
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Reload();
    }

    private void Home_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Navigate("https://www.youtube.com");
    }

    // View Menu Handlers
    private void FullScreen_Click(object sender, RoutedEventArgs e)
    {
        ToggleFullScreen();
    }

    private void ZoomIn_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor += 0.1;
    }

    private void ZoomOut_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = Math.Max(0.1, YouTubeWebView.ZoomFactor - 0.1);
    }

    private void ResetZoom_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = 1.0;
    }

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        var settingsWindow = new SettingsWindow();
        settingsWindow.Owner = this;
        settingsWindow.ShowDialog();
    }

    private void Performance_Click(object sender, RoutedEventArgs e)
    {
        var performanceWindow = new PerformanceBenchmarkWindow();
        performanceWindow.Owner = this;
        performanceWindow.Show();
    }

    private void MemoryProfiler_Click(object sender, RoutedEventArgs e)
    {
        var memoryProfilerWindow = new MemoryProfilerWindow();
        memoryProfilerWindow.Owner = this;
        memoryProfilerWindow.Show();
    }

    private void LogViewer_Click(object sender, RoutedEventArgs e)
    {
        var logViewerWindow = new LogViewerWindow();
        logViewerWindow.Owner = this;
        logViewerWindow.Show();
    }

    private async void DiagnoseAdBlocking_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var diagnostics = await AdBlockDiagnostics.RunComprehensiveDiagnosticsAsync(YouTubeWebView.CoreWebView2);
            
            var diagnosticsWindow = new Window
            {
                Title = "Ad Blocking Diagnostics",
                Width = 900,
                Height = 700,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(10)
            };
            
            var textBlock = new TextBlock
            {
                Text = diagnostics,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap
            };
            
            scrollViewer.Content = textBlock;
            diagnosticsWindow.Content = scrollViewer;
            diagnosticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running ad blocking diagnostics: {ex.Message}", "Diagnostics Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void ForceAdRemoval_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = await AdBlockDiagnostics.ForceAdRemovalAsync(YouTubeWebView.CoreWebView2);
            MessageBox.Show(result, "Force Ad Removal", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error during force ad removal: {ex.Message}", "Force Removal Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void TestAdBlocking_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var isEffective = await AdBlockDiagnostics.TestAdBlockingEffectivenessAsync(YouTubeWebView.CoreWebView2);
            var message = isEffective 
                ? "✅ Ad blocking is working effectively! No ads detected."
                : "⚠️ Ad blocking may not be fully effective. Some ads were detected.";
            
            MessageBox.Show(message, "Ad Blocking Test", MessageBoxButton.OK, 
                          isEffective ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error testing ad blocking: {ex.Message}", "Test Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void DiagnoseVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var diagnostics = await VideoPlaybackDiagnostics.RunVideoPlaybackTestAsync(YouTubeWebView.CoreWebView2);
            
            var diagnosticsWindow = new Window
            {
                Title = "Video Playback Diagnostics",
                Width = 1000,
                Height = 800,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(10)
            };
            
            var textBlock = new TextBlock
            {
                Text = diagnostics,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap
            };
            
            scrollViewer.Content = textBlock;
            diagnosticsWindow.Content = scrollViewer;
            diagnosticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running video playback diagnostics: {ex.Message}", "Diagnostics Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void FixVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = await VideoPlaybackDiagnostics.FixVideoPlaybackIssuesAsync(YouTubeWebView.CoreWebView2);
            MessageBox.Show(result, "Video Playback Fix", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error fixing video playback: {ex.Message}", "Fix Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void TestVideoPlayback_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var isWorking = await VideoPlaybackDiagnostics.TestVideoPlaybackFunctionalityAsync(YouTubeWebView.CoreWebView2);
            var message = isWorking 
                ? "✅ Video playback is working perfectly! All controls are functional."
                : "⚠️ Video playback issues detected. Some functionality may be impaired.";
            
            MessageBox.Show(message, "Video Playback Test", MessageBoxButton.OK, 
                          isWorking ? MessageBoxImage.Information : MessageBoxImage.Warning);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error testing video playback: {ex.Message}", "Test Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void RunFullTestSuite_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var testResults = await ComprehensiveTestSuite.RunFullTestSuiteAsync(YouTubeWebView.CoreWebView2);
            
            var testWindow = new Window
            {
                Title = "Comprehensive Test Suite Results",
                Width = 1100,
                Height = 900,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(15)
            };
            
            var textBlock = new TextBlock
            {
                Text = testResults,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 11,
                TextWrapping = TextWrapping.Wrap,
                LineHeight = 16
            };
            
            scrollViewer.Content = textBlock;
            testWindow.Content = scrollViewer;
            testWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running comprehensive test suite: {ex.Message}", "Test Suite Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void QuickHealthCheck_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var healthCheck = await ComprehensiveTestSuite.RunQuickHealthCheckAsync(YouTubeWebView.CoreWebView2);
            MessageBox.Show(healthCheck, "Quick Health Check", MessageBoxButton.OK, MessageBoxImage.Information);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running health check: {ex.Message}", "Health Check Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void DiagnoseVideo_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var diagnostics = await VideoInteractionDiagnostics.DiagnoseVideoInteractionAsync(YouTubeWebView.CoreWebView2);
            
            var diagnosticsWindow = new Window
            {
                Title = "Video Interaction Diagnostics",
                Width = 800,
                Height = 600,
                Owner = this,
                WindowStartupLocation = WindowStartupLocation.CenterOwner
            };
            
            var scrollViewer = new ScrollViewer
            {
                VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
                HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
                Padding = new Thickness(10)
            };
            
            var textBlock = new TextBlock
            {
                Text = diagnostics,
                FontFamily = new FontFamily("Consolas"),
                FontSize = 12,
                TextWrapping = TextWrapping.Wrap
            };
            
            scrollViewer.Content = textBlock;
            diagnosticsWindow.Content = scrollViewer;
            diagnosticsWindow.ShowDialog();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error running diagnostics: {ex.Message}", "Diagnostics Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async void FixVideoInteraction_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            var result = await VideoInteractionDiagnostics.AttemptVideoInteractionFixAsync(YouTubeWebView.CoreWebView2);
            
            if (result)
            {
                MessageBox.Show("Video interaction fixes applied successfully!\n\nTry clicking on videos now.", 
                              "Fix Applied", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            else
            {
                MessageBox.Show("No fixes were applied. The video interaction might already be working correctly.", 
                              "No Fixes Needed", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error applying fixes: {ex.Message}", "Fix Error", 
                          MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }



    private async Task InjectVideoInteractionScript()
    {
        var script = @"
            (function() {
                'use strict';
                console.log('[VideoInteraction] Ensuring video interaction functionality...');
                
                // Function to ensure video elements are clickable
                function ensureVideoInteraction() {
                    // Find all video elements
                    const videos = document.querySelectorAll('video');
                    videos.forEach(video => {
                        video.style.pointerEvents = 'auto';
                        video.style.cursor = 'pointer';
                        
                        // Ensure video can receive events
                        if (!video.hasAttribute('data-interaction-enabled')) {
                            video.setAttribute('data-interaction-enabled', 'true');
                            
                            // Add click event listener to ensure play/pause works
                            video.addEventListener('click', function(e) {
                                console.log('[VideoInteraction] Video clicked');
                                if (video.paused) {
                                    video.play().catch(err => console.log('Play failed:', err));
                                } else {
                                    video.pause();
                                }
                            }, true);
                        }
                    });
                    
                    // Ensure YouTube player controls are clickable
                    const playerControls = document.querySelectorAll('.ytp-chrome-bottom, .ytp-chrome-controls, .ytp-play-button, .ytp-large-play-button');
                    playerControls.forEach(control => {
                        control.style.pointerEvents = 'auto';
                        control.style.cursor = 'pointer';
                        control.style.zIndex = '9999';
                    });
                    
                    // Ensure video thumbnails are clickable
                    const thumbnails = document.querySelectorAll('ytd-thumbnail, .ytd-thumbnail, #thumbnail');
                    thumbnails.forEach(thumb => {
                        thumb.style.pointerEvents = 'auto';
                        thumb.style.cursor = 'pointer';
                    });
                    
                    // Ensure video titles are clickable
                    const titles = document.querySelectorAll('#video-title, .ytd-video-meta-block, a#video-title-link');
                    titles.forEach(title => {
                        title.style.pointerEvents = 'auto';
                        title.style.cursor = 'pointer';
                    });
                }
                
                // Run immediately
                ensureVideoInteraction();
                
                // Run when DOM changes
                const observer = new MutationObserver(function(mutations) {
                    let shouldCheck = false;
                    mutations.forEach(mutation => {
                        if (mutation.addedNodes.length > 0) {
                            shouldCheck = true;
                        }
                    });
                    
                    if (shouldCheck) {
                        setTimeout(ensureVideoInteraction, 100);
                    }
                });
                
                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
                
                // Run periodically to ensure functionality
                setInterval(ensureVideoInteraction, 5000);
                
                console.log('[VideoInteraction] Video interaction script initialized');
            })();
        ";

        try
        {
            await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(script);
            AppLogger.Instance.LogInfo("MainWindow", "Video interaction script injected successfully");
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("MainWindow", ex, "Failed to inject video interaction script");
        }
    }

    private void UpdateStatsDisplay(object? sender, EventArgs e)
    {
        try
        {
            // Update ad blocking statistics
            if (videoSafeAdBlocker != null)
            {
                var stats = videoSafeAdBlocker.GetBlockedStats();
                var totalBlocked = videoSafeAdBlocker.GetTotalBlocked();
                
                Dispatcher.Invoke(() =>
                {
                    BlockedRequestsText.Text = $"Requests: {stats.GetValueOrDefault("Requests", 0)}";
                    BlockedElementsText.Text = $"Elements: {stats.GetValueOrDefault("Elements", 0)}";
                    SessionTotalText.Text = $"Session: {totalBlocked}";
                    
                    // Update status indicator color based on activity
                    if (totalBlocked > 0)
                    {
                        AdBlockerStatusIndicator.Fill = System.Windows.Media.Brushes.LimeGreen;
                    }
                    else
                    {
                        AdBlockerStatusIndicator.Fill = System.Windows.Media.Brushes.Orange;
                    }
                });
            }
            
            // Update performance metrics
            if (AppSettings.ShowPerformanceIndicator)
            {
                var metrics = PerformanceMonitor.Instance.GetCurrentMetrics();
                var uptime = DateTime.Now - _applicationStartTime;
                
                Dispatcher.Invoke(() =>
                {
                    MemoryUsageText.Text = $"Memory: {metrics.CurrentMemoryMB:F0} MB";
                    UptimeText.Text = $"Uptime: {uptime.TotalMinutes:F0}m";
                    PerformanceIndicator.Visibility = Visibility.Visible;
                });
            }
            else
            {
                Dispatcher.Invoke(() =>
                {
                    PerformanceIndicator.Visibility = Visibility.Collapsed;
                });
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("MainWindow", ex, "Error updating stats display");
        }
    }

    private void ToggleFullScreen()
    {
        if (!isFullScreen)
        {
            previousWindowState = WindowState;
            previousWindowStyle = WindowStyle;
            
            WindowStyle = WindowStyle.None;
            WindowState = WindowState.Maximized;
            isFullScreen = true;
        }
        else
        {
            WindowStyle = previousWindowStyle;
            WindowState = previousWindowState;
            isFullScreen = false;
        }
    }

    // Keyboard shortcuts
    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.F11)
        {
            ToggleFullScreen();
            e.Handled = true;
        }
        else if (e.Key == Key.F5)
        {
            YouTubeWebView.CoreWebView2.Reload();
            e.Handled = true;
        }
        
        base.OnKeyDown(e);
    }

    // Toggle ad blocker from settings
    public void ToggleAdBlocker(bool enable)
    {
        if (videoSafeAdBlocker != null)
        {
            if (enable)
            {
                videoSafeAdBlocker.EnableAdBlocking();
            }
            else
            {
                videoSafeAdBlocker.DisableAdBlocking();
            }
        }
    }

    // Public method to access the video quality controller
    public VideoQualityController? GetQualityController()
    {
        return qualityController;
    }

    // Public method to access the video-safe ad blocker
    public VideoSafeAdBlocker? GetVideoSafeAdBlocker()
    {
        return videoSafeAdBlocker;
    }

    // Public method to access the YouTube enhancer
    public YouTubeEnhancer? GetYouTubeEnhancer()
    {
        return youtubeEnhancer;
    }

    // Update quality settings from settings window
    public async Task UpdateQualitySettings()
    {
        if (qualityController != null)
        {
            qualityController.UpdateSettings();
            await qualityController.RefreshQualitySettings();
            
            // Force immediate application if quality forcing is enabled
            if (AppSettings.ForceVideoQuality)
            {
                await qualityController.ForceQualityApplication();
            }
        }
        
        // Also update YouTube enhancer settings
        if (youtubeEnhancer != null)
        {
            await youtubeEnhancer.UpdateSettings();
        }
    }

    // Force quality application (can be called from settings or manually)
    public async Task ForceQualityApplication()
    {
        if (qualityController != null)
        {
            await qualityController.ForceQualityApplication();
        }
    }
}
