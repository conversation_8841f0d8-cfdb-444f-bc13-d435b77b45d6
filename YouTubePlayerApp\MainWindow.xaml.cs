using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;
using Microsoft.Web.WebView2.Core;

namespace YouTubePlayerApp;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private bool isFullScreen = false;
    private WindowState previousWindowState;
    private WindowStyle previousWindowStyle;
    private AdBlocker? adBlocker;
    private VideoQualityController? qualityController;
    private YouTubeEnhancer? youtubeEnhancer;
    private readonly DispatcherTimer _statsUpdateTimer;
    private readonly DateTime _applicationStartTime;

    public MainWindow()
    {
        _applicationStartTime = DateTime.Now;
        InitializeComponent();
        InitializeWebView();
        
        // Set up stats update timer
        _statsUpdateTimer = new DispatcherTimer
        {
            Interval = TimeSpan.FromSeconds(3)
        };
        _statsUpdateTimer.Tick += UpdateStatsDisplay;
        _statsUpdateTimer.Start();
        
        // Initialize performance monitoring
        PerformanceMonitor.Instance.LogCurrentState("Application Start");
    }

    protected override void OnClosed(EventArgs e)
    {
        try
        {
            // Dispose resources to prevent memory leaks
            adBlocker?.Dispose();
            qualityController?.Dispose();
            youtubeEnhancer?.Dispose();
            PerformanceMonitor.Instance.Dispose();
            SettingsCache.Instance.Dispose();
            
            PerformanceMonitor.Instance.LogCurrentState("Application Exit");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error during cleanup: {ex.Message}");
        }
        
        // Stop timers and clean up resources
        _statsUpdateTimer?.Stop();
        
        base.OnClosed(e);
    }

    private async void InitializeWebView()
    {
        PerformanceMonitor.Instance.StartOperation("WebView.Initialize");
        try
        {
            await YouTubeWebView.EnsureCoreWebView2Async();
            
            // Configure user agent for better YouTube compatibility
            YouTubeWebView.CoreWebView2.Settings.UserAgent = 
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36";
            
            // Enable features for better web compatibility
            YouTubeWebView.CoreWebView2.Settings.AreHostObjectsAllowed = true;
            YouTubeWebView.CoreWebView2.Settings.IsWebMessageEnabled = true;
            YouTubeWebView.CoreWebView2.Settings.AreDevToolsEnabled = false;
            
            // Initialize ad blocker
            adBlocker = new AdBlocker();
            adBlocker.Initialize(YouTubeWebView.CoreWebView2);
            
            // Initialize video quality controller
            qualityController = new VideoQualityController();
            qualityController.Initialize(YouTubeWebView.CoreWebView2);
            
            // Initialize YouTube enhancer
            youtubeEnhancer = new YouTubeEnhancer();
            youtubeEnhancer.Initialize(YouTubeWebView.CoreWebView2);
            
            // Apply additional YouTube-specific optimizations
            ApplyYouTubeOptimizations();
        }
        finally
        {
            PerformanceMonitor.Instance.EndOperation("WebView.Initialize");
        }
    }
    
    private async void ApplyYouTubeOptimizations()
    {
        try
        {
            // Disable notifications
            await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                Object.defineProperty(Notification, 'permission', {
                    get() { return 'denied'; }
                });
            ");
            
            // Apply ad blocker settings
            if (AppSettings.DoNotTrack)
            {
                await YouTubeWebView.CoreWebView2.AddScriptToExecuteOnDocumentCreatedAsync(@"
                    Object.defineProperty(Navigator.prototype, 'doNotTrack', {
                        get() { return '1'; }
                    });
                ");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error applying YouTube optimizations: {ex.Message}");
        }
    }

    private void WebView_NavigationCompleted(object sender, CoreWebView2NavigationCompletedEventArgs e)
    {
        if (e.IsSuccess)
        {
            Title = $"YouTube Desktop Player - {YouTubeWebView.CoreWebView2.DocumentTitle}";
            UpdateAdBlockStatus();
        }
    }

    private void UpdateAdBlockStatus()
    {
        // This method is now handled by UpdateStatsDisplay
        // Keep for backward compatibility if needed
        if (AppSettings.ShowAdBlockStats && AppSettings.BlockAds && adBlocker != null)
        {
            var totalBlocked = adBlocker.GetTotalBlocked();
            var stats = adBlocker.GetBlockedStats();
            
            SessionTotalText.Text = $"Session: {totalBlocked}";
            BlockedRequestsText.Text = $"Requests: {stats.GetValueOrDefault("Requests", 0)}";
            AdBlockStatusIndicator.Visibility = Visibility.Visible;
        }
        else
        {
            AdBlockStatusIndicator.Visibility = Visibility.Collapsed;
        }
    }

    // Navigation Menu Handlers
    private void Back_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoBack)
            YouTubeWebView.CoreWebView2.GoBack();
    }

    private void Forward_Click(object sender, RoutedEventArgs e)
    {
        if (YouTubeWebView.CoreWebView2.CanGoForward)
            YouTubeWebView.CoreWebView2.GoForward();
    }

    private void Refresh_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Reload();
    }

    private void Home_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.CoreWebView2.Navigate("https://www.youtube.com");
    }

    // View Menu Handlers
    private void FullScreen_Click(object sender, RoutedEventArgs e)
    {
        ToggleFullScreen();
    }

    private void ZoomIn_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor += 0.1;
    }

    private void ZoomOut_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = Math.Max(0.1, YouTubeWebView.ZoomFactor - 0.1);
    }

    private void ResetZoom_Click(object sender, RoutedEventArgs e)
    {
        YouTubeWebView.ZoomFactor = 1.0;
    }

    private void Settings_Click(object sender, RoutedEventArgs e)
    {
        var settingsWindow = new SettingsWindow();
        settingsWindow.Owner = this;
        settingsWindow.ShowDialog();
    }

    private void Performance_Click(object sender, RoutedEventArgs e)
    {
        var performanceWindow = new PerformanceBenchmarkWindow();
        performanceWindow.Owner = this;
        performanceWindow.Show();
    }

    private void MemoryProfiler_Click(object sender, RoutedEventArgs e)
    {
        var memoryProfilerWindow = new MemoryProfilerWindow();
        memoryProfilerWindow.Owner = this;
        memoryProfilerWindow.Show();
    }

    private void LogViewer_Click(object sender, RoutedEventArgs e)
    {
        var logViewerWindow = new LogViewerWindow();
        logViewerWindow.Owner = this;
        logViewerWindow.Show();
    }

    private void UpdateStatsDisplay(object? sender, EventArgs e)
    {
        try
        {
            // Update ad blocking statistics
            if (adBlocker != null)
            {
                var stats = adBlocker.GetBlockedStats();
                var totalBlocked = adBlocker.GetTotalBlocked();
                
                Dispatcher.Invoke(() =>
                {
                    BlockedRequestsText.Text = $"Requests: {stats.GetValueOrDefault("Requests", 0)}";
                    BlockedElementsText.Text = $"Elements: {stats.GetValueOrDefault("Elements", 0)}";
                    SessionTotalText.Text = $"Session: {totalBlocked}";
                    
                    // Update status indicator color based on activity
                    if (totalBlocked > 0)
                    {
                        AdBlockerStatusIndicator.Fill = System.Windows.Media.Brushes.LimeGreen;
                    }
                    else
                    {
                        AdBlockerStatusIndicator.Fill = System.Windows.Media.Brushes.Orange;
                    }
                });
            }
            
            // Update performance metrics
            if (AppSettings.ShowPerformanceIndicator)
            {
                var metrics = PerformanceMonitor.Instance.GetCurrentMetrics();
                var uptime = DateTime.Now - _applicationStartTime;
                
                Dispatcher.Invoke(() =>
                {
                    MemoryUsageText.Text = $"Memory: {metrics.CurrentMemoryMB:F0} MB";
                    UptimeText.Text = $"Uptime: {uptime.TotalMinutes:F0}m";
                    PerformanceIndicator.Visibility = Visibility.Visible;
                });
            }
            else
            {
                Dispatcher.Invoke(() =>
                {
                    PerformanceIndicator.Visibility = Visibility.Collapsed;
                });
            }
        }
        catch (Exception ex)
        {
            AppLogger.Instance.LogError("MainWindow", ex, "Error updating stats display");
        }
    }

    private void ToggleFullScreen()
    {
        if (!isFullScreen)
        {
            previousWindowState = WindowState;
            previousWindowStyle = WindowStyle;
            
            WindowStyle = WindowStyle.None;
            WindowState = WindowState.Maximized;
            isFullScreen = true;
        }
        else
        {
            WindowStyle = previousWindowStyle;
            WindowState = previousWindowState;
            isFullScreen = false;
        }
    }

    // Keyboard shortcuts
    protected override void OnKeyDown(KeyEventArgs e)
    {
        if (e.Key == Key.F11)
        {
            ToggleFullScreen();
            e.Handled = true;
        }
        else if (e.Key == Key.F5)
        {
            YouTubeWebView.CoreWebView2.Reload();
            e.Handled = true;
        }
        
        base.OnKeyDown(e);
    }

    // Public method to access the ad blocker for statistics
    public AdBlocker? GetAdBlocker()
    {
        return adBlocker;
    }

    // Toggle ad blocker from settings
    public void ToggleAdBlocker(bool enable)
    {
        if (adBlocker != null)
        {
            if (enable)
            {
                adBlocker.EnableAdBlocking();
            }
            else
            {
                adBlocker.DisableAdBlocking();
            }
        }
    }

    // Public method to access the video quality controller
    public VideoQualityController? GetQualityController()
    {
        return qualityController;
    }

    // Public method to access the YouTube enhancer
    public YouTubeEnhancer? GetYouTubeEnhancer()
    {
        return youtubeEnhancer;
    }

    // Update quality settings from settings window
    public async Task UpdateQualitySettings()
    {
        if (qualityController != null)
        {
            qualityController.UpdateSettings();
            await qualityController.RefreshQualitySettings();
            
            // Force immediate application if quality forcing is enabled
            if (AppSettings.ForceVideoQuality)
            {
                await qualityController.ForceQualityApplication();
            }
        }
        
        // Also update YouTube enhancer settings
        if (youtubeEnhancer != null)
        {
            await youtubeEnhancer.UpdateSettings();
        }
    }

    // Force quality application (can be called from settings or manually)
    public async Task ForceQualityApplication()
    {
        if (qualityController != null)
        {
            await qualityController.ForceQualityApplication();
        }
    }
}
