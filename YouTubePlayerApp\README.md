# YouTube Desktop Player

A Windows desktop application that embeds YouTube.com in a native application window using WebView2.

## Features

- **Native Windows Experience**: Built with WPF and WebView2 for optimal performance
- **Full YouTube Functionality**: Complete access to YouTube features including video playback, playlists, and user accounts
- **Browser-like Navigation**: Back, forward, refresh, and home navigation controls
- **Fullscreen Support**: Press F11 or use the menu to toggle fullscreen mode
- **Zoom Controls**: Zoom in/out and reset zoom functionality
- **Settings Panel**: Configurable options for startup behavior, video quality, and privacy
- **Keyboard Shortcuts**: F11 for fullscreen, F5 for refresh
- **🚫 Powerful Ad Blocker**: Multi-layered ad blocking system with advanced features:
  - **Video Ad Blocking**: Removes pre-roll, mid-roll, and post-roll advertisements
  - **Banner Ad Removal**: Hides display ads and promotional content
  - **Tracking Protection**: Blocks analytics and tracking scripts
  - **Auto-Skip**: Automatically skips ads when possible
  - **Real-time Statistics**: View blocked content statistics
  - **Customizable Settings**: Granular control over blocking methods
- **📺 Video Quality Control**: Enterprise-grade quality enforcement system:
  - **Force Specific Quality**: Lock videos to any quality from 144p to 4K
  - **Quality Locking**: Prevent users from changing video quality manually
  - **UI Control Hiding**: Completely hide YouTube's quality selection interface
  - **Override Protection**: Automatically revert any quality changes back to enforced setting
  - **Bandwidth Management**: Perfect for institutional and corporate environments

## Requirements

- Windows 10 version 1903+ or Windows 11
- .NET 8.0 Runtime
- WebView2 Runtime (usually pre-installed on modern Windows)

## Technology Stack

- **.NET 8 WPF**: Native Windows UI framework
- **Microsoft WebView2**: Chromium-based web engine for excellent YouTube compatibility
- **C#**: Primary development language

## Building and Running

### Prerequisites

1. Install [.NET 8 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
2. Ensure WebView2 Runtime is installed (check Windows Update)

### Build Commands

```bash
# Restore dependencies
dotnet restore

# Build the application
dotnet build

# Run the application
dotnet run

# Build for release
dotnet build --configuration Release

# Publish as single file executable
dotnet publish --configuration Release --self-contained true --runtime win-x64 --output ./publish
```

## Project Structure

```
YouTubePlayerApp/
├── MainWindow.xaml          # Main application window UI
├── MainWindow.xaml.cs       # Main window logic and WebView2 integration
├── SettingsWindow.xaml      # Settings dialog UI
├── SettingsWindow.xaml.cs   # Settings dialog logic
├── AppSettings.cs           # Application settings management
├── App.xaml                 # Application-level resources
├── App.xaml.cs              # Application startup logic
└── YouTubePlayerApp.csproj  # Project configuration
```

## Configuration

Settings are automatically saved to:
`%APPDATA%\YouTubePlayerApp\settings.json`

Available settings include:
- Startup behavior (start with Windows, fullscreen, window position)
- Home page preferences (YouTube home, trending, or custom URL)
- Video quality defaults
- Privacy options (cookie management, tracking)
- **Ad blocking configuration**:
  - Enable/disable ad blocker
  - Selective blocking (video ads, banners, tracking)
  - Auto-skip functionality
  - Statistics display
  - Reset and clear options
- **Video quality control**:
  - Force specific video quality (144p to 4K)
  - Lock quality controls to prevent user changes
  - Hide YouTube's quality selection interface
  - Override user quality change attempts
  - Continuous quality monitoring and enforcement

## Key Features Implementation

### WebView2 Integration
- Uses Microsoft's WebView2 control for modern web compatibility
- Custom user agent string for optimal YouTube experience
- Proper audio/video codec support through Chromium engine

### Navigation Controls
- Browser-style back/forward navigation
- Refresh and home page functionality
- Fullscreen toggle with F11 key support

### Settings Framework
- Extensible settings system using JSON serialization
- Organized into General, Video, and Privacy categories
- Persistent storage in user's AppData folder

### Ad Blocking System
- **Multi-layered blocking approach**:
  - URL pattern filtering blocks ad domains at the network level
  - CSS injection hides ad elements on the page
  - JavaScript injection removes dynamic ads and auto-skips
  - Real-time DOM monitoring for newly added ad content
- **YouTube-specific optimizations**:
  - Targets YouTube's ad delivery systems
  - Blocks video ads, banner ads, and promotional content
  - Prevents tracking and analytics collection
- **User control and transparency**:
  - Granular settings for different blocking methods
  - Real-time statistics and blocked content tracking
  - Optional status indicator showing blocked content count

### Video Quality Control System
- **Quality enforcement mechanisms**:
  - JavaScript injection to override YouTube's quality selection
  - Continuous monitoring to prevent quality changes
  - DOM manipulation to hide or disable quality controls
  - Timer-based quality restoration for persistent enforcement
- **Enterprise-grade control**:
  - Support for all YouTube quality levels (144p to 4K)
  - Configurable override behavior for user attempts to change quality
  - Optional complete hiding of YouTube's settings interface
  - Bandwidth management capabilities for institutional use
- **Seamless integration**:
  - Works across all YouTube videos and navigation
  - Persists quality settings across browser sessions
  - Minimal impact on video playback performance
  - Compatible with YouTube's dynamic content loading

## Future Enhancement Opportunities

1. **Picture-in-Picture Mode**: Floating mini-player window
2. **Custom Themes**: Dark/light mode toggle
3. **Download Integration**: Video download capabilities
4. **Playlist Management**: Local playlist creation and management
5. **Keyboard Shortcuts**: Custom hotkey configuration
6. **System Tray Integration**: Minimize to system tray functionality

## Troubleshooting

### WebView2 Issues
If the application fails to load YouTube:
1. Ensure WebView2 Runtime is installed
2. Check Windows Update for the latest version
3. Clear application data through Settings > Privacy > Clear All Data

### Performance Issues
- Reduce zoom level if videos lag
- Close other browser instances to free up resources
- Check video quality settings in the Settings panel

## Development Notes

This application demonstrates:
- Modern Windows desktop development with WPF
- WebView2 integration for web content embedding
- Settings management and persistence
- Clean MVVM-friendly architecture for future enhancements

The codebase is structured for easy extension and maintenance, with clear separation between UI, business logic, and configuration management.
