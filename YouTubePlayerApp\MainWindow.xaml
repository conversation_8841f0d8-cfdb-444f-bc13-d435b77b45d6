<Window x:Class="YouTubePlayerApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        xmlns:local="clr-namespace:YouTubePlayerApp"
        mc:Ignorable="d"
        Title="YouTube Desktop Player" Height="800" Width="1200" 
        WindowStartupLocation="CenterScreen" WindowState="Maximized">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Top Menu Bar -->
        <Menu Grid.Row="0" Background="#f1f1f1">
            <MenuItem Header="_View">
                <MenuItem Header="_Full Screen" Click="FullScreen_Click"/>
                <Separator/>
                <MenuItem Header="_Zoom In" Click="ZoomIn_Click"/>
                <MenuItem Header="_Zoom Out" Click="ZoomOut_Click"/>
                <MenuItem Header="_Reset Zoom" Click="ResetZoom_Click"/>
            </MenuItem>
            <MenuItem Header="_Navigation">
                <MenuItem Header="_Back" Click="Back_Click"/>
                <MenuItem Header="_Forward" Click="Forward_Click"/>
                <MenuItem Header="_Refresh" Click="Refresh_Click"/>
                <MenuItem Header="_Home" Click="Home_Click"/>
            </MenuItem>
            <MenuItem Header="_Settings" Click="Settings_Click"/>
            <MenuItem Header="_Performance" Click="Performance_Click"/>
        </Menu>
        
        <!-- Main WebView2 Control -->
        <Grid Grid.Row="1">
            <wv2:WebView2 x:Name="YouTubeWebView" 
                          Source="https://www.youtube.com"
                          NavigationCompleted="WebView_NavigationCompleted"/>
            
            <!-- Ad Block Status Indicator -->
            <Border x:Name="AdBlockStatusIndicator" 
                    Background="#80000000" 
                    CornerRadius="5" 
                    Padding="10,5" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Top" 
                    Margin="20"
                    Visibility="Collapsed">
                <StackPanel Orientation="Horizontal">
                    <Ellipse Width="8" Height="8" Fill="LimeGreen" Margin="0,0,5,0"/>
                    <TextBlock x:Name="AdBlockStatusText" 
                               Text="Ad Blocker: 0 blocked" 
                               Foreground="White" 
                               FontSize="12"/>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
