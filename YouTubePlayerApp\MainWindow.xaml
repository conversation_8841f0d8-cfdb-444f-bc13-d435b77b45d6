<Window x:Class="YouTubePlayerApp.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:wv2="clr-namespace:Microsoft.Web.WebView2.Wpf;assembly=Microsoft.Web.WebView2.Wpf"
        xmlns:local="clr-namespace:YouTubePlayerApp"
        mc:Ignorable="d"
        Title="YouTube Desktop Player" Height="800" Width="1200" 
        WindowStartupLocation="CenterScreen" WindowState="Maximized">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Top Menu Bar -->
        <Menu Grid.Row="0" Background="#f1f1f1">
            <MenuItem Header="_View">
                <MenuItem Header="_Full Screen" Click="FullScreen_Click"/>
                <Separator/>
                <MenuItem Header="_Zoom In" Click="ZoomIn_Click"/>
                <MenuItem Header="_Zoom Out" Click="ZoomOut_Click"/>
                <MenuItem Header="_Reset Zoom" Click="ResetZoom_Click"/>
            </MenuItem>
            <MenuItem Header="_Navigation">
                <MenuItem Header="_Back" Click="Back_Click"/>
                <MenuItem Header="_Forward" Click="Forward_Click"/>
                <MenuItem Header="_Refresh" Click="Refresh_Click"/>
                <MenuItem Header="_Home" Click="Home_Click"/>
            </MenuItem>
            <MenuItem Header="_Tools">
                <MenuItem Header="_Settings" Click="Settings_Click"/>
                <MenuItem Header="_Performance" Click="Performance_Click"/>
                <MenuItem Header="_Memory Profiler" Click="MemoryProfiler_Click"/>
                <MenuItem Header="_Log Viewer" Click="LogViewer_Click"/>
                <Separator/>
                <MenuItem Header="_Diagnose Video Issues" Click="DiagnoseVideo_Click"/>
                <MenuItem Header="_Fix Video Interaction" Click="FixVideoInteraction_Click"/>
            </MenuItem>
        </Menu>
        
        <!-- Main WebView2 Control -->
        <Grid Grid.Row="1">
            <wv2:WebView2 x:Name="YouTubeWebView" 
                          Source="https://www.youtube.com"
                          NavigationCompleted="WebView_NavigationCompleted"/>
            
            <!-- Enhanced Ad Block Status Indicator -->
            <Border x:Name="AdBlockStatusIndicator" 
                    Background="#90000000" 
                    CornerRadius="8" 
                    Padding="12,8" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Top" 
                    Margin="20"
                    Visibility="Visible"
                    BorderBrush="#40FFFFFF"
                    BorderThickness="1">
                <StackPanel>
                    <!-- Header with status -->
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                        <Ellipse x:Name="AdBlockerStatusIndicator" Width="10" Height="10" Fill="LimeGreen" Margin="0,0,8,0"/>
                        <TextBlock Text="Ad Blocker" 
                                   Foreground="White" 
                                   FontSize="11" 
                                   FontWeight="SemiBold"/>
                    </StackPanel>
                    
                    <!-- Statistics -->
                    <StackPanel>
                        <TextBlock x:Name="BlockedRequestsText" 
                                   Text="Requests: 0" 
                                   Foreground="#E0FFFFFF" 
                                   FontSize="10"
                                   Margin="0,1"/>
                        <TextBlock x:Name="BlockedElementsText" 
                                   Text="Elements: 0" 
                                   Foreground="#E0FFFFFF" 
                                   FontSize="10"
                                   Margin="0,1"/>
                        <TextBlock x:Name="SessionTotalText" 
                                   Text="Session: 0" 
                                   Foreground="#C0FFFFFF" 
                                   FontSize="9"
                                   Margin="0,3,0,0"/>
                    </StackPanel>
                </StackPanel>
            </Border>
            
            <!-- Performance Indicator (Bottom Right) -->
            <Border x:Name="PerformanceIndicator" 
                    Background="#90000000" 
                    CornerRadius="8" 
                    Padding="10,6" 
                    HorizontalAlignment="Right" 
                    VerticalAlignment="Bottom" 
                    Margin="20"
                    Visibility="Collapsed"
                    BorderBrush="#40FFFFFF"
                    BorderThickness="1">
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="📊" FontSize="12" Margin="0,0,5,0"/>
                    <StackPanel>
                        <TextBlock x:Name="MemoryUsageText" 
                                   Text="Memory: 0 MB" 
                                   Foreground="White" 
                                   FontSize="9"/>
                        <TextBlock x:Name="UptimeText" 
                                   Text="Uptime: 0m" 
                                   Foreground="#C0FFFFFF" 
                                   FontSize="8"/>
                    </StackPanel>
                </StackPanel>
            </Border>
        </Grid>
    </Grid>
</Window>
