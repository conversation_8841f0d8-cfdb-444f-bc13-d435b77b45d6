<Window x:Class="YouTubePlayerApp.AdBlockStatsWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:YouTubePlayerApp"
        mc:Ignorable="d"
        Title="Ad Blocker Statistics" Height="400" Width="500"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <TextBlock Grid.Row="0" Text="Ad Blocker Statistics" 
                   FontSize="18" FontWeight="Bold" Margin="0,0,0,20"/>
        
        <!-- Statistics Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <GroupBox Header="Blocking Summary" Margin="0,0,0,15">
                    <Grid Margin="15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Total Requests Blocked:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="TotalBlockedText" 
                                   Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Video Ads Blocked:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="VideoAdsBlockedText" 
                                   Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Banner Elements Removed:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="ElementsBlockedText" 
                                   Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="3" Grid.Column="0" Text="Tracking Scripts Blocked:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="3" Grid.Column="1" x:Name="TrackingBlockedText" 
                                   Text="0" FontWeight="Bold" VerticalAlignment="Center" Margin="0,5"/>
                    </Grid>
                </GroupBox>
                
                <GroupBox Header="Recently Blocked URLs" Margin="0,0,0,15">
                    <ListBox x:Name="BlockedUrlsList" Height="150" Margin="10"
                             ScrollViewer.HorizontalScrollBarVisibility="Auto">
                        <ListBox.ItemTemplate>
                            <DataTemplate>
                                <TextBlock Text="{Binding}" TextWrapping="NoWrap"/>
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                    </ListBox>
                </GroupBox>
                
                <GroupBox Header="Session Information" Margin="0,0,0,15">
                    <Grid Margin="15">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>
                        
                        <TextBlock Grid.Row="0" Grid.Column="0" Text="Session Started:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="0" Grid.Column="1" x:Name="SessionStartText" 
                                   Text="--" VerticalAlignment="Center" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="1" Grid.Column="0" Text="Ad Blocker Status:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="1" Grid.Column="1" x:Name="AdBlockerStatusText" 
                                   Text="Active" Foreground="Green" FontWeight="Bold" VerticalAlignment="Center" Margin="0,5"/>
                        
                        <TextBlock Grid.Row="2" Grid.Column="0" Text="Estimated Data Saved:" 
                                   VerticalAlignment="Center" Margin="0,5"/>
                        <TextBlock Grid.Row="2" Grid.Column="1" x:Name="DataSavedText" 
                                   Text="~0 KB" VerticalAlignment="Center" Margin="0,5"/>
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
        
        <!-- Bottom Buttons -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" 
                    HorizontalAlignment="Right" Margin="0,20,0,0">
            <Button x:Name="RefreshButton" Content="Refresh" Width="80" Height="30" 
                    Margin="0,0,10,0" Click="RefreshButton_Click"/>
            <Button x:Name="ClearStatsButton" Content="Clear Stats" Width="80" Height="30" 
                    Margin="0,0,10,0" Click="ClearStatsButton_Click"/>
            <Button x:Name="CloseButton" Content="Close" Width="80" Height="30" 
                    IsCancel="True" Click="CloseButton_Click"/>
        </StackPanel>
    </Grid>
</Window>
