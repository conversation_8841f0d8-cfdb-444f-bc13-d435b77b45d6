using System.Diagnostics;
using System.Windows;

namespace YouTubePlayerApp;

/// <summary>
/// Performance monitoring and benchmarking window
/// </summary>
public partial class PerformanceBenchmarkWindow : Window
{
    private readonly Timer _refreshTimer;

    public PerformanceBenchmarkWindow()
    {
        InitializeComponent();
        
        // Auto-refresh metrics every 2 seconds
        _refreshTimer = new Timer(RefreshMetrics, null, TimeSpan.Zero, TimeSpan.FromSeconds(2));
        
        Loaded += (s, e) => RefreshMetrics(null);
    }

    private void RefreshMetrics(object? state)
    {
        Dispatcher.Invoke(() =>
        {
            var metrics = PerformanceMonitor.Instance.GetCurrentMetrics();
            
            CurrentMemoryText.Text = $"{metrics.CurrentMemoryMB:F1} MB";
            PeakMemoryText.Text = $"{metrics.PeakMemoryMB:F1} MB";
            WorkingSetText.Text = $"{metrics.WorkingSetMB:F1} MB";
            PrivateBytesText.Text = $"{metrics.PrivateBytesMB:F1} MB";
            CpuUsageText.Text = $"{metrics.CpuUsagePercent:F1}%";
            UptimeText.Text = $"{metrics.UptimeMinutes:F1} min";
            Gen0GCText.Text = metrics.Gen0Collections.ToString();
            Gen1GCText.Text = metrics.Gen1Collections.ToString();
            Gen2GCText.Text = metrics.Gen2Collections.ToString();
        });
    }

    private void ForceGCButton_Click(object sender, RoutedEventArgs e)
    {
        PerformanceMonitor.Instance.ForceGarbageCollection();
        RefreshMetrics(null);
    }

    private void BenchmarkAdBlockerButton_Click(object sender, RoutedEventArgs e)
    {
        BenchmarkAdBlockerButton.IsEnabled = false;
        AdBlockerBenchmarkResult.Text = "Running benchmark...";
        
        Task.Run(() =>
        {
            var results = BenchmarkAdBlocker();
            
            Dispatcher.Invoke(() =>
            {
                AdBlockerBenchmarkResult.Text = results;
                BenchmarkAdBlockerButton.IsEnabled = true;
            });
        });
    }

    private void BenchmarkQualityControllerButton_Click(object sender, RoutedEventArgs e)
    {
        BenchmarkQualityControllerButton.IsEnabled = false;
        QualityControllerBenchmarkResult.Text = "Running benchmark...";
        
        Task.Run(() =>
        {
            var results = BenchmarkQualityController();
            
            Dispatcher.Invoke(() =>
            {
                QualityControllerBenchmarkResult.Text = results;
                BenchmarkQualityControllerButton.IsEnabled = true;
            });
        });
    }

    private void BenchmarkSettingsCacheButton_Click(object sender, RoutedEventArgs e)
    {
        BenchmarkSettingsCacheButton.IsEnabled = false;
        SettingsCacheBenchmarkResult.Text = "Running benchmark...";
        
        Task.Run(() =>
        {
            var results = BenchmarkSettingsCache();
            
            Dispatcher.Invoke(() =>
            {
                SettingsCacheBenchmarkResult.Text = results;
                BenchmarkSettingsCacheButton.IsEnabled = true;
            });
        });
    }

    private string BenchmarkAdBlocker()
    {
        const int iterations = 10000;
        var testUrls = new[]
        {
            "https://googleads.g.doubleclick.net/pagead/ads",
            "https://www.youtube.com/watch?v=test",
            "https://googlesyndication.com/safeframe/",
            "https://youtube.com/ptracking/test",
            "https://example.com/video.mp4"
        };

        var stopwatch = Stopwatch.StartNew();
        
        using (var adBlocker = new AdBlocker())
        {
            for (int i = 0; i < iterations; i++)
            {
                foreach (var url in testUrls)
                {
                    // Simulate URL blocking check
                    var shouldBlock = url.Contains("googleads") || url.Contains("googlesyndication") || url.Contains("ptracking");
                }
            }
        }
        
        stopwatch.Stop();
        
        var totalOperations = iterations * testUrls.Length;
        var avgTimePerCheck = (double)stopwatch.ElapsedMilliseconds / totalOperations;
        
        return $"Total: {stopwatch.ElapsedMilliseconds}ms\n" +
               $"Operations: {totalOperations:N0}\n" +
               $"Avg per check: {avgTimePerCheck:F4}ms\n" +
               $"Checks/sec: {1000 / avgTimePerCheck:F0}";
    }

    private string BenchmarkQualityController()
    {
        const int iterations = 1000;
        var qualities = VideoQualityController.GetAvailableQualities();

        var stopwatch = Stopwatch.StartNew();
        
        using (var controller = new VideoQualityController())
        {
            for (int i = 0; i < iterations; i++)
            {
                foreach (var quality in qualities)
                {
                    var displayName = VideoQualityController.GetQualityDisplayName(quality);
                }
            }
        }
        
        stopwatch.Stop();
        
        var totalOperations = iterations * qualities.Length;
        var avgTimePerOperation = (double)stopwatch.ElapsedMilliseconds / totalOperations;
        
        return $"Total: {stopwatch.ElapsedMilliseconds}ms\n" +
               $"Operations: {totalOperations:N0}\n" +
               $"Avg per operation: {avgTimePerOperation:F4}ms\n" +
               $"Operations/sec: {1000 / avgTimePerOperation:F0}";
    }

    private string BenchmarkSettingsCache()
    {
        const int iterations = 100000;
        var cache = SettingsCache.Instance;
        
        var stopwatch = Stopwatch.StartNew();
        
        // Test cached property access performance
        for (int i = 0; i < iterations; i++)
        {
            var blockAds = cache.BlockAds;
            var forceQuality = cache.ForceVideoQuality;
            var forcedQuality = cache.ForcedVideoQuality;
            var showStats = cache.ShowAdBlockStats;
        }
        
        stopwatch.Stop();
        
        var avgTimePerAccess = (double)stopwatch.ElapsedTicks / iterations / 10000.0; // Convert to milliseconds
        
        return $"Total: {stopwatch.ElapsedMilliseconds}ms\n" +
               $"Property accesses: {iterations * 4:N0}\n" +
               $"Avg per access: {avgTimePerAccess:F6}ms\n" +
               $"Accesses/sec: {1000 / avgTimePerAccess:F0}";
    }

    private void RefreshButton_Click(object sender, RoutedEventArgs e)
    {
        RefreshMetrics(null);
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        _refreshTimer?.Dispose();
        Close();
    }

    protected override void OnClosed(EventArgs e)
    {
        _refreshTimer?.Dispose();
        base.OnClosed(e);
    }
}
