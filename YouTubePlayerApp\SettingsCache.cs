using System.Collections.Concurrent;
using System.Runtime.CompilerServices;

namespace YouTubePlayerApp;

/// <summary>
/// High-performance caching system for application settings to reduce property access overhead
/// </summary>
public sealed class SettingsCache : IDisposable
{
    private static readonly Lazy<SettingsCache> _instance = new(() => new SettingsCache());
    public static SettingsCache Instance => _instance.Value;

    private readonly ConcurrentDictionary<string, object> _cache;
    private readonly ConcurrentDictionary<string, DateTime> _cacheTimestamps;
    private readonly Timer _cacheCleanupTimer;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(5);
    private bool _disposed;

    // Frequently accessed settings cached as fields for ultra-fast access
    private volatile bool _blockAds;
    private volatile bool _forceVideoQuality;
    private volatile string _forcedVideoQuality = "auto";
    private volatile bool _showAdBlockStats;
    private volatile bool _lockQualityControls;
    private volatile bool _hideQualityControls;
    private volatile bool _overrideUserQualityChanges;
    
    private DateTime _lastSettingsRefresh = DateTime.MinValue;
    private readonly TimeSpan _settingsRefreshInterval = TimeSpan.FromSeconds(1);

    private SettingsCache()
    {
        _cache = new ConcurrentDictionary<string, object>();
        _cacheTimestamps = new ConcurrentDictionary<string, DateTime>();
        
        // Clean cache every minute
        _cacheCleanupTimer = new Timer(CleanExpiredCache, null, TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
        
        RefreshFrequentlyAccessedSettings();
    }

    /// <summary>
    /// Ultra-fast access to frequently used ad blocking setting
    /// </summary>
    public bool BlockAds
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _blockAds;
        }
    }

    /// <summary>
    /// Ultra-fast access to video quality forcing setting
    /// </summary>
    public bool ForceVideoQuality
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _forceVideoQuality;
        }
    }

    /// <summary>
    /// Ultra-fast access to forced video quality value
    /// </summary>
    public string ForcedVideoQuality
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _forcedVideoQuality;
        }
    }

    /// <summary>
    /// Ultra-fast access to ad block stats display setting
    /// </summary>
    public bool ShowAdBlockStats
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _showAdBlockStats;
        }
    }

    /// <summary>
    /// Ultra-fast access to quality controls locking setting
    /// </summary>
    public bool LockQualityControls
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _lockQualityControls;
        }
    }

    /// <summary>
    /// Ultra-fast access to quality controls hiding setting
    /// </summary>
    public bool HideQualityControls
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _hideQualityControls;
        }
    }

    /// <summary>
    /// Ultra-fast access to quality override setting
    /// </summary>
    public bool OverrideUserQualityChanges
    {
        get
        {
            RefreshSettingsIfNeeded();
            return _overrideUserQualityChanges;
        }
    }

    [MethodImpl(MethodImplOptions.AggressiveInlining)]
    private void RefreshSettingsIfNeeded()
    {
        var now = DateTime.UtcNow;
        if (now - _lastSettingsRefresh > _settingsRefreshInterval)
        {
            RefreshFrequentlyAccessedSettings();
            _lastSettingsRefresh = now;
        }
    }

    private void RefreshFrequentlyAccessedSettings()
    {
        _blockAds = AppSettings.BlockAds;
        _forceVideoQuality = AppSettings.ForceVideoQuality;
        _forcedVideoQuality = AppSettings.ForcedVideoQuality;
        _showAdBlockStats = AppSettings.ShowAdBlockStats;
        _lockQualityControls = AppSettings.LockQualityControls;
        _hideQualityControls = AppSettings.HideQualityControls;
        _overrideUserQualityChanges = AppSettings.OverrideUserQualityChanges;
    }

    /// <summary>
    /// Force refresh of all cached settings
    /// </summary>
    public void InvalidateCache()
    {
        _cache.Clear();
        _cacheTimestamps.Clear();
        RefreshFrequentlyAccessedSettings();
    }

    /// <summary>
    /// Get a cached value or compute and cache it
    /// </summary>
    public T GetOrAdd<T>(string key, Func<T> factory)
    {
        if (_disposed) return factory();
        
        var now = DateTime.UtcNow;
        
        // Check if cached value exists and is not expired
        if (_cache.TryGetValue(key, out var cachedValue) && 
            _cacheTimestamps.TryGetValue(key, out var timestamp) &&
            now - timestamp < _cacheExpiry)
        {
            return (T)cachedValue;
        }

        // Compute new value
        var newValue = factory();
        
        // Cache the new value
        _cache.TryAdd(key, newValue!);
        _cacheTimestamps.TryAdd(key, now);
        
        return newValue;
    }

    /// <summary>
    /// Set a cached value
    /// </summary>
    public void Set<T>(string key, T value)
    {
        if (_disposed) return;
        
        _cache.AddOrUpdate(key, value!, (k, v) => value!);
        _cacheTimestamps.AddOrUpdate(key, DateTime.UtcNow, (k, v) => DateTime.UtcNow);
    }

    /// <summary>
    /// Remove a specific key from cache
    /// </summary>
    public void Remove(string key)
    {
        if (_disposed) return;
        
        _cache.TryRemove(key, out _);
        _cacheTimestamps.TryRemove(key, out _);
    }

    private void CleanExpiredCache(object? state)
    {
        if (_disposed) return;
        
        var now = DateTime.UtcNow;
        var expiredKeys = new List<string>();
        
        foreach (var kvp in _cacheTimestamps)
        {
            if (now - kvp.Value > _cacheExpiry)
            {
                expiredKeys.Add(kvp.Key);
            }
        }
        
        foreach (var key in expiredKeys)
        {
            _cache.TryRemove(key, out _);
            _cacheTimestamps.TryRemove(key, out _);
        }
        
        if (expiredKeys.Count > 0)
        {
            System.Diagnostics.Debug.WriteLine($"SettingsCache: Cleaned {expiredKeys.Count} expired entries");
        }
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        _disposed = true;
        _cacheCleanupTimer?.Dispose();
        _cache.Clear();
        _cacheTimestamps.Clear();
    }
}
