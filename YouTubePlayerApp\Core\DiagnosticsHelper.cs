using System.Text;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Web.WebView2.Core;

namespace YouTubePlayerApp.Core;

/// <summary>
/// مساعد مشترك للوظائف التشخيصية لتجنب التكرار في الكود
/// </summary>
public static class DiagnosticsHelper
{
    /// <summary>
    /// إنشاء نافذة تشخيصية موحدة
    /// </summary>
    /// <param name="title">عنوان النافذة</param>
    /// <param name="content">المحتوى النصي</param>
    /// <param name="owner">النافذة الأب</param>
    /// <param name="width">عرض النافذة</param>
    /// <param name="height">ارتفاع النافذة</param>
    /// <returns>النافذة التشخيصية</returns>
    public static Window CreateDiagnosticsWindow(string title, string content, Window? owner = null, 
        double width = 900, double height = 700)
    {
        var diagnosticsWindow = new Window
        {
            Title = title,
            Width = width,
            Height = height,
            Owner = owner,
            WindowStartupLocation = WindowStartupLocation.CenterOwner
        };

        var scrollViewer = new ScrollViewer
        {
            VerticalScrollBarVisibility = ScrollBarVisibility.Auto,
            HorizontalScrollBarVisibility = ScrollBarVisibility.Auto,
            Padding = new Thickness(15)
        };

        var textBlock = new TextBlock
        {
            Text = content,
            FontFamily = new System.Windows.Media.FontFamily("Consolas"),
            FontSize = 11,
            TextWrapping = TextWrapping.Wrap
        };

        scrollViewer.Content = textBlock;
        diagnosticsWindow.Content = scrollViewer;

        return diagnosticsWindow;
    }

    /// <summary>
    /// عرض رسالة خطأ موحدة
    /// </summary>
    /// <param name="message">رسالة الخطأ</param>
    /// <param name="title">عنوان الرسالة</param>
    /// <param name="exception">الاستثناء إن وجد</param>
    public static void ShowErrorMessage(string message, string title = "خطأ", Exception? exception = null)
    {
        var fullMessage = exception != null 
            ? $"{message}\n\nتفاصيل الخطأ: {exception.Message}"
            : message;

        MessageBox.Show(fullMessage, title, MessageBoxButton.OK, MessageBoxImage.Error);
        
        // تسجيل الخطأ
        AppLogger.LogError($"{title}: {fullMessage}");
    }

    /// <summary>
    /// عرض رسالة معلومات موحدة
    /// </summary>
    /// <param name="message">رسالة المعلومات</param>
    /// <param name="title">عنوان الرسالة</param>
    public static void ShowInfoMessage(string message, string title = "معلومات")
    {
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Information);
        AppLogger.LogInfo($"{title}: {message}");
    }

    /// <summary>
    /// عرض رسالة تحذير موحدة
    /// </summary>
    /// <param name="message">رسالة التحذير</param>
    /// <param name="title">عنوان الرسالة</param>
    public static void ShowWarningMessage(string message, string title = "تحذير")
    {
        MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Warning);
        AppLogger.LogWarning($"{title}: {message}");
    }

    /// <summary>
    /// تنفيذ عملية تشخيصية مع معالجة الأخطاء
    /// </summary>
    /// <param name="operation">العملية المراد تنفيذها</param>
    /// <param name="operationName">اسم العملية</param>
    /// <param name="showSuccessMessage">عرض رسالة نجاح</param>
    /// <returns>نتيجة العملية</returns>
    public static async Task<T?> ExecuteDiagnosticOperationAsync<T>(
        Func<Task<T>> operation, 
        string operationName,
        bool showSuccessMessage = false) where T : class
    {
        try
        {
            AppLogger.LogInfo($"بدء تنفيذ {operationName}");
            PerformanceMonitor.Instance.StartOperation(operationName);

            var result = await operation();

            if (showSuccessMessage)
            {
                ShowInfoMessage($"تم تنفيذ {operationName} بنجاح", "نجح التنفيذ");
            }

            AppLogger.LogInfo($"تم إكمال {operationName} بنجاح");
            return result;
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"خطأ في تنفيذ {operationName}", "خطأ في التشخيص", ex);
            return null;
        }
        finally
        {
            PerformanceMonitor.Instance.EndOperation(operationName);
        }
    }

    /// <summary>
    /// تنفيذ عملية تشخيصية بدون إرجاع قيمة
    /// </summary>
    /// <param name="operation">العملية المراد تنفيذها</param>
    /// <param name="operationName">اسم العملية</param>
    /// <param name="showSuccessMessage">عرض رسالة نجاح</param>
    public static async Task ExecuteDiagnosticOperationAsync(
        Func<Task> operation, 
        string operationName,
        bool showSuccessMessage = true)
    {
        try
        {
            AppLogger.LogInfo($"بدء تنفيذ {operationName}");
            PerformanceMonitor.Instance.StartOperation(operationName);

            await operation();

            if (showSuccessMessage)
            {
                ShowInfoMessage($"تم تنفيذ {operationName} بنجاح", "نجح التنفيذ");
            }

            AppLogger.LogInfo($"تم إكمال {operationName} بنجاح");
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"خطأ في تنفيذ {operationName}", "خطأ في التشخيص", ex);
        }
        finally
        {
            PerformanceMonitor.Instance.EndOperation(operationName);
        }
    }

    /// <summary>
    /// تحليل حالة WebView2 وإرجاع تقرير مفصل
    /// </summary>
    /// <param name="webView">مثيل WebView2</param>
    /// <returns>تقرير حالة WebView2</returns>
    public static async Task<string> AnalyzeWebViewStateAsync(CoreWebView2 webView)
    {
        if (webView == null)
            return "❌ WebView2 غير مهيأ";

        var report = new StringBuilder();
        report.AppendLine("=== تحليل حالة WebView2 ===");
        report.AppendLine($"📅 وقت التحليل: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine();

        try
        {
            // معلومات أساسية
            report.AppendLine("📋 المعلومات الأساسية:");
            report.AppendLine($"   🌐 URL الحالي: {webView.Source}");
            report.AppendLine($"   📄 عنوان الصفحة: {webView.DocumentTitle}");
            report.AppendLine($"   🔄 يمكن الرجوع: {webView.CanGoBack}");
            report.AppendLine($"   ⏭️ يمكن التقدم: {webView.CanGoForward}");
            report.AppendLine();

            // إعدادات WebView2
            report.AppendLine("⚙️ الإعدادات:");
            report.AppendLine($"   📜 JavaScript مفعل: {webView.Settings.IsScriptEnabled}");
            report.AppendLine($"   🔒 كلمات المرور محفوظة: {webView.Settings.IsPasswordAutosaveEnabled}");
            report.AppendLine($"   🎯 الإشعارات مفعلة: {!webView.Settings.AreDefaultScriptDialogsEnabled}");
            report.AppendLine($"   🛠️ أدوات المطور مفعلة: {webView.Settings.AreDevToolsEnabled}");
            report.AppendLine();

            // اختبار JavaScript
            try
            {
                var jsResult = await webView.ExecuteScriptAsync("document.readyState");
                report.AppendLine($"✅ حالة JavaScript: نشط - حالة الصفحة: {jsResult}");
            }
            catch (Exception jsEx)
            {
                report.AppendLine($"❌ خطأ في JavaScript: {jsEx.Message}");
            }

            // اختبار YouTube
            if (webView.Source.Contains("youtube.com"))
            {
                report.AppendLine();
                report.AppendLine("🎥 تحليل YouTube:");
                
                try
                {
                    var playerResult = await webView.ExecuteScriptAsync(
                        "document.querySelector('video') ? 'موجود' : 'غير موجود'");
                    report.AppendLine($"   📹 مشغل الفيديو: {playerResult}");
                }
                catch
                {
                    report.AppendLine("   📹 مشغل الفيديو: غير محدد");
                }
            }

        }
        catch (Exception ex)
        {
            report.AppendLine($"❌ خطأ في التحليل: {ex.Message}");
        }

        return report.ToString();
    }

    /// <summary>
    /// تحليل أداء التطبيق وإرجاع تقرير
    /// </summary>
    /// <returns>تقرير الأداء</returns>
    public static string AnalyzePerformance()
    {
        var report = new StringBuilder();
        report.AppendLine("=== تحليل الأداء ===");
        report.AppendLine($"📅 وقت التحليل: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine();

        try
        {
            // معلومات الذاكرة
            var memoryInfo = MemoryProfiler.GetCurrentMemoryInfo();
            report.AppendLine("💾 استخدام الذاكرة:");
            report.AppendLine($"   📊 الذاكرة المستخدمة: {memoryInfo.WorkingSet / 1024 / 1024:F2} MB");
            report.AppendLine($"   📈 الذاكرة الخاصة: {memoryInfo.PrivateMemorySize / 1024 / 1024:F2} MB");
            report.AppendLine($"   🔄 الذاكرة الافتراضية: {memoryInfo.VirtualMemorySize / 1024 / 1024:F2} MB");
            report.AppendLine();

            // معلومات الأداء
            var performanceStats = PerformanceMonitor.Instance.GetCurrentStats();
            report.AppendLine("⚡ إحصائيات الأداء:");
            report.AppendLine($"   🕐 وقت التشغيل: {performanceStats.GetValueOrDefault("Uptime", "غير محدد")}");
            report.AppendLine($"   🔄 العمليات النشطة: {performanceStats.GetValueOrDefault("ActiveOperations", "0")}");
            report.AppendLine();

            // إعدادات التطبيق
            report.AppendLine("⚙️ إعدادات التطبيق:");
            report.AppendLine($"   🚫 حجب الإعلانات: {(AppSettings.BlockAds ? "مفعل" : "معطل")}");
            report.AppendLine($"   🔒 عدم التتبع: {(AppSettings.DoNotTrack ? "مفعل" : "معطل")}");
            report.AppendLine($"   📊 عرض إحصائيات حجب الإعلانات: {(AppSettings.ShowAdBlockStats ? "مفعل" : "معطل")}");

        }
        catch (Exception ex)
        {
            report.AppendLine($"❌ خطأ في تحليل الأداء: {ex.Message}");
        }

        return report.ToString();
    }
}