{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.121\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.121\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "554C963A6464808AFC3B6F0C2A570EE215610908CE452FE1A6C6151E12D5E77C"}, "default_search_provider_data": {"template_url_data": "DDD4460B4737EC2B1827C76FFF2295252972E18D0C48360078F871C3DD4E64D4"}, "edge": {"services": {"account_id": "FBB8C442EA7EBCB93D8603515871ABDCA7BE14D689F1275EAFF2831546798593", "last_username": "EA2EE9D723FD73D8793447A69FFCC5E1C303496C4A57DA0C5D65F833CE46F81C"}}, "enterprise_signin": {"policy_recovery_token": "83DDF486DD9DA212FC21763C88CADD542463DA23B5FF1A3D3877B32616B0BEC6"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "B0126CF215D883BD942487DD5E791540B4C53D2C2014B021D51BC7E63B0FE3DB", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "D20FBBEEEABD9A030A3CCAD5A466CEB5EB0C20C8443A41476F39BC6257822DA7"}, "ui": {"developer_mode": "3EE3A6341DC7D6554E83FA9CEEFDA093F7DF752EC70221104E69DD1B818701A0"}}, "google": {"services": {"last_signed_in_username": "55FECFF177D696D568A83B1A389FB05ED5A801E33AE228F5E0AFFA2886567886"}}, "homepage": "E84ED9BB5D3CBCDBA728889135C215108DBAE2AA58710242EDE0E822DFBF3EF3", "homepage_is_newtabpage": "F9B7E05E8DFA2BEB8DA3412604AFA800CA318C3562FDFFF2D0EEE6815B84EABD", "media": {"cdm": {"origin_data": "F2AC4B5A28AFB9BA8FBBCC6F7706CE42A255162A83E8575E149BD85E5D7208C9"}, "storage_id_salt": "1777B30995259AD24B883471B4AD0A6FE083E64B26AB47B824975D9CF5BEEE3F"}, "pinned_tabs": "96DD6A2E3883B8D176CBAA4052EE06125538C9B12683F53A48086A664BDF789F", "prefs": {"preference_reset_time": "F39FF8649841643B932A0318860361A53C331AE076F87A6FF0FBA7AACBF3B69B"}, "safebrowsing": {"incidents_sent": "E3C038F932FFBF14D383F91B6E4942FDB83FD0813D24B78E4E17564F01F08BC6"}, "search_provider_overrides": "8E00E6901870A43966DDFE22B0E2A849B04E6F9B9E027F1481C9DA367F2D9724", "session": {"restore_on_startup": "1402F08B854CFBB159B26F135441B6E6FE5DC6E9F0B0C1A1E3118D7F2EA5FE15", "startup_urls": "2EF6836849EDB8EE36AE0FCBF56262B6664BCBA34DC52698680A2792F0C50FF4"}}, "super_mac": "8B24DB213BFCC131C04CE04FEE549A914E72BD2B3430997B4579FD5C93DC9015"}}