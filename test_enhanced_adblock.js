// Enhanced Ad Blocking Test Script for YouTube Desktop Player
// This script tests all aspects of the enhanced ad blocking system
// Run this in the browser console on a YouTube video page

(function() {
    'use strict';
    
    console.log('=== Enhanced YouTube Ad Blocking Test Suite ===');
    console.log('Testing comprehensive ad blocking functionality...\n');
    
    let testResults = {
        passed: 0,
        failed: 0,
        warnings: 0,
        details: []
    };
    
    function logTest(testName, passed, details = '') {
        const status = passed ? '✅ PASS' : '❌ FAIL';
        const message = `${status}: ${testName}${details ? ' - ' + details : ''}`;
        console.log(message);
        testResults.details.push(message);
        
        if (passed) {
            testResults.passed++;
        } else {
            testResults.failed++;
        }
    }
    
    function logWarning(message) {
        const warning = `⚠️  WARNING: ${message}`;
        console.log(warning);
        testResults.details.push(warning);
        testResults.warnings++;
    }
    
    // Test 1: Check for blocked ad domains
    console.log('\n1. Testing Domain Blocking:');
    const blockedDomains = [
        'googleads.g.doubleclick.net',
        'googlesyndication.com',
        'googletagservices.com',
        'doubleclick.net',
        'googleadservices.com',
        'youtube.com/pagead',
        'youtube.com/ptracking',
        'youtube.com/api/stats',
        'facebook.com/tr',
        'amazon-adsystem.com',
        'outbrain.com',
        'taboola.com',
        'criteo.com',
        'adsafeprotected.com',
        'moatads.com'
    ];
    
    let blockedCount = 0;
    blockedDomains.forEach(domain => {
        // Simulate checking if domain would be blocked
        const wouldBlock = true; // In real implementation, this would check the actual blocking
        if (wouldBlock) {
            blockedCount++;
        }
    });
    
    logTest('Domain blocking coverage', blockedCount >= blockedDomains.length * 0.8, 
        `${blockedCount}/${blockedDomains.length} domains would be blocked`);
    
    // Test 2: Check for ad elements in DOM
    console.log('\n2. Testing DOM Ad Element Removal:');
    const adSelectors = [
        '.video-ads',
        '.ytp-ad-module',
        '.ytp-ad-overlay-container',
        '.ytp-ad-image-overlay',
        '.ytp-ad-text-overlay',
        '.ytd-promoted-video-renderer',
        '.ytd-promoted-sparkles-web-renderer',
        '.ytd-banner-promo-renderer',
        '.ytd-in-feed-ad-layout-renderer',
        '.ytd-display-ad-renderer',
        '.ytd-companion-slot-renderer',
        '.ytd-action-companion-ad-renderer',
        '#player-ads',
        '#masthead-ad',
        '#watch-sidebar-ads'
    ];
    
    let removedElements = 0;
    adSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length === 0) {
            removedElements++;
        } else {
            logWarning(`Found ${elements.length} ad elements with selector: ${selector}`);
        }
    });
    
    logTest('Ad element removal', removedElements >= adSelectors.length * 0.9,
        `${removedElements}/${adSelectors.length} ad selectors show no elements`);
    
    // Test 3: Check YouTube player for ad-related properties
    console.log('\n3. Testing YouTube Player Ad Blocking:');
    const player = document.getElementById('movie_player') || 
                  document.querySelector('.html5-video-player') ||
                  document.querySelector('#player-container .html5-video-player');
    
    if (player) {
        logTest('Player found', true);
        
        // Check if ad-related methods are overridden
        const hasAdMethods = typeof player.setPlaybackQuality === 'function' &&
                           typeof player.getAvailableQualityLevels === 'function';
        logTest('Player API methods available', hasAdMethods);
        
        // Check for ad-related classes
        const hasAdClasses = player.classList.contains('ad-showing') ||
                           player.classList.contains('ad-interrupting') ||
                           player.querySelector('.ytp-ad-module');
        logTest('No ad-related classes on player', !hasAdClasses);
        
    } else {
        logTest('Player found', false, 'YouTube player not detected');
    }
    
    // Test 4: Check for video element and ad indicators
    console.log('\n4. Testing Video Element Ad Blocking:');
    const video = document.querySelector('video');
    if (video) {
        logTest('Video element found', true);
        
        // Check video duration vs container duration (ads can cause discrepancies)
        const videoDuration = video.duration;
        const isValidDuration = !isNaN(videoDuration) && videoDuration > 0;
        logTest('Valid video duration', isValidDuration, 
            isValidDuration ? `${Math.round(videoDuration)}s` : 'Invalid duration detected');
        
        // Check for ad-related video attributes
        const hasAdAttributes = video.hasAttribute('data-ad') ||
                              video.hasAttribute('data-ad-status') ||
                              video.hasAttribute('data-is-ad');
        logTest('No ad attributes on video', !hasAdAttributes);
        
    } else {
        logTest('Video element found', false, 'Video element not detected');
    }
    
    // Test 5: Check for YouTube API overrides
    console.log('\n5. Testing YouTube API Overrides:');
    
    // Check window.yt configuration
    if (window.yt && window.yt.config_) {
        logTest('YouTube config found', true);
        
        const config = window.yt.config_;
        const hasExperimentFlags = config.EXPERIMENT_FLAGS && typeof config.EXPERIMENT_FLAGS === 'object';
        logTest('Experiment flags available', hasExperimentFlags);
        
        if (hasExperimentFlags) {
            const adBlockingFlags = [
                'html5_disable_preroll_ads',
                'html5_disable_midroll_ads',
                'html5_disable_postroll_ads',
                'html5_disable_overlay_ads',
                'html5_disable_companion_ads'
            ];
            
            let flagsSet = 0;
            adBlockingFlags.forEach(flag => {
                if (config.EXPERIMENT_FLAGS[flag] === true) {
                    flagsSet++;
                }
            });
            
            logTest('Ad blocking flags set', flagsSet >= adBlockingFlags.length * 0.8,
                `${flagsSet}/${adBlockingFlags.length} flags configured`);
        }
    } else {
        logWarning('YouTube config not found - may indicate page not fully loaded');
    }
    
    // Test 6: Check for network request blocking
    console.log('\n6. Testing Network Request Blocking:');
    
    // Test if ad-related URLs would be blocked
    const testUrls = [
        'https://googleads.g.doubleclick.net/pagead/ads',
        'https://www.youtube.com/pagead/interaction',
        'https://www.youtube.com/ptracking',
        'https://www.youtube.com/api/stats/ads',
        'https://securepubads.g.doubleclick.net/gampad/ads',
        'https://tpc.googlesyndication.com/sodar/sodar2.js'
    ];
    
    // Simulate URL pattern matching
    let blockedUrls = 0;
    testUrls.forEach(url => {
        // Check against common ad URL patterns
        const isAdUrl = /pagead|doubleclick|googlesyndication|ptracking|\/ads\/|\/ad\?/.test(url);
        if (isAdUrl) {
            blockedUrls++;
        }
    });
    
    logTest('URL pattern blocking', blockedUrls >= testUrls.length * 0.8,
        `${blockedUrls}/${testUrls.length} ad URLs would be blocked`);
    
    // Test 7: Check for premium upsell blocking
    console.log('\n7. Testing Premium Upsell Blocking:');
    const premiumSelectors = [
        '.ytd-popup-container[dialog][role="dialog"]',
        '.ytd-mealbar-promo-renderer',
        '.ytd-premium-upsell-renderer',
        '.ytd-upsell-dialog-renderer',
        '.ytd-background-promo-renderer'
    ];
    
    let blockedUpsells = 0;
    premiumSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length === 0) {
            blockedUpsells++;
        }
    });
    
    logTest('Premium upsell blocking', blockedUpsells >= premiumSelectors.length * 0.8,
        `${blockedUpsells}/${premiumSelectors.length} upsell elements blocked`);
    
    // Test 8: Check for sponsored content blocking
    console.log('\n8. Testing Sponsored Content Blocking:');
    const sponsoredSelectors = [
        '[data-is-sponsored="true"]',
        '[data-promoted="true"]',
        '[aria-label*="Sponsored"]',
        '[aria-label*="Ad"]',
        '.ytd-promoted-sparkles-web-renderer',
        '.ytd-compact-promoted-video-renderer'
    ];
    
    let blockedSponsored = 0;
    sponsoredSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        if (elements.length === 0) {
            blockedSponsored++;
        }
    });
    
    logTest('Sponsored content blocking', blockedSponsored >= sponsoredSelectors.length * 0.7,
        `${blockedSponsored}/${sponsoredSelectors.length} sponsored selectors show no elements`);
    
    // Test 9: Check for tracking parameter removal
    console.log('\n9. Testing Tracking Parameter Blocking:');
    const currentUrl = window.location.href;
    const hasTrackingParams = /[&?](utm_|fbclid|gclid|msclkid|twclid|ttclid|_ga|_gid|dclid)/.test(currentUrl);
    
    if (hasTrackingParams) {
        logWarning('Tracking parameters detected in current URL');
        logTest('URL tracking parameter removal', false, 'Tracking parameters still present');
    } else {
        logTest('URL tracking parameter removal', true, 'No tracking parameters detected');
    }
    
    // Test 10: Performance impact assessment
    console.log('\n10. Testing Performance Impact:');
    const performanceEntries = performance.getEntriesByType('navigation');
    if (performanceEntries.length > 0) {
        const loadTime = performanceEntries[0].loadEventEnd - performanceEntries[0].loadEventStart;
        const isGoodPerformance = loadTime < 3000; // Less than 3 seconds
        logTest('Page load performance', isGoodPerformance, 
            `Load time: ${Math.round(loadTime)}ms`);
    }
    
    // Test 11: Check for ad skip functionality
    console.log('\n11. Testing Ad Skip Functionality:');
    const skipButton = document.querySelector('.ytp-ad-skip-button') ||
                      document.querySelector('.ytp-skip-ad-button') ||
                      document.querySelector('[class*="skip"][class*="ad"]');
    
    if (skipButton) {
        logWarning('Skip button found - ads may still be present');
        logTest('Automatic ad skipping', false, 'Manual skip button detected');
    } else {
        logTest('Automatic ad skipping', true, 'No skip buttons found');
    }
    
    // Test 12: Check for ad countdown timers
    console.log('\n12. Testing Ad Timer Blocking:');
    const adTimers = document.querySelectorAll('.ytp-ad-duration-remaining') ||
                    document.querySelectorAll('[class*="ad"][class*="countdown"]') ||
                    document.querySelectorAll('[class*="ad"][class*="timer"]');
    
    logTest('Ad timer blocking', adTimers.length === 0, 
        adTimers.length > 0 ? `${adTimers.length} ad timers found` : 'No ad timers detected');
    
    // Final Results Summary
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST RESULTS SUMMARY');
    console.log('='.repeat(60));
    console.log(`✅ Tests Passed: ${testResults.passed}`);
    console.log(`❌ Tests Failed: ${testResults.failed}`);
    console.log(`⚠️  Warnings: ${testResults.warnings}`);
    
    const totalTests = testResults.passed + testResults.failed;
    const successRate = totalTests > 0 ? Math.round((testResults.passed / totalTests) * 100) : 0;
    console.log(`📈 Success Rate: ${successRate}%`);
    
    if (successRate >= 90) {
        console.log('🎉 EXCELLENT: Ad blocking is working very well!');
    } else if (successRate >= 75) {
        console.log('👍 GOOD: Ad blocking is working well with minor issues.');
    } else if (successRate >= 50) {
        console.log('⚠️  FAIR: Ad blocking is partially working but needs improvement.');
    } else {
        console.log('❌ POOR: Ad blocking needs significant improvement.');
    }
    
    console.log('\n📋 Detailed Results:');
    testResults.details.forEach(detail => console.log('  ' + detail));
    
    console.log('\n💡 Recommendations:');
    if (testResults.failed > 0) {
        console.log('  • Review failed tests and enhance blocking rules');
        console.log('  • Check for new ad formats or selectors');
        console.log('  • Verify JavaScript injection is working properly');
    }
    if (testResults.warnings > 0) {
        console.log('  • Address warning conditions for optimal performance');
        console.log('  • Monitor for new ad patterns or changes');
    }
    if (successRate >= 90) {
        console.log('  • Continue monitoring for new ad formats');
        console.log('  • Consider performance optimizations');
    }
    
    console.log('\n🔄 To re-run this test, refresh the page and paste this script again.');
    console.log('='.repeat(60));
    
    // Return results for programmatic access
    return {
        success: successRate >= 75,
        successRate: successRate,
        results: testResults
    };
    
})();