<Window x:Class="YouTubePlayerApp.MemoryProfilerWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Memory Profiler - YouTube Player" 
        Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        ResizeMode="CanResize">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Control Panel -->
        <Border Grid.Row="0" Background="#F0F0F0" Padding="10" BorderBrush="#CCCCCC" BorderThickness="0,0,0,1">
            <StackPanel Orientation="Horizontal">
                <Button Name="StartProfilingButton" Content="Start Profiling" Width="120" Height="30" Margin="0,0,10,0" Click="StartProfiling_Click"/>
                <Button Name="StopProfilingButton" Content="Stop Profiling" Width="120" Height="30" Margin="0,0,10,0" Click="StopProfiling_Click" IsEnabled="False"/>
                <Button Name="TakeSnapshotButton" Content="Take Snapshot" Width="120" Height="30" Margin="0,0,10,0" Click="TakeSnapshot_Click"/>
                <Button Name="ForceGCButton" Content="Force GC" Width="100" Height="30" Margin="0,0,10,0" Click="ForceGC_Click"/>
                <Button Name="ClearDataButton" Content="Clear Data" Width="100" Height="30" Margin="0,0,10,0" Click="ClearData_Click"/>
                <Button Name="RefreshButton" Content="Refresh" Width="80" Height="30" Margin="0,0,10,0" Click="Refresh_Click"/>
            </StackPanel>
        </Border>
        
        <!-- Main Content -->
        <TabControl Grid.Row="1" Margin="10">
            
            <!-- Current Memory State Tab -->
            <TabItem Header="Current State">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Quick Stats -->
                    <Border Grid.Row="0" Background="#E8F4FD" Padding="15" Margin="0,0,0,10" CornerRadius="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Managed Memory" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="ManagedMemoryText" Text="0.0 MB" FontSize="16" Foreground="Blue"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Working Set" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="WorkingSetText" Text="0.0 MB" FontSize="16" Foreground="Green"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="Private Memory" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="PrivateMemoryText" Text="0.0 MB" FontSize="16" Foreground="Orange"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="3">
                                <TextBlock Text="GC Collections" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="GCCollectionsText" Text="0" FontSize="16" Foreground="Red"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- Detailed Breakdown -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <GroupBox Header="Memory Breakdown" Padding="10" Margin="0,0,0,10">
                                <Grid Name="MemoryBreakdownGrid">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="200"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <!-- Content will be populated programmatically -->
                                </Grid>
                            </GroupBox>
                            
                            <GroupBox Header="Performance Metrics" Padding="10" Margin="0,0,0,10">
                                <Grid Name="PerformanceMetricsGrid">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="200"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>
                                    <!-- Content will be populated programmatically -->
                                </Grid>
                            </GroupBox>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- Memory Trends Tab -->
            <TabItem Header="Trends &amp; Analysis">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Trends Summary -->
                    <Border Grid.Row="0" Background="#FFF8E1" Padding="15" Margin="0,0,0,10" CornerRadius="5">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            
                            <StackPanel Grid.Column="0">
                                <TextBlock Text="Average Memory" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="AverageMemoryText" Text="0.0 MB" FontSize="16"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="1">
                                <TextBlock Text="Peak Memory" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="PeakMemoryText" Text="0.0 MB" FontSize="16"/>
                            </StackPanel>
                            
                            <StackPanel Grid.Column="2">
                                <TextBlock Text="Growth Rate" FontWeight="Bold" FontSize="12"/>
                                <TextBlock Name="GrowthRateText" Text="0.0 MB/min" FontSize="16"/>
                            </StackPanel>
                        </Grid>
                    </Border>
                    
                    <!-- Detailed Analysis -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel>
                            <GroupBox Header="Memory Leak Analysis" Padding="10" Margin="0,0,0,10">
                                <StackPanel Name="LeakAnalysisPanel">
                                    <!-- Content will be populated programmatically -->
                                </StackPanel>
                            </GroupBox>
                            
                            <GroupBox Header="Memory History" Padding="10" Margin="0,0,0,10">
                                <StackPanel Name="MemoryHistoryPanel">
                                    <!-- Content will be populated programmatically -->
                                </StackPanel>
                            </GroupBox>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
            <!-- Snapshots Tab -->
            <TabItem Header="Snapshots">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Snapshot Controls -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <TextBox Name="SnapshotLabelTextBox" Width="200" Height="25" Margin="0,0,10,0" 
                                 Text="Snapshot" VerticalContentAlignment="Center"/>
                        <Button Name="TakeSnapshotWithLabelButton" Content="Take Labeled Snapshot" Width="150" Height="25" 
                                Click="TakeSnapshotWithLabel_Click"/>
                        <ComboBox Name="CompareFromComboBox" Width="120" Height="25" Margin="20,0,10,0"/>
                        <TextBlock Text="vs" VerticalAlignment="Center" Margin="0,0,10,0"/>
                        <ComboBox Name="CompareToComboBox" Width="120" Height="25" Margin="0,0,10,0"/>
                        <Button Name="CompareSnapshotsButton" Content="Compare" Width="80" Height="25" 
                                Click="CompareSnapshots_Click"/>
                    </StackPanel>
                    
                    <!-- Snapshots List and Comparison -->
                    <Grid Grid.Row="1">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="300"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- Snapshots List -->
                        <GroupBox Grid.Column="0" Header="Snapshots" Padding="5" Margin="0,0,10,0">
                            <ListBox Name="SnapshotsListBox" SelectionChanged="SnapshotsListBox_SelectionChanged">
                                <ListBox.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel>
                                            <TextBlock Text="{Binding Label}" FontWeight="Bold"/>
                                            <TextBlock Text="{Binding Timestamp, StringFormat='yyyy-MM-dd HH:mm:ss'}" FontSize="10" Foreground="Gray"/>
                                            <TextBlock Text="{Binding ManagedMemoryMB, StringFormat='Managed: {0:F2} MB'}" FontSize="10"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ListBox.ItemTemplate>
                            </ListBox>
                        </GroupBox>
                        
                        <!-- Snapshot Details / Comparison -->
                        <GroupBox Grid.Column="1" Header="Details" Padding="10">
                            <ScrollViewer VerticalScrollBarVisibility="Auto">
                                <StackPanel Name="SnapshotDetailsPanel">
                                    <TextBlock Text="Select a snapshot to view details or compare two snapshots" 
                                             HorizontalAlignment="Center" VerticalAlignment="Center" 
                                             Foreground="Gray" FontStyle="Italic"/>
                                </StackPanel>
                            </ScrollViewer>
                        </GroupBox>
                    </Grid>
                </Grid>
            </TabItem>
            
            <!-- GC Analysis Tab -->
            <TabItem Header="Garbage Collection">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- GC Controls -->
                    <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                        <Button Name="ForceGCWithAnalysisButton" Content="Force GC &amp; Analyze" Width="150" Height="30" 
                                Click="ForceGCWithAnalysis_Click"/>
                        <TextBlock Text="Last GC Analysis:" VerticalAlignment="Center" Margin="20,0,10,0"/>
                        <TextBlock Name="LastGCAnalysisText" Text="None" VerticalAlignment="Center" FontWeight="Bold"/>
                    </StackPanel>
                    
                    <!-- GC Analysis Results -->
                    <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                        <StackPanel Name="GCAnalysisPanel">
                            <TextBlock Text="Click 'Force GC &amp; Analyze' to see garbage collection effectiveness" 
                                     HorizontalAlignment="Center" VerticalAlignment="Center" 
                                     Foreground="Gray" FontStyle="Italic"/>
                        </StackPanel>
                    </ScrollViewer>
                </Grid>
            </TabItem>
            
        </TabControl>
        
        <!-- Status Bar -->
        <Border Grid.Row="2" Background="#F0F0F0" Padding="10" BorderBrush="#CCCCCC" BorderThickness="0,1,0,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <TextBlock Name="StatusText" Text="Ready" VerticalAlignment="Center"/>
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Profiling: " VerticalAlignment="Center"/>
                    <Ellipse Name="ProfilingIndicator" Width="10" Height="10" Fill="Red" Margin="5,0"/>
                    <TextBlock Name="ProfilingStatusText" Text="Stopped" VerticalAlignment="Center" Margin="5,0"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
