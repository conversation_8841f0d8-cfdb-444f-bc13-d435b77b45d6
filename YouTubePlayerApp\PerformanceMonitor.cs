using System.Diagnostics;
using System.Runtime;

namespace YouTubePlayerApp;

/// <summary>
/// Performance and memory monitoring system for the YouTube Desktop Player
/// </summary>
public sealed class PerformanceMonitor : IDisposable
{
    private static readonly Lazy<PerformanceMonitor> _instance = new(() => new PerformanceMonitor());
    public static PerformanceMonitor Instance => _instance.Value;

    private readonly Dictionary<string, PerformanceCounter> _performanceCounters;
    private readonly Dictionary<string, Stopwatch> _operationTimers;
    private readonly Dictionary<string, long> _memoryBaselines;
    private readonly object _lockObject = new();
    private bool _disposed;

    // Performance metrics
    public long InitialMemoryUsage { get; private set; }
    public long CurrentMemoryUsage => GC.GetTotalMemory(false);
    public long PeakMemoryUsage { get; private set; }
    public TimeSpan ApplicationUptime => DateTime.Now - _applicationStartTime;
    
    private readonly DateTime _applicationStartTime;
    private readonly Timer _memoryMonitorTimer;

    private PerformanceMonitor()
    {
        _performanceCounters = new Dictionary<string, PerformanceCounter>();
        _operationTimers = new Dictionary<string, Stopwatch>();
        _memoryBaselines = new Dictionary<string, long>();
        _applicationStartTime = DateTime.Now;
        
        InitialMemoryUsage = CurrentMemoryUsage;
        PeakMemoryUsage = InitialMemoryUsage;
        
        // Monitor memory usage every 30 seconds
        _memoryMonitorTimer = new Timer(UpdateMemoryMetrics, null, TimeSpan.Zero, TimeSpan.FromSeconds(30));
        
        InitializePerformanceCounters();
    }

    private void InitializePerformanceCounters()
    {
        try
        {
            var processName = Process.GetCurrentProcess().ProcessName;
            
            // CPU usage counter
            _performanceCounters["CPU"] = new PerformanceCounter("Process", "% Processor Time", processName);
            
            // Memory counters
            _performanceCounters["WorkingSet"] = new PerformanceCounter("Process", "Working Set", processName);
            _performanceCounters["PrivateBytes"] = new PerformanceCounter("Process", "Private Bytes", processName);
            
            // .NET memory counters
            _performanceCounters["Gen0Collections"] = new PerformanceCounter(".NET CLR Memory", "# Gen 0 Collections", processName);
            _performanceCounters["Gen1Collections"] = new PerformanceCounter(".NET CLR Memory", "# Gen 1 Collections", processName);
            _performanceCounters["Gen2Collections"] = new PerformanceCounter(".NET CLR Memory", "# Gen 2 Collections", processName);
        }
        catch (Exception ex)
        {
            Debug.WriteLine($"Failed to initialize performance counters: {ex.Message}");
        }
    }

    public void StartOperation(string operationName)
    {
        lock (_lockObject)
        {
            if (_disposed) return;
            
            if (!_operationTimers.ContainsKey(operationName))
            {
                _operationTimers[operationName] = new Stopwatch();
            }
            
            _operationTimers[operationName].Restart();
            _memoryBaselines[operationName] = CurrentMemoryUsage;
        }
    }

    public void EndOperation(string operationName)
    {
        lock (_lockObject)
        {
            if (_disposed || !_operationTimers.ContainsKey(operationName)) return;
            
            _operationTimers[operationName].Stop();
            
            var duration = _operationTimers[operationName].ElapsedMilliseconds;
            var memoryDelta = CurrentMemoryUsage - _memoryBaselines[operationName];
            
            LogOperationMetrics(operationName, duration, memoryDelta);
        }
    }

    public T MeasureOperation<T>(string operationName, Func<T> operation)
    {
        StartOperation(operationName);
        try
        {
            return operation();
        }
        finally
        {
            EndOperation(operationName);
        }
    }

    public void MeasureOperation(string operationName, Action operation)
    {
        StartOperation(operationName);
        try
        {
            operation();
        }
        finally
        {
            EndOperation(operationName);
        }
    }

    public void ForceGarbageCollection()
    {
        var beforeMemory = CurrentMemoryUsage;
        
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        var afterMemory = CurrentMemoryUsage;
        var freedMemory = beforeMemory - afterMemory;
        
        Debug.WriteLine($"Garbage Collection: Freed {freedMemory / 1024 / 1024:F2} MB");
    }

    public PerformanceMetrics GetCurrentMetrics()
    {
        lock (_lockObject)
        {
            if (_disposed) return new PerformanceMetrics();
            
            return new PerformanceMetrics
            {
                CurrentMemoryMB = CurrentMemoryUsage / 1024.0 / 1024.0,
                PeakMemoryMB = PeakMemoryUsage / 1024.0 / 1024.0,
                WorkingSetMB = GetCounterValue("WorkingSet") / 1024.0 / 1024.0,
                PrivateBytesMB = GetCounterValue("PrivateBytes") / 1024.0 / 1024.0,
                CpuUsagePercent = GetCounterValue("CPU"),
                Gen0Collections = (int)GetCounterValue("Gen0Collections"),
                Gen1Collections = (int)GetCounterValue("Gen1Collections"),
                Gen2Collections = (int)GetCounterValue("Gen2Collections"),
                UptimeMinutes = ApplicationUptime.TotalMinutes
            };
        }
    }

    private float GetCounterValue(string counterName)
    {
        try
        {
            return _performanceCounters.ContainsKey(counterName) 
                ? _performanceCounters[counterName].NextValue() 
                : 0f;
        }
        catch
        {
            return 0f;
        }
    }

    private void UpdateMemoryMetrics(object? state)
    {
        if (_disposed) return;
        
        var currentMemory = CurrentMemoryUsage;
        if (currentMemory > PeakMemoryUsage)
        {
            PeakMemoryUsage = currentMemory;
        }
        
        // Optional: Force GC if memory usage is high
        if (currentMemory > InitialMemoryUsage * 3) // 3x initial memory
        {
            ForceGarbageCollection();
        }
    }

    private void LogOperationMetrics(string operationName, long durationMs, long memoryDeltaBytes)
    {
        if (durationMs > 100 || Math.Abs(memoryDeltaBytes) > 1024 * 1024) // Log if > 100ms or > 1MB memory change
        {
            Debug.WriteLine($"Operation '{operationName}': {durationMs}ms, Memory Δ: {memoryDeltaBytes / 1024:F0} KB");
        }
    }

    public void LogCurrentState(string context = "")
    {
        var metrics = GetCurrentMetrics();
        Debug.WriteLine($"Performance State {context}:");
        Debug.WriteLine($"  Memory: {metrics.CurrentMemoryMB:F2} MB (Peak: {metrics.PeakMemoryMB:F2} MB)");
        Debug.WriteLine($"  Working Set: {metrics.WorkingSetMB:F2} MB");
        Debug.WriteLine($"  CPU: {metrics.CpuUsagePercent:F1}%");
        Debug.WriteLine($"  GC: Gen0={metrics.Gen0Collections}, Gen1={metrics.Gen1Collections}, Gen2={metrics.Gen2Collections}");
        Debug.WriteLine($"  Uptime: {metrics.UptimeMinutes:F1} minutes");
    }

    public void Dispose()
    {
        if (_disposed) return;
        
        lock (_lockObject)
        {
            _disposed = true;
            
            _memoryMonitorTimer?.Dispose();
            
            foreach (var counter in _performanceCounters.Values)
            {
                counter?.Dispose();
            }
            _performanceCounters.Clear();
            
            _operationTimers.Clear();
            _memoryBaselines.Clear();
        }
    }
}

/// <summary>
/// Performance metrics data structure
/// </summary>
public class PerformanceMetrics
{
    public double CurrentMemoryMB { get; set; }
    public double PeakMemoryMB { get; set; }
    public double WorkingSetMB { get; set; }
    public double PrivateBytesMB { get; set; }
    public float CpuUsagePercent { get; set; }
    public int Gen0Collections { get; set; }
    public int Gen1Collections { get; set; }
    public int Gen2Collections { get; set; }
    public double UptimeMinutes { get; set; }
}
